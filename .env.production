# Production Environment Configuration for ISMS
# This file should be customized for your production deployment
# DO NOT commit this file with real secrets to version control

# Environment mode
ENV_MODE=production

# API settings
PROJECT_NAME="Integrated Supermarket Management System (ISMS)"
VERSION=0.1.0
DESCRIPTION="Backend API for the Integrated Supermarket Management System"
API_V1_STR=/api/v1

# Database settings - External PostgreSQL (Required for production)
# Replace these with your actual PostgreSQL connection details
POSTGRES_SERVER=your-postgres-server.com
POSTGRES_USER=your_postgres_user
POSTGRES_PASSWORD=your_secure_postgres_password
POSTGRES_DB=isms_production

# Security settings (CRITICAL: Change these in production)
SECRET_KEY=your-super-secure-secret-key-change-this-in-production-min-32-chars
ACCESS_TOKEN_EXPIRE_MINUTES=11520

# CORS settings for production
BACKEND_CORS_ORIGINS=["https://api.isms.helevon.org","https://isms.helevon.org","https://app.isms.helevon.org"]

# Logging settings
LOG_LEVEL=INFO

# Performance settings
WORKERS=4
MAX_CONNECTIONS=100
POOL_SIZE=20
MAX_OVERFLOW=30

# Security headers
SECURE_HEADERS=true
HTTPS_ONLY=true

# Rate limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Monitoring
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true
