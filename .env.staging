# Staging Environment Configuration for ISMS
# This file should be customized for your staging deployment
# DO NOT commit this file with real secrets to version control

# Environment mode
ENV_MODE=production

# API settings
PROJECT_NAME="ISMS - Staging"
VERSION=0.1.0
DESCRIPTION="Backend API for the Integrated Supermarket Management System - Staging Environment"
API_V1_STR=/api/v1

# Database settings - External PostgreSQL (Required for staging)
# Replace these with your actual staging PostgreSQL connection details
POSTGRES_SERVER=your-staging-postgres-server.com
POSTGRES_USER=your_staging_postgres_user
POSTGRES_PASSWORD=your_staging_postgres_password
POSTGRES_DB=isms_staging

# Security settings (Use different keys from production)
SECRET_KEY=your-staging-secret-key-different-from-production-min-32-chars
ACCESS_TOKEN_EXPIRE_MINUTES=11520

# CORS settings for staging
BACKEND_CORS_ORIGINS=["https://staging-api.isms.helevon.org","https://staging.isms.helevon.org","https://staging-app.isms.helevon.org"]

# Logging settings (More verbose for staging)
LOG_LEVEL=DEBUG

# Performance settings (Lower for staging)
WORKERS=2
MAX_CONNECTIONS=50
POOL_SIZE=10
MAX_OVERFLOW=15

# Security headers
SECURE_HEADERS=true
HTTPS_ONLY=true

# Rate limiting (More lenient for testing)
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=200
RATE_LIMIT_WINDOW=60

# Monitoring
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true

# Staging specific settings
ENABLE_DEBUG_ENDPOINTS=true
ALLOW_TEST_DATA=true
