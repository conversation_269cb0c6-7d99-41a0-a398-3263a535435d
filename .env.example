# Environment mode
ENV_MODE=development

# API settings
PROJECT_NAME="Integrated Supermarket Management System (ISMS)"
VERSION=0.1.0
DESCRIPTION="Backend API for the Integrated Supermarket Management System"

# Database settings
SQLITE_DATABASE_URI=sqlite+aiosqlite:///./isms.db

# PostgreSQL settings (for production)
# POSTGRES_SERVER=db
# POSTGRES_USER=postgres
# POSTGRES_PASSWORD=postgres
# POSTGRES_DB=isms

# Security settings
SECRET_KEY=your-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=11520

# CORS settings
BACKEND_CORS_ORIGINS='["http://localhost:3000","http://localhost:8000"]'

# Logging settings
LOG_LEVEL=INFO
