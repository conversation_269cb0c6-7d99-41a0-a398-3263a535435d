{"data_mtime": **********, "dep_lines": [23, 12, 17, 18, 7, 15, 26, 3, 5, 6, 8, 9, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 25, 5, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic_settings.sources.providers.env", "pydantic._internal._typing_extra", "pydantic_settings.sources.types", "pydantic_settings.sources.utils", "collections.abc", "typing_inspection.introspection", "pydantic_settings.main", "__future__", "os", "warnings", "pathlib", "typing", "dotenv", "builtins", "_frozen_importlib", "abc", "pydantic", "pydantic._internal", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "pydantic.main", "pydantic_settings.sources.base", "re"], "hash": "08ca2cbb663224bdd2dc0e0b329a80b279a07bde", "id": "pydantic_settings.sources.providers.dotenv", "ignore_all": true, "interface_hash": "946ae37c4f7ce434be226d88b13b5b8220fec39f", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/pydantic_settings/sources/providers/dotenv.py", "plugin_data": null, "size": 5888, "suppressed": [], "version_id": "1.16.1"}