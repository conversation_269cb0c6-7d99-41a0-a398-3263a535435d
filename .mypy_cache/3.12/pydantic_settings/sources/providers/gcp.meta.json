{"data_mtime": **********, "dep_lines": [7, 3, 14, 1, 4, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 11, 12, 10], "dep_prios": [5, 5, 25, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 25, 25, 25], "dependencies": ["pydantic_settings.sources.providers.env", "collections.abc", "pydantic_settings.main", "__future__", "functools", "typing", "builtins", "_collections_abc", "_frozen_importlib", "abc", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic_settings.sources.base", "types"], "hash": "ab5d8cfd77b484cbc26900f5b69d5f0aafd3b317", "id": "pydantic_settings.sources.providers.gcp", "ignore_all": true, "interface_hash": "31d8754e0512c0eaf25bd36bd40eaa93f065b994", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/pydantic_settings/sources/providers/gcp.py", "plugin_data": null, "size": 5223, "suppressed": ["google.auth.credentials", "google.cloud.secretmanager", "google.auth"], "version_id": "1.16.1"}