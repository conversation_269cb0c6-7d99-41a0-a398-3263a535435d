{".class": "MypyFile", "_fullname": "pydantic_settings.sources.base", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef", "module_public": false}, "AliasChoices": {".class": "SymbolTableNode", "cross_ref": "pydantic.aliases.AliasChoices", "kind": "Gdef", "module_public": false}, "AliasPath": {".class": "SymbolTableNode", "cross_ref": "pydantic.aliases.AliasPath", "kind": "Gdef", "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef", "module_public": false}, "BaseSettings": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.main.BaseSettings", "kind": "Gdef", "module_public": false}, "ConfigFileSourceMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_read_file", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_settings.sources.base.ConfigFileSourceMixin", "name": "ConfigFileSourceMixin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "pydantic_settings.sources.base.ConfigFileSourceMixin", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic_settings.sources.base", "mro": ["pydantic_settings.sources.base.ConfigFileSourceMixin", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_read_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "pydantic_settings.sources.base.ConfigFileSourceMixin._read_file", "name": "_read_file", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["pydantic_settings.sources.base.ConfigFileSourceMixin", "pathlib.Path"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_read_file of ConfigFileSourceMixin", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pydantic_settings.sources.base.ConfigFileSourceMixin._read_file", "name": "_read_file", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["pydantic_settings.sources.base.ConfigFileSourceMixin", "pathlib.Path"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_read_file of ConfigFileSourceMixin", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_read_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "files"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic_settings.sources.base.ConfigFileSourceMixin._read_files", "name": "_read_files", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "files"], "arg_types": ["pydantic_settings.sources.base.ConfigFileSourceMixin", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_settings.sources.types.PathType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_read_files of ConfigFileSourceMixin", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.base.ConfigFileSourceMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic_settings.sources.base.ConfigFileSourceMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DefaultSettingsSource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic_settings.sources.base.PydanticBaseSettingsSource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_settings.sources.base.DefaultSettingsSource", "name": "DefaultSettingsSource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_settings.sources.base.DefaultSettingsSource", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic_settings.sources.base", "mro": ["pydantic_settings.sources.base.DefaultSettingsSource", "pydantic_settings.sources.base.PydanticBaseSettingsSource", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic_settings.sources.base.DefaultSettingsSource.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_settings.sources.base.DefaultSettingsSource"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of DefaultSettingsSource", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "settings_cls", "nested_model_default_partial_update"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic_settings.sources.base.DefaultSettingsSource.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "settings_cls", "nested_model_default_partial_update"], "arg_types": ["pydantic_settings.sources.base.DefaultSettingsSource", {".class": "TypeType", "item": "pydantic_settings.main.BaseSettings"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of DefaultSettingsSource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic_settings.sources.base.DefaultSettingsSource.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pydantic_settings.sources.base.DefaultSettingsSource"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of DefaultSettingsSource", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "defaults": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic_settings.sources.base.DefaultSettingsSource.defaults", "name": "defaults", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "get_field_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "field", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic_settings.sources.base.DefaultSettingsSource.get_field_value", "name": "get_field_value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "field", "field_name"], "arg_types": ["pydantic_settings.sources.base.DefaultSettingsSource", "pydantic.fields.FieldInfo", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_field_value of DefaultSettingsSource", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "nested_model_default_partial_update": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.base.DefaultSettingsSource.nested_model_default_partial_update", "name": "nested_model_default_partial_update", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.base.DefaultSettingsSource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic_settings.sources.base.DefaultSettingsSource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EnvNoneType": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.sources.types.EnvNoneType", "kind": "Gdef", "module_public": false}, "FieldInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.FieldInfo", "kind": "Gdef", "module_public": false}, "ForceDecode": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.sources.types.ForceDecode", "kind": "Gdef", "module_public": false}, "InitSettingsSource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic_settings.sources.base.PydanticBaseSettingsSource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_settings.sources.base.InitSettingsSource", "name": "InitSettingsSource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_settings.sources.base.InitSettingsSource", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic_settings.sources.base", "mro": ["pydantic_settings.sources.base.InitSettingsSource", "pydantic_settings.sources.base.PydanticBaseSettingsSource", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic_settings.sources.base.InitSettingsSource.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_settings.sources.base.InitSettingsSource"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of InitSettingsSource", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "settings_cls", "init_kwargs", "nested_model_default_partial_update"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic_settings.sources.base.InitSettingsSource.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "settings_cls", "init_kwargs", "nested_model_default_partial_update"], "arg_types": ["pydantic_settings.sources.base.InitSettingsSource", {".class": "TypeType", "item": "pydantic_settings.main.BaseSettings"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of InitSettingsSource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic_settings.sources.base.InitSettingsSource.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pydantic_settings.sources.base.InitSettingsSource"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of InitSettingsSource", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_field_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "field", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic_settings.sources.base.InitSettingsSource.get_field_value", "name": "get_field_value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "field", "field_name"], "arg_types": ["pydantic_settings.sources.base.InitSettingsSource", "pydantic.fields.FieldInfo", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_field_value of InitSettingsSource", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "init_kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.base.InitSettingsSource.init_kwargs", "name": "init_kwargs", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "nested_model_default_partial_update": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.base.InitSettingsSource.nested_model_default_partial_update", "name": "nested_model_default_partial_update", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.base.InitSettingsSource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic_settings.sources.base.InitSettingsSource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoDecode": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.sources.types.NoDecode", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef", "module_public": false}, "PathType": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.sources.types.PathType", "kind": "Gdef", "module_public": false}, "PydanticBaseEnvSettingsSource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get_field_value", 1]], "alt_promote": null, "bases": ["pydantic_settings.sources.base.PydanticBaseSettingsSource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_settings.sources.base.PydanticBaseEnvSettingsSource", "name": "PydanticBaseEnvSettingsSource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "pydantic_settings.sources.base.PydanticBaseEnvSettingsSource", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic_settings.sources.base", "mro": ["pydantic_settings.sources.base.PydanticBaseEnvSettingsSource", "pydantic_settings.sources.base.PydanticBaseSettingsSource", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic_settings.sources.base.PydanticBaseEnvSettingsSource.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_settings.sources.base.PydanticBaseEnvSettingsSource"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of PydanticBaseEnvSettingsSource", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "settings_cls", "case_sensitive", "env_prefix", "env_ignore_empty", "env_parse_none_str", "env_parse_enums"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic_settings.sources.base.PydanticBaseEnvSettingsSource.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "settings_cls", "case_sensitive", "env_prefix", "env_ignore_empty", "env_parse_none_str", "env_parse_enums"], "arg_types": ["pydantic_settings.sources.base.PydanticBaseEnvSettingsSource", {".class": "TypeType", "item": "pydantic_settings.main.BaseSettings"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of PydanticBaseEnvSettingsSource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply_case_sensitive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic_settings.sources.base.PydanticBaseEnvSettingsSource._apply_case_sensitive", "name": "_apply_case_sensitive", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["pydantic_settings.sources.base.PydanticBaseEnvSettingsSource", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_apply_case_sensitive of PydanticBaseEnvSettingsSource", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_field_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "field", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic_settings.sources.base.PydanticBaseEnvSettingsSource._extract_field_info", "name": "_extract_field_info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "field", "field_name"], "arg_types": ["pydantic_settings.sources.base.PydanticBaseEnvSettingsSource", "pydantic.fields.FieldInfo", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_extract_field_info of PydanticBaseEnvSettingsSource", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_resolved_field_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "field", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic_settings.sources.base.PydanticBaseEnvSettingsSource._get_resolved_field_value", "name": "_get_resolved_field_value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "field", "field_name"], "arg_types": ["pydantic_settings.sources.base.PydanticBaseEnvSettingsSource", "pydantic.fields.FieldInfo", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_resolved_field_value of PydanticBaseEnvSettingsSource", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_replace_env_none_type_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic_settings.sources.base.PydanticBaseEnvSettingsSource._replace_env_none_type_values", "name": "_replace_env_none_type_values", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_value"], "arg_types": ["pydantic_settings.sources.base.PydanticBaseEnvSettingsSource", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_replace_env_none_type_values of PydanticBaseEnvSettingsSource", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_replace_field_names_case_insensitively": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "field", "field_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic_settings.sources.base.PydanticBaseEnvSettingsSource._replace_field_names_case_insensitively", "name": "_replace_field_names_case_insensitively", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "field", "field_values"], "arg_types": ["pydantic_settings.sources.base.PydanticBaseEnvSettingsSource", "pydantic.fields.FieldInfo", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_replace_field_names_case_insensitively of PydanticBaseEnvSettingsSource", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "case_sensitive": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.base.PydanticBaseEnvSettingsSource.case_sensitive", "name": "case_sensitive", "setter_type": null, "type": "builtins.bool"}}, "env_ignore_empty": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.base.PydanticBaseEnvSettingsSource.env_ignore_empty", "name": "env_ignore_empty", "setter_type": null, "type": "builtins.bool"}}, "env_parse_enums": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.base.PydanticBaseEnvSettingsSource.env_parse_enums", "name": "env_parse_enums", "setter_type": null, "type": "builtins.bool"}}, "env_parse_none_str": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.base.PydanticBaseEnvSettingsSource.env_parse_none_str", "name": "env_parse_none_str", "setter_type": null, "type": "builtins.str"}}, "env_prefix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.base.PydanticBaseEnvSettingsSource.env_prefix", "name": "env_prefix", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.base.PydanticBaseEnvSettingsSource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic_settings.sources.base.PydanticBaseEnvSettingsSource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PydanticBaseSettingsSource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 1], ["get_field_value", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_settings.sources.base.PydanticBaseSettingsSource", "name": "PydanticBaseSettingsSource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "pydantic_settings.sources.base.PydanticBaseSettingsSource", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic_settings.sources.base", "mro": ["pydantic_settings.sources.base.PydanticBaseSettingsSource", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "pydantic_settings.sources.base.PydanticBaseSettingsSource.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_settings.sources.base.PydanticBaseSettingsSource"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of PydanticBaseSettingsSource", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pydantic_settings.sources.base.PydanticBaseSettingsSource.__call__", "name": "__call__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_settings.sources.base.PydanticBaseSettingsSource"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of PydanticBaseSettingsSource", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "settings_cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic_settings.sources.base.PydanticBaseSettingsSource.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "settings_cls"], "arg_types": ["pydantic_settings.sources.base.PydanticBaseSettingsSource", {".class": "TypeType", "item": "pydantic_settings.main.BaseSettings"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of PydanticBaseSettingsSource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_current_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic_settings.sources.base.PydanticBaseSettingsSource._current_state", "name": "_current_state", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_set_current_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic_settings.sources.base.PydanticBaseSettingsSource._set_current_state", "name": "_set_current_state", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["pydantic_settings.sources.base.PydanticBaseSettingsSource", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_set_current_state of PydanticBaseSettingsSource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_settings_sources_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "states"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic_settings.sources.base.PydanticBaseSettingsSource._set_settings_sources_data", "name": "_set_settings_sources_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "states"], "arg_types": ["pydantic_settings.sources.base.PydanticBaseSettingsSource", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_set_settings_sources_data of PydanticBaseSettingsSource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_settings_sources_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic_settings.sources.base.PydanticBaseSettingsSource._settings_sources_data", "name": "_settings_sources_data", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.base.PydanticBaseSettingsSource.config", "name": "config", "setter_type": null, "type": {".class": "TypedDictType", "fallback": "pydantic_settings.main.SettingsConfigDict", "items": [["title", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["model_title_generator", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.type"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["field_title_generator", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["pydantic.fields.FieldInfo", "pydantic.fields.ComputedFieldInfo"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["str_to_lower", "builtins.bool"], ["str_to_upper", "builtins.bool"], ["str_strip_whitespace", "builtins.bool"], ["str_min_length", "builtins.int"], ["str_max_length", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["extra", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.ExtraValues"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["frozen", "builtins.bool"], ["populate_by_name", "builtins.bool"], ["use_enum_values", "builtins.bool"], ["validate_assignment", "builtins.bool"], ["arbitrary_types_allowed", "builtins.bool"], ["from_attributes", "builtins.bool"], ["loc_by_alias", "builtins.bool"], ["alias_generator", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "pydantic.aliases.AliasGenerator", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["ignored_types", {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.tuple"}], ["allow_inf_nan", "builtins.bool"], ["json_schema_extra", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonDict"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonSchemaExtraCallable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["json_encoders", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.object"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonEncoder"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["strict", "builtins.bool"], ["revalidate_instances", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "always"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "never"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "subclass-instances"}], "uses_pep604_syntax": false}], ["ser_j<PERSON>_<PERSON><PERSON><PERSON>", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "iso8601"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "float"}], "uses_pep604_syntax": false}], ["ser_json_bytes", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "utf8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "base64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hex"}], "uses_pep604_syntax": false}], ["val_json_bytes", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "utf8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "base64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hex"}], "uses_pep604_syntax": false}], ["ser_json_inf_nan", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "null"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "constants"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "strings"}], "uses_pep604_syntax": false}], ["validate_default", "builtins.bool"], ["validate_return", "builtins.bool"], ["protected_namespaces", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], ["hide_input_in_errors", "builtins.bool"], ["defer_build", "builtins.bool"], ["plugin_settings", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["schema_generator", {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic._internal._generate_schema.GenerateSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["json_schema_serialization_defaults_required", "builtins.bool"], ["json_schema_mode_override", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["coerce_numbers_to_str", "builtins.bool"], ["regex_engine", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rust-regex"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "python-re"}], "uses_pep604_syntax": false}], ["validation_error_cause", "builtins.bool"], ["use_attribute_docstrings", "builtins.bool"], ["cache_strings", {".class": "UnionType", "items": ["builtins.bool", {".class": "LiteralType", "fallback": "builtins.str", "value": "all"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "keys"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}], "uses_pep604_syntax": true}], ["validate_by_alias", "builtins.bool"], ["validate_by_name", "builtins.bool"], ["serialize_by_alias", "builtins.bool"], ["case_sensitive", "builtins.bool"], ["nested_model_default_partial_update", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["env_prefix", "builtins.str"], ["env_file", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_settings.sources.types.DotenvType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["env_file_encoding", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["env_ignore_empty", "builtins.bool"], ["env_nested_delimiter", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["env_nested_max_split", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["env_parse_none_str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["env_parse_enums", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["cli_prog_name", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["cli_parse_args", {".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["cli_parse_none_str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["cli_hide_none_type", "builtins.bool"], ["cli_avoid_json", "builtins.bool"], ["cli_enforce_required", "builtins.bool"], ["cli_use_class_docs_for_groups", "builtins.bool"], ["cli_exit_on_error", "builtins.bool"], ["cli_prefix", "builtins.str"], ["cli_flag_prefix_char", "builtins.str"], ["cli_implicit_flags", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["cli_ignore_unknown_args", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["cli_kebab_case", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["secrets_dir", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_settings.sources.types.PathType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["json_file", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_settings.sources.types.PathType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["json_file_encoding", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["yaml_file", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_settings.sources.types.PathType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["yaml_file_encoding", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["pyproject_toml_depth", "builtins.int"], ["pyproject_toml_table_header", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], ["toml_file", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_settings.sources.types.PathType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["enable_decoding", "builtins.bool"]], "readonly_keys": [], "required_keys": []}}}, "current_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "pydantic_settings.sources.base.PydanticBaseSettingsSource.current_state", "name": "current_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_settings.sources.base.PydanticBaseSettingsSource"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_state of PydanticBaseSettingsSource", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic_settings.sources.base.PydanticBaseSettingsSource.current_state", "name": "current_state", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_settings.sources.base.PydanticBaseSettingsSource"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_state of PydanticBaseSettingsSource", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "decode_complex_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "field_name", "field", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic_settings.sources.base.PydanticBaseSettingsSource.decode_complex_value", "name": "decode_complex_value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "field_name", "field", "value"], "arg_types": ["pydantic_settings.sources.base.PydanticBaseSettingsSource", "builtins.str", "pydantic.fields.FieldInfo", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "decode_complex_value of PydanticBaseSettingsSource", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "field_is_complex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic_settings.sources.base.PydanticBaseSettingsSource.field_is_complex", "name": "field_is_complex", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field"], "arg_types": ["pydantic_settings.sources.base.PydanticBaseSettingsSource", "pydantic.fields.FieldInfo"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_is_complex of PydanticBaseSettingsSource", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_field_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "field", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "pydantic_settings.sources.base.PydanticBaseSettingsSource.get_field_value", "name": "get_field_value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "field", "field_name"], "arg_types": ["pydantic_settings.sources.base.PydanticBaseSettingsSource", "pydantic.fields.FieldInfo", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_field_value of PydanticBaseSettingsSource", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pydantic_settings.sources.base.PydanticBaseSettingsSource.get_field_value", "name": "get_field_value", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "field", "field_name"], "arg_types": ["pydantic_settings.sources.base.PydanticBaseSettingsSource", "pydantic.fields.FieldInfo", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_field_value of PydanticBaseSettingsSource", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "prepare_field_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "field_name", "field", "value", "value_is_complex"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic_settings.sources.base.PydanticBaseSettingsSource.prepare_field_value", "name": "prepare_field_value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "field_name", "field", "value", "value_is_complex"], "arg_types": ["pydantic_settings.sources.base.PydanticBaseSettingsSource", "builtins.str", "pydantic.fields.FieldInfo", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "prepare_field_value of PydanticBaseSettingsSource", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "settings_cls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic_settings.sources.base.PydanticBaseSettingsSource.settings_cls", "name": "settings_cls", "setter_type": null, "type": {".class": "TypeType", "item": "pydantic_settings.main.BaseSettings"}}}, "settings_sources_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "pydantic_settings.sources.base.PydanticBaseSettingsSource.settings_sources_data", "name": "settings_sources_data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_settings.sources.base.PydanticBaseSettingsSource"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "settings_sources_data of PydanticBaseSettingsSource", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic_settings.sources.base.PydanticBaseSettingsSource.settings_sources_data", "name": "settings_sources_data", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_settings.sources.base.PydanticBaseSettingsSource"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "settings_sources_data of PydanticBaseSettingsSource", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.base.PydanticBaseSettingsSource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic_settings.sources.base.PydanticBaseSettingsSource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PydanticModel": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.sources.types.PydanticModel", "kind": "Gdef", "module_public": false}, "SettingsError": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.exceptions.SettingsError", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "TypeAdapter": {".class": "SymbolTableNode", "cross_ref": "pydantic.type_adapter.TypeAdapter", "kind": "Gdef", "module_public": false}, "_CliSubCommand": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.sources.types._CliSubCommand", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic_settings.sources.base.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_settings.sources.base.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_settings.sources.base.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_settings.sources.base.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_settings.sources.base.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_settings.sources.base.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_settings.sources.base.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_annotation_is_complex": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.sources.utils._annotation_is_complex", "kind": "Gdef", "module_public": false}, "_annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "_get_alias_names": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.sources.utils._get_alias_names", "kind": "Gdef", "module_public": false}, "_get_model_fields": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.sources.utils._get_model_fields", "kind": "Gdef", "module_public": false}, "_lenient_issubclass": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.utils._lenient_issubclass", "kind": "Gdef", "module_public": false}, "_union_is_complex": {".class": "SymbolTableNode", "cross_ref": "pydantic_settings.sources.utils._union_is_complex", "kind": "Gdef", "module_public": false}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef", "module_public": false}, "asdict": {".class": "SymbolTableNode", "cross_ref": "dataclasses.asdict", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "get_args": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.get_args", "kind": "Gdef", "module_public": false}, "get_origin": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.get_origin", "kind": "Gdef", "module_public": false}, "get_subcommand": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["model", "is_required", "cli_exit_on_error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_settings.sources.base.get_subcommand", "name": "get_subcommand", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["model", "is_required", "cli_exit_on_error"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.types.PydanticModel", "id": -1, "name": "PydanticModel", "namespace": "pydantic_settings.sources.base.get_subcommand", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._dataclasses.PydanticDataclass", "pydantic.main.BaseModel"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_subcommand", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.types.PydanticModel", "id": -1, "name": "PydanticModel", "namespace": "pydantic_settings.sources.base.get_subcommand", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._dataclasses.PydanticDataclass", "pydantic.main.BaseModel"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_settings.sources.types.PydanticModel", "id": -1, "name": "PydanticModel", "namespace": "pydantic_settings.sources.base.get_subcommand", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._dataclasses.PydanticDataclass", "pydantic.main.BaseModel"], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}}}, "is_dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.is_dataclass", "kind": "Gdef", "module_public": false}, "is_model_class": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._utils.is_model_class", "kind": "Gdef", "module_public": false}, "is_union_origin": {".class": "SymbolTableNode", "cross_ref": "typing_inspection.introspection.is_union_origin", "kind": "Gdef", "module_public": false}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/pydantic_settings/sources/base.py"}