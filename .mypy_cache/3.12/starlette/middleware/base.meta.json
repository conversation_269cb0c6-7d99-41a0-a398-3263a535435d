{"data_mtime": 1751559212, "dep_lines": [7, 8, 9, 10, 1, 3, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["starlette._utils", "starlette.requests", "starlette.responses", "starlette.types", "__future__", "typing", "anyio", "builtins", "_frozen_importlib", "abc", "anyio._core", "anyio._core._exceptions", "anyio._core._streams", "anyio._core._synchronization", "anyio._core._tasks", "anyio._core._typedattr", "anyio.abc", "anyio.abc._resources", "anyio.abc._streams", "anyio.abc._tasks", "anyio.streams", "anyio.streams.memory", "contextlib", "starlette.background"], "hash": "c51792d6b2a4cb503fb1f6c8e780e7bcca2d0a92", "id": "starlette.middleware.base", "ignore_all": true, "interface_hash": "4c648eb783e378c933247193491db2e5f9ea7afa", "mtime": 1747044682, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/starlette/middleware/base.py", "plugin_data": null, "size": 8971, "suppressed": [], "version_id": "1.16.1"}