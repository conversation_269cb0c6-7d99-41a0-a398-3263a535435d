{"data_mtime": **********, "dep_lines": [14, 15, 16, 12, 13, 17, 18, 19, 20, 21, 1, 3, 4, 5, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 5, 30, 30, 30, 30], "dependencies": ["starlette.middleware.base", "starlette.middleware.errors", "starlette.middleware.exceptions", "starlette.datastructures", "starlette.middleware", "starlette.requests", "starlette.responses", "starlette.routing", "starlette.types", "starlette.websockets", "__future__", "sys", "typing", "warnings", "builtins", "_frozen_importlib", "_typeshed", "abc", "contextlib"], "hash": "5b182ff8bafdddc8a116a48a4a673e29ba7a36e2", "id": "starlette.applications", "ignore_all": true, "interface_hash": "694eb58c6af2cbccc9c9b8329ff407cb1b70a5f4", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/starlette/applications.py", "plugin_data": null, "size": 10678, "suppressed": [], "version_id": "1.16.1"}