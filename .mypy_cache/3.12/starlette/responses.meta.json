{"data_mtime": 1751559212, "dep_lines": [4, 12, 16, 19, 21, 22, 23, 24, 25, 26, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 10, 20, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["http.cookies", "email.utils", "urllib.parse", "anyio.to_thread", "starlette._utils", "starlette.background", "starlette.concurrency", "starlette.datastructures", "starlette.requests", "starlette.types", "__future__", "<PERSON><PERSON><PERSON>", "http", "json", "os", "re", "stat", "typing", "warnings", "datetime", "functools", "mimetypes", "secrets", "anyio", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "_warnings", "abc", "enum", "types", "urllib"], "hash": "cb733404321f59652d238636b06bd70d2deeb163", "id": "starlette.responses", "ignore_all": true, "interface_hash": "a91545744c38514ae1bea875d810f00bb800ea24", "mtime": 1747044682, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/starlette/responses.py", "plugin_data": null, "size": 20226, "suppressed": [], "version_id": "1.16.1"}