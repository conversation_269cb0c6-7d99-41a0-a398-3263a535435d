{"data_mtime": 1751559181, "dep_lines": [8, 1, 3, 4, 5, 6, 8, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["anyio.to_thread", "__future__", "functools", "sys", "typing", "warnings", "anyio", "builtins", "_frozen_importlib", "_typeshed", "abc", "anyio._core", "anyio._core._synchronization"], "hash": "0f63977fadd1c3c77b2dc200037b823af79d9a08", "id": "starlette.concurrency", "ignore_all": true, "interface_hash": "b1dfeeaaeccebd867ffa2b7f2e4d663dec1deb13", "mtime": 1747044682, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/starlette/concurrency.py", "plugin_data": null, "size": 1746, "suppressed": [], "version_id": "1.16.1"}