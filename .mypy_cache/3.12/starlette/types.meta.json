{"data_mtime": 1751559212, "dep_lines": [4, 5, 6, 1, 1, 1, 1], "dep_prios": [25, 25, 25, 10, 5, 30, 30], "dependencies": ["starlette.requests", "starlette.responses", "starlette.websockets", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "7bee68c119a2a6de93ab3f06336b132af9c579ac", "id": "starlette.types", "ignore_all": true, "interface_hash": "393438255127812ccd141c89e508040b1cb412d1", "mtime": 1747044682, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/starlette/types.py", "plugin_data": null, "size": 1048, "suppressed": [], "version_id": "1.16.1"}