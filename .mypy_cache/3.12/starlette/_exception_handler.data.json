{".class": "MypyFile", "_fullname": "starlette._exception_handler", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ASGIApp": {".class": "SymbolTableNode", "cross_ref": "starlette.types.ASGIApp", "kind": "Gdef"}, "ExceptionHandler": {".class": "SymbolTableNode", "cross_ref": "starlette.types.ExceptionHandler", "kind": "Gdef"}, "ExceptionHandlers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "starlette._exception_handler.ExceptionHandlers", "line": 12, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.ExceptionHandler"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "HTTPException": {".class": "SymbolTableNode", "cross_ref": "starlette.exceptions.HTTPException", "kind": "Gdef"}, "Message": {".class": "SymbolTableNode", "cross_ref": "starlette.types.Message", "kind": "Gdef"}, "Receive": {".class": "SymbolTableNode", "cross_ref": "starlette.types.Receive", "kind": "Gdef"}, "Request": {".class": "SymbolTableNode", "cross_ref": "starlette.requests.Request", "kind": "Gdef"}, "Scope": {".class": "SymbolTableNode", "cross_ref": "starlette.types.Scope", "kind": "Gdef"}, "Send": {".class": "SymbolTableNode", "cross_ref": "starlette.types.Send", "kind": "Gdef"}, "StatusHandlers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "starlette._exception_handler.StatusHandlers", "line": 13, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.ExceptionHandler"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "WebSocket": {".class": "SymbolTableNode", "cross_ref": "starlette.websockets.WebSocket", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette._exception_handler.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette._exception_handler.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette._exception_handler.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette._exception_handler.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette._exception_handler.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette._exception_handler.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_lookup_exception_handler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["exc_handlers", "exc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "starlette._exception_handler._lookup_exception_handler", "name": "_lookup_exception_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["exc_handlers", "exc"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "starlette._exception_handler.ExceptionHandlers"}, "builtins.Exception"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_lookup_exception_handler", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.ExceptionHandler"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "is_async_callable": {".class": "SymbolTableNode", "cross_ref": "starlette._utils.is_async_callable", "kind": "Gdef"}, "run_in_threadpool": {".class": "SymbolTableNode", "cross_ref": "starlette.concurrency.run_in_threadpool", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "wrap_app_handling_exceptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["app", "conn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "starlette._exception_handler.wrap_app_handling_exceptions", "name": "wrap_app_handling_exceptions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["app", "conn"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.ASGIApp"}, {".class": "UnionType", "items": ["starlette.requests.Request", "starlette.websockets.WebSocket"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "wrap_app_handling_exceptions", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "starlette.types.ASGIApp"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/starlette/_exception_handler.py"}