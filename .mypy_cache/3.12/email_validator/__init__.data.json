{".class": "MypyFile", "_fullname": "email_validator", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ALLOW_DISPLAY_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "email_validator.ALLOW_DISPLAY_NAME", "name": "ALLOW_DISPLAY_NAME", "setter_type": null, "type": "builtins.bool"}}, "ALLOW_DOMAIN_LITERAL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "email_validator.ALLOW_DOMAIN_LITERAL", "name": "ALLOW_DOMAIN_LITERAL", "setter_type": null, "type": "builtins.bool"}}, "ALLOW_QUOTED_LOCAL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "email_validator.ALLOW_QUOTED_LOCAL", "name": "ALLOW_QUOTED_LOCAL", "setter_type": null, "type": "builtins.bool"}}, "ALLOW_SMTPUTF8": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "email_validator.ALLOW_SMTPUTF8", "name": "ALLOW_SMTPUTF8", "setter_type": null, "type": "builtins.bool"}}, "CHECK_DELIVERABILITY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "email_validator.CHECK_DELIVERABILITY", "name": "CHECK_DELIVERABILITY", "setter_type": null, "type": "builtins.bool"}}, "DEFAULT_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "email_validator.DEFAULT_TIMEOUT", "name": "DEFAULT_TIMEOUT", "setter_type": null, "type": "builtins.int"}}, "EmailNotValidError": {".class": "SymbolTableNode", "cross_ref": "email_validator.exceptions_types.EmailNotValidError", "kind": "Gdef"}, "EmailSyntaxError": {".class": "SymbolTableNode", "cross_ref": "email_validator.exceptions_types.EmailSyntaxError", "kind": "Gdef"}, "EmailUndeliverableError": {".class": "SymbolTableNode", "cross_ref": "email_validator.exceptions_types.EmailUndeliverableError", "kind": "Gdef"}, "GLOBALLY_DELIVERABLE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "email_validator.GLOBALLY_DELIVERABLE", "name": "GLOBALLY_DELIVERABLE", "setter_type": null, "type": "builtins.bool"}}, "SPECIAL_USE_DOMAIN_NAMES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "email_validator.SPECIAL_USE_DOMAIN_NAMES", "name": "SPECIAL_USE_DOMAIN_NAMES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "TEST_ENVIRONMENT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "email_validator.TEST_ENVIRONMENT", "name": "TEST_ENVIRONMENT", "setter_type": null, "type": "builtins.bool"}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "ValidatedEmail": {".class": "SymbolTableNode", "cross_ref": "email_validator.exceptions_types.ValidatedEmail", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "email_validator.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email_validator.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email_validator.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email_validator.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email_validator.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email_validator.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email_validator.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email_validator.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "cross_ref": "email_validator.version.__version__", "kind": "Gdef"}, "caching_resolver": {".class": "SymbolTableNode", "cross_ref": "email_validator.deliverability.caching_resolver", "kind": "Gdef"}, "validate_email": {".class": "SymbolTableNode", "cross_ref": "email_validator.validate_email.validate_email", "kind": "Gdef"}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/email_validator/__init__.py"}