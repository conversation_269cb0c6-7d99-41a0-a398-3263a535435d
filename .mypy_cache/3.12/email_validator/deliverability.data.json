{".class": "MypyFile", "_fullname": "email_validator.deliverability", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "DeliverabilityInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "email_validator.deliverability.DeliverabilityInfo", "name": "DeliverabilityInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "email_validator.deliverability.DeliverabilityInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "email_validator.deliverability", "mro": ["email_validator.deliverability.DeliverabilityInfo", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["mx", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], ["mx_fallback_type", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["unknown-deliverability", "builtins.str"]], "readonly_keys": [], "required_keys": []}}}, "EmailUndeliverableError": {".class": "SymbolTableNode", "cross_ref": "email_validator.exceptions_types.EmailUndeliverableError", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email_validator.deliverability.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email_validator.deliverability.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email_validator.deliverability.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email_validator.deliverability.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email_validator.deliverability.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email_validator.deliverability.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "caching_resolver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["timeout", "cache", "dns_resolver"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email_validator.deliverability.caching_resolver", "name": "caching_resolver", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["timeout", "cache", "dns_resolver"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.resolver.Resolver", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "caching_resolver", "ret_type": "dns.resolver.Resolver", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dns": {".class": "SymbolTableNode", "cross_ref": "dns", "kind": "Gdef"}, "ipaddress": {".class": "SymbolTableNode", "cross_ref": "ipaddress", "kind": "Gdef"}, "validate_email_deliverability": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["domain", "domain_i18n", "timeout", "dns_resolver"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email_validator.deliverability.validate_email_deliverability", "name": "validate_email_deliverability", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["domain", "domain_i18n", "timeout", "dns_resolver"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.resolver.Resolver", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "validate_email_deliverability", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "email_validator.deliverability.DeliverabilityInfo"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/email_validator/deliverability.py"}