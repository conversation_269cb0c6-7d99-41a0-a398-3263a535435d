{".class": "MypyFile", "_fullname": "email_validator.validate_email", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CASE_INSENSITIVE_MAILBOX_NAMES": {".class": "SymbolTableNode", "cross_ref": "email_validator.rfc_constants.CASE_INSENSITIVE_MAILBOX_NAMES", "kind": "Gdef"}, "EmailSyntaxError": {".class": "SymbolTableNode", "cross_ref": "email_validator.exceptions_types.EmailSyntaxError", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "ValidatedEmail": {".class": "SymbolTableNode", "cross_ref": "email_validator.exceptions_types.ValidatedEmail", "kind": "Gdef"}, "_Resolver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "email_validator.validate_email._Resolver", "line": 10, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "dns.resolver.Resolver"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email_validator.validate_email.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email_validator.validate_email.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email_validator.validate_email.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email_validator.validate_email.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email_validator.validate_email.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "email_validator.validate_email.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "dns": {".class": "SymbolTableNode", "cross_ref": "dns", "kind": "Gdef"}, "split_email": {".class": "SymbolTableNode", "cross_ref": "email_validator.syntax.split_email", "kind": "Gdef"}, "unicodedata": {".class": "SymbolTableNode", "cross_ref": "unicodedata", "kind": "Gdef"}, "validate_email": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": [null, "allow_smtputf8", "allow_empty_local", "allow_quoted_local", "allow_domain_literal", "allow_display_name", "check_deliverability", "test_environment", "globally_deliverable", "timeout", "dns_resolver"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "email_validator.validate_email.validate_email", "name": "validate_email", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": [null, "allow_smtputf8", "allow_empty_local", "allow_quoted_local", "allow_domain_literal", "allow_display_name", "check_deliverability", "test_environment", "globally_deliverable", "timeout", "dns_resolver"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.resolver.Resolver", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "validate_email", "ret_type": "email_validator.exceptions_types.ValidatedEmail", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "validate_email_domain_literal": {".class": "SymbolTableNode", "cross_ref": "email_validator.syntax.validate_email_domain_literal", "kind": "Gdef"}, "validate_email_domain_name": {".class": "SymbolTableNode", "cross_ref": "email_validator.syntax.validate_email_domain_name", "kind": "Gdef"}, "validate_email_length": {".class": "SymbolTableNode", "cross_ref": "email_validator.syntax.validate_email_length", "kind": "Gdef"}, "validate_email_local_part": {".class": "SymbolTableNode", "cross_ref": "email_validator.syntax.validate_email_local_part", "kind": "Gdef"}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/email_validator/validate_email.py"}