{".class": "MypyFile", "_fullname": "dns.quic._sync", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseQuicConnection": {".class": "SymbolTableNode", "cross_ref": "dns.quic._common.BaseQuicConnection", "kind": "Gdef"}, "BaseQuicManager": {".class": "SymbolTableNode", "cross_ref": "dns.quic._common.BaseQuicManager", "kind": "Gdef"}, "BaseQuicStream": {".class": "SymbolTableNode", "cross_ref": "dns.quic._common.BaseQuicStream", "kind": "Gdef"}, "QUIC_MAX_DATAGRAM": {".class": "SymbolTableNode", "cross_ref": "dns.quic._common.QUIC_MAX_DATAGRAM", "kind": "Gdef"}, "SyncQuicConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.quic._common.BaseQuicConnection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.quic._sync.SyncQuicConnection", "name": "SyncQuicConnection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.quic._sync", "mro": ["dns.quic._sync.SyncQuicConnection", "dns.quic._common.BaseQuicConnection", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "connection", "address", "port", "source", "source_port", "manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicConnection.__init__", "name": "__init__", "type": null}}, "_drain_wakeup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicConnection._drain_wakeup", "name": "_drain_wakeup", "type": null}}, "_handle_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicConnection._handle_events", "name": "_handle_events", "type": null}}, "_handshake_complete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.quic._sync.SyncQuicConnection._handshake_complete", "name": "_handshake_complete", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_lock": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.quic._sync.SyncQuicConnection._lock", "name": "_lock", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicConnection._read", "name": "_read", "type": null}}, "_receive_wakeup": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.quic._sync.SyncQuicConnection._receive_wakeup", "name": "_receive_wakeup", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_send_wakeup": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.quic._sync.SyncQuicConnection._send_wakeup", "name": "_send_wakeup", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_socket": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.quic._sync.SyncQuicConnection._socket", "name": "_socket", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_worker": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicConnection._worker", "name": "_worker", "type": null}}, "_worker_thread": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.quic._sync.SyncQuicConnection._worker_thread", "name": "_worker_thread", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicConnection.close", "name": "close", "type": null}}, "close_stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stream_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicConnection.close_stream", "name": "close_stream", "type": null}}, "make_stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicConnection.make_stream", "name": "make_stream", "type": null}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicConnection.run", "name": "run", "type": null}}, "send_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "stream_id", "data", "is_end"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicConnection.send_data", "name": "send_data", "type": null}}, "send_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "stream_id", "headers", "is_end"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicConnection.send_headers", "name": "send_headers", "type": null}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "stream", "data", "is_end"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicConnection.write", "name": "write", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.quic._sync.SyncQuicConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.quic._sync.SyncQuicConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SyncQuicManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.quic._common.BaseQuicManager"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.quic._sync.SyncQuicManager", "name": "SyncQuicManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.quic._sync", "mro": ["dns.quic._sync.SyncQuicManager", "dns.quic._common.BaseQuicManager", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicManager.__enter__", "name": "__enter__", "type": null}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicManager.__exit__", "name": "__exit__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "conf", "verify_mode", "server_name", "h3"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicManager.__init__", "name": "__init__", "type": null}}, "_lock": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.quic._sync.SyncQuicManager._lock", "name": "_lock", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "address", "port"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicManager.closed", "name": "closed", "type": null}}, "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "address", "port", "source", "source_port", "want_session_ticket", "want_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicManager.connect", "name": "connect", "type": null}}, "save_session_ticket": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "address", "port", "ticket"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicManager.save_session_ticket", "name": "save_session_ticket", "type": null}}, "save_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "address", "port", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicManager.save_token", "name": "save_token", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.quic._sync.SyncQuicManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.quic._sync.SyncQuicManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SyncQuicStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.quic._common.BaseQuicStream"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.quic._sync.SyncQuicStream", "name": "SyncQuicStream", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicStream", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.quic._sync", "mro": ["dns.quic._sync.SyncQuicStream", "dns.quic._common.BaseQuicStream", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicStream.__enter__", "name": "__enter__", "type": null}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicStream.__exit__", "name": "__exit__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "stream_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicStream.__init__", "name": "__init__", "type": null}}, "_add_input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "is_end"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicStream._add_input", "name": "_add_input", "type": null}}, "_lock": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.quic._sync.SyncQuicStream._lock", "name": "_lock", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_wake_up": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.quic._sync.SyncQuicStream._wake_up", "name": "_wake_up", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicStream.close", "name": "close", "type": null}}, "receive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicStream.receive", "name": "receive", "type": null}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "datagram", "is_end"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicStream.send", "name": "send", "type": null}}, "wait_for": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "amount", "expiration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicStream.wait_for", "name": "wait_for", "type": null}}, "wait_for_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "expiration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._sync.SyncQuicStream.wait_for_end", "name": "wait_for_end", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.quic._sync.SyncQuicStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.quic._sync.SyncQuicStream", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnexpectedEOF": {".class": "SymbolTableNode", "cross_ref": "dns.quic._common.UnexpectedEOF", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.quic._sync.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.quic._sync.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.quic._sync.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.quic._sync.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.quic._sync.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.quic._sync.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "aioquic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "dns.quic._sync.aioquic", "name": "aioquic", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "dns.quic._sync.aioquic", "source_any": null, "type_of_any": 3}}}, "dns": {".class": "SymbolTableNode", "cross_ref": "dns", "kind": "Gdef"}, "selectors": {".class": "SymbolTableNode", "cross_ref": "selectors", "kind": "Gdef"}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef"}, "socket_factory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "dns.quic._sync.socket_factory", "line": 26, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "socket.socket"}}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef"}, "struct": {".class": "SymbolTableNode", "cross_ref": "struct", "kind": "Gdef"}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/dns/quic/_sync.py"}