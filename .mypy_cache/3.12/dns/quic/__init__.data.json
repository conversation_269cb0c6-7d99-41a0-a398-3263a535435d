{".class": "MypyFile", "_fullname": "dns.quic", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncQuicConnection": {".class": "SymbolTableNode", "cross_ref": "dns.quic._common.AsyncQuicConnection", "kind": "Gdef"}, "AsyncQuicManager": {".class": "SymbolTableNode", "cross_ref": "dns.quic._common.AsyncQuicManager", "kind": "Gdef"}, "AsyncQuicStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.quic.AsyncQuicStream", "name": "AsyncQuicStream", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.quic.AsyncQuicStream", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.quic", "mro": ["dns.quic.AsyncQuicStream", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.quic.AsyncQuicStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.quic.AsyncQuicStream", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncioQuicConnection": {".class": "SymbolTableNode", "cross_ref": "dns.quic._asyncio.AsyncioQuicConnection", "kind": "Gdef"}, "AsyncioQuicManager": {".class": "SymbolTableNode", "cross_ref": "dns.quic._asyncio.AsyncioQuicManager", "kind": "Gdef"}, "AsyncioQuicStream": {".class": "SymbolTableNode", "cross_ref": "dns.quic._asyncio.AsyncioQuicStream", "kind": "Gdef"}, "Headers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "dns.quic.Headers", "line": 80, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "NullContext": {".class": "SymbolTableNode", "cross_ref": "dns._asyncbackend.NullContext", "kind": "Gdef"}, "SyncQuicConnection": {".class": "SymbolTableNode", "cross_ref": "dns.quic._sync.SyncQuicConnection", "kind": "Gdef"}, "SyncQuicManager": {".class": "SymbolTableNode", "cross_ref": "dns.quic._sync.SyncQuicManager", "kind": "Gdef"}, "SyncQuicStream": {".class": "SymbolTableNode", "cross_ref": "dns.quic._sync.SyncQuicStream", "kind": "Gdef"}, "TrioQuicConnection": {".class": "SymbolTableNode", "cross_ref": "dns.quic._trio.TrioQuicConnection", "kind": "Gdef"}, "TrioQuicManager": {".class": "SymbolTableNode", "cross_ref": "dns.quic._trio.TrioQuicManager", "kind": "Gdef"}, "TrioQuicStream": {".class": "SymbolTableNode", "cross_ref": "dns.quic._trio.TrioQuicStream", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.quic.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.quic.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.quic.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.quic.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.quic.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.quic.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.quic.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_async_factories": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "dns.quic._async_factories", "name": "_async_factories", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "TupleType", "implicit": false, "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "null_factory", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["context", "args", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_asyncio_manager_factory", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_asyncio_manager_factory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["context", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._asyncio_manager_factory", "name": "_asyncio_manager_factory", "type": null}}, "_trio_context_factory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._trio_context_factory", "name": "_trio_context_factory", "type": null}}, "_trio_manager_factory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["context", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._trio_manager_factory", "name": "_trio_manager_factory", "type": null}}, "aioquic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "dns.quic.aioquic", "name": "aioquic", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "dns.quic.aioquic", "source_any": null, "type_of_any": 3}}}, "dns": {".class": "SymbolTableNode", "cross_ref": "dns", "kind": "Gdef"}, "factories_for_backend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["backend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic.factories_for_backend", "name": "factories_for_backend", "type": null}}, "have_quic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "dns.quic.have_quic", "name": "have_quic", "setter_type": null, "type": "builtins.bool"}}, "null_factory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic.null_factory", "name": "null_factory", "type": null}}, "trio": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "dns.quic.trio", "name": "trio", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "dns.quic.trio", "source_any": null, "type_of_any": 3}}}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/dns/quic/__init__.py"}