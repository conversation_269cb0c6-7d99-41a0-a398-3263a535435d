{"data_mtime": 1751559177, "dep_lines": [16, 13, 14, 15, 3, 4, 5, 6, 13, 1, 1, 1, 1, 1, 1, 8, 9, 10, 8, 8, 11], "dep_prios": [5, 10, 10, 5, 10, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 10, 10, 10, 20, 20, 10], "dependencies": ["dns.quic._common", "dns.exception", "dns.inet", "dns._asyncbackend", "socket", "ssl", "struct", "time", "dns", "builtins", "_frozen_importlib", "abc", "enum", "types", "typing"], "hash": "70c5bdfc726257249662c5cb64708b41b0cdc4dc", "id": "dns.quic._trio", "ignore_all": true, "interface_hash": "aa3cd8ed2db1f661fdf5271ce7671c911624b2a6", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/dns/quic/_trio.py", "plugin_data": null, "size": 9248, "suppressed": ["aioquic.quic.configuration", "aioquic.quic.connection", "aioquic.quic.events", "aioquic.quic", "aioquic", "trio"], "version_id": "1.16.1"}