{".class": "MypyFile", "_fullname": "dns.quic._trio", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncQuicConnection": {".class": "SymbolTableNode", "cross_ref": "dns.quic._common.AsyncQuicConnection", "kind": "Gdef"}, "AsyncQuicManager": {".class": "SymbolTableNode", "cross_ref": "dns.quic._common.AsyncQuicManager", "kind": "Gdef"}, "BaseQuicStream": {".class": "SymbolTableNode", "cross_ref": "dns.quic._common.BaseQuicStream", "kind": "Gdef"}, "NullContext": {".class": "SymbolTableNode", "cross_ref": "dns._asyncbackend.NullContext", "kind": "Gdef"}, "QUIC_MAX_DATAGRAM": {".class": "SymbolTableNode", "cross_ref": "dns.quic._common.QUIC_MAX_DATAGRAM", "kind": "Gdef"}, "TrioQuicConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.quic._common.AsyncQuicConnection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.quic._trio.TrioQuicConnection", "name": "TrioQuicConnection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.quic._trio.TrioQuicConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.quic._trio", "mro": ["dns.quic._trio.TrioQuicConnection", "dns.quic._common.AsyncQuicConnection", "dns.quic._common.BaseQuicConnection", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "connection", "address", "port", "source", "source_port", "manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._trio.TrioQuicConnection.__init__", "name": "__init__", "type": null}}, "_handle_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._trio.TrioQuicConnection._handle_events", "name": "_handle_events", "type": null}}, "_handshake_complete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.quic._trio.TrioQuicConnection._handshake_complete", "name": "_handshake_complete", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_run_done": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.quic._trio.TrioQuicConnection._run_done", "name": "_run_done", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_send_pending": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.quic._trio.TrioQuicConnection._send_pending", "name": "_send_pending", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_socket": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.quic._trio.TrioQuicConnection._socket", "name": "_socket", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_worker": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._trio.TrioQuicConnection._worker", "name": "_worker", "type": null}}, "_worker_scope": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.quic._trio.TrioQuicConnection._worker_scope", "name": "_worker_scope", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._trio.TrioQuicConnection.close", "name": "close", "type": null}}, "make_stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._trio.TrioQuicConnection.make_stream", "name": "make_stream", "type": null}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._trio.TrioQuicConnection.run", "name": "run", "type": null}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "stream", "data", "is_end"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._trio.TrioQuicConnection.write", "name": "write", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.quic._trio.TrioQuicConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.quic._trio.TrioQuicConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TrioQuicManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.quic._common.AsyncQuicManager"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.quic._trio.TrioQuicManager", "name": "TrioQuicManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.quic._trio.TrioQuicManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.quic._trio", "mro": ["dns.quic._trio.TrioQuicManager", "dns.quic._common.AsyncQuicManager", "dns.quic._common.BaseQuicManager", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._trio.TrioQuicManager.__aenter__", "name": "__aenter__", "type": null}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc_val", "exc_tb"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._trio.TrioQuicManager.__aexit__", "name": "__aexit__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "nursery", "conf", "verify_mode", "server_name", "h3"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._trio.TrioQuicManager.__init__", "name": "__init__", "type": null}}, "_nursery": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.quic._trio.TrioQuicManager._nursery", "name": "_nursery", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "address", "port", "source", "source_port", "want_session_ticket"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._trio.TrioQuicManager.connect", "name": "connect", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.quic._trio.TrioQuicManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.quic._trio.TrioQuicManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TrioQuicStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.quic._common.BaseQuicStream"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.quic._trio.TrioQuicStream", "name": "TrioQuicStream", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.quic._trio.TrioQuicStream", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.quic._trio", "mro": ["dns.quic._trio.TrioQuicStream", "dns.quic._common.BaseQuicStream", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._trio.TrioQuicStream.__aenter__", "name": "__aenter__", "type": null}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc_val", "exc_tb"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._trio.TrioQuicStream.__aexit__", "name": "__aexit__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "stream_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._trio.TrioQuicStream.__init__", "name": "__init__", "type": null}}, "_add_input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "is_end"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._trio.TrioQuicStream._add_input", "name": "_add_input", "type": null}}, "_wake_up": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.quic._trio.TrioQuicStream._wake_up", "name": "_wake_up", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._trio.TrioQuicStream.close", "name": "close", "type": null}}, "receive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._trio.TrioQuicStream.receive", "name": "receive", "type": null}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "datagram", "is_end"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._trio.TrioQuicStream.send", "name": "send", "type": null}}, "wait_for": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "amount"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._trio.TrioQuicStream.wait_for", "name": "wait_for", "type": null}}, "wait_for_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._trio.TrioQuicStream.wait_for_end", "name": "wait_for_end", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.quic._trio.TrioQuicStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.quic._trio.TrioQuicStream", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnexpectedEOF": {".class": "SymbolTableNode", "cross_ref": "dns.quic._common.UnexpectedEOF", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.quic._trio.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.quic._trio.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.quic._trio.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.quic._trio.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.quic._trio.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.quic._trio.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "aioquic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "dns.quic._trio.aioquic", "name": "aioquic", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "dns.quic._trio.aioquic", "source_any": null, "type_of_any": 3}}}, "dns": {".class": "SymbolTableNode", "cross_ref": "dns", "kind": "Gdef"}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef"}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef"}, "struct": {".class": "SymbolTableNode", "cross_ref": "struct", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "trio": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "dns.quic._trio.trio", "name": "trio", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "dns.quic._trio.trio", "source_any": null, "type_of_any": 3}}}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/dns/quic/_trio.py"}