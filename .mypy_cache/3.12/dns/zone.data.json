{".class": "MypyFile", "_fullname": "dns.zone", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BadZone": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.exception.DNSException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.zone.BadZone", "name": "BadZone", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.zone.BadZone", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.zone", "mro": ["dns.zone.BadZone", "dns.exception.DNSException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.zone.BadZone.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.zone.BadZone", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "DigestHashAlgorithm": {".class": "SymbolTableNode", "cross_ref": "dns.zonetypes.DigestHashAlgorithm", "kind": "Gdef"}, "DigestScheme": {".class": "SymbolTableNode", "cross_ref": "dns.zonetypes.DigestScheme", "kind": "Gdef"}, "DigestVerificationFailure": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.exception.DNSException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.zone.DigestVerificationFailure", "name": "DigestVerificationFailure", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.zone.DigestVerificationFailure", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.zone", "mro": ["dns.zone.DigestVerificationFailure", "dns.exception.DNSException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.zone.DigestVerificationFailure.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.zone.DigestVerificationFailure", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImmutableVersion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.zone.Version"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.zone.ImmutableVersion", "name": "ImmutableVersion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.zone.ImmutableVersion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.zone", "mro": ["dns.zone.ImmutableVersion", "dns.zone.Version", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "version"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.ImmutableVersion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "version"], "arg_types": ["dns.zone.ImmutableVersion", "dns.zone.WritableVersion"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ImmutableVersion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.zone.ImmutableVersion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.zone.ImmutableVersion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImmutableVersionedNode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.zone.VersionedNode"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.zone.ImmutableVersionedNode", "name": "ImmutableVersionedNode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.zone.ImmutableVersionedNode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.zone", "mro": ["dns.zone.ImmutableVersionedNode", "dns.zone.VersionedNode", "dns.node.Node", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.ImmutableVersionedNode.__init__", "name": "__init__", "type": null}}, "delete_rdataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "rdclass", "rdtype", "covers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.ImmutableVersionedNode.delete_rdataset", "name": "delete_rdataset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "rdclass", "rdtype", "covers"], "arg_types": ["dns.zone.ImmutableVersionedNode", "dns.rdataclass.RdataClass", "dns.rdatatype.RdataType", "dns.rdatatype.RdataType"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_rdataset of ImmutableVersionedNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_rdataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "rdclass", "rdtype", "covers", "create"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.ImmutableVersionedNode.find_rdataset", "name": "find_rdataset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "rdclass", "rdtype", "covers", "create"], "arg_types": ["dns.zone.ImmutableVersionedNode", "dns.rdataclass.RdataClass", "dns.rdatatype.RdataType", "dns.rdatatype.RdataType", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_rdataset of ImmutableVersionedNode", "ret_type": "dns.rdataset.Rdataset", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_rdataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "rdclass", "rdtype", "covers", "create"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.ImmutableVersionedNode.get_rdataset", "name": "get_rdataset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "rdclass", "rdtype", "covers", "create"], "arg_types": ["dns.zone.ImmutableVersionedNode", "dns.rdataclass.RdataClass", "dns.rdatatype.RdataType", "dns.rdatatype.RdataType", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_rdataset of ImmutableVersionedNode", "ret_type": {".class": "UnionType", "items": ["dns.rdataset.Rdataset", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_immutable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.ImmutableVersionedNode.is_immutable", "name": "is_immutable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["dns.zone.ImmutableVersionedNode"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_immutable of ImmutableVersionedNode", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "replace_rdataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "replacement"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.ImmutableVersionedNode.replace_rdataset", "name": "replace_rdataset", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "replacement"], "arg_types": ["dns.zone.ImmutableVersionedNode", "dns.rdataset.Rdataset"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "replace_rdataset of ImmutableVersionedNode", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.zone.ImmutableVersionedNode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.zone.ImmutableVersionedNode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef"}, "NoDigest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.exception.DNSException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.zone.NoDigest", "name": "NoDigest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.zone.NoDigest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.zone", "mro": ["dns.zone.NoDigest", "dns.exception.DNSException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.zone.NoDigest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.zone.NoDigest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoNS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.zone.BadZone"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.zone.NoNS", "name": "NoNS", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.zone.NoNS", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.zone", "mro": ["dns.zone.NoNS", "dns.zone.BadZone", "dns.exception.DNSException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.zone.NoNS.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.zone.NoNS", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoSOA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.zone.BadZone"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.zone.NoSOA", "name": "NoSOA", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.zone.NoSOA", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.zone", "mro": ["dns.zone.NoSOA", "dns.zone.BadZone", "dns.exception.DNSException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.zone.NoSOA.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.zone.NoSOA", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "Transaction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.transaction.Transaction"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.zone.Transaction", "name": "Transaction", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.zone.Transaction", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.zone", "mro": ["dns.zone.Transaction", "dns.transaction.Transaction", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "zone", "replacement", "version", "make_immutable"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Transaction.__init__", "name": "__init__", "type": null}}, "_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Transaction._changed", "name": "_changed", "type": null}}, "_delete_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Transaction._delete_name", "name": "_delete_name", "type": null}}, "_delete_rdataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "rdtype", "covers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Transaction._delete_rdataset", "name": "_delete_rdataset", "type": null}}, "_end_transaction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "commit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Transaction._end_transaction", "name": "_end_transaction", "type": null}}, "_get_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Transaction._get_node", "name": "_get_node", "type": null}}, "_get_rdataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "rdtype", "covers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Transaction._get_rdataset", "name": "_get_rdataset", "type": null}}, "_iterate_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Transaction._iterate_names", "name": "_iterate_names", "type": null}}, "_iterate_rdatasets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Transaction._iterate_rdatasets", "name": "_iterate_rdatasets", "type": null}}, "_name_exists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Transaction._name_exists", "name": "_name_exists", "type": null}}, "_origin_information": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Transaction._origin_information", "name": "_origin_information", "type": null}}, "_put_rdataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "rdataset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Transaction._put_rdataset", "name": "_put_rdataset", "type": null}}, "_set_origin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "origin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Transaction._set_origin", "name": "_set_origin", "type": null}}, "_setup_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Transaction._setup_version", "name": "_setup_version", "type": null}}, "make_immutable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.zone.Transaction.make_immutable", "name": "make_immutable", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.zone.Transaction.version", "name": "version", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "zone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "dns.zone.Transaction.zone", "name": "zone", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "dns.zone.Transaction.zone", "name": "zone", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["dns.zone.Transaction"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "zone of Transaction", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.zone.Transaction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.zone.Transaction", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UnknownOrigin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.zone.BadZone"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.zone.<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.zone.<PERSON><PERSON><PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.zone", "mro": ["dns.zone.<PERSON><PERSON><PERSON><PERSON>", "dns.zone.BadZone", "dns.exception.DNSException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.zone.Unknown<PERSON><PERSON><PERSON>.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.zone.<PERSON><PERSON><PERSON><PERSON>", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnsupportedDigestHashAlgorithm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.exception.DNSException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.zone.UnsupportedDigestHashAlgorithm", "name": "UnsupportedDigestHashAlgorithm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.zone.UnsupportedDigestHashAlgorithm", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.zone", "mro": ["dns.zone.UnsupportedDigestHashAlgorithm", "dns.exception.DNSException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.zone.UnsupportedDigestHashAlgorithm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.zone.UnsupportedDigestHashAlgorithm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnsupportedDigestScheme": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.exception.DNSException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.zone.UnsupportedDigestScheme", "name": "UnsupportedDigestScheme", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.zone.UnsupportedDigestScheme", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.zone", "mro": ["dns.zone.UnsupportedDigestScheme", "dns.exception.DNSException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.zone.UnsupportedDigestScheme.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.zone.UnsupportedDigestScheme", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.zone.Version", "name": "Version", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.zone.Version", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.zone", "mro": ["dns.zone.Version", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "zone", "id", "nodes", "origin"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Version.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "zone", "id", "nodes", "origin"], "arg_types": ["dns.zone.Version", "dns.zone.Zone", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["dns.name.Name", "dns.node.Node"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.name.Name", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Version", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_validate_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Version._validate_name", "name": "_validate_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["dns.zone.Version", "dns.name.Name"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_validate_name of Version", "ret_type": "dns.name.Name", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Version.get_node", "name": "get_node", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["dns.zone.Version", "dns.name.Name"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_node of Version", "ret_type": {".class": "UnionType", "items": ["dns.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_rdataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "rdtype", "covers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Version.get_rdataset", "name": "get_rdataset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "rdtype", "covers"], "arg_types": ["dns.zone.Version", "dns.name.Name", "dns.rdatatype.RdataType", "dns.rdatatype.RdataType"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_rdataset of Version", "ret_type": {".class": "UnionType", "items": ["dns.rdataset.Rdataset", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.zone.Version.id", "name": "id", "setter_type": null, "type": "builtins.int"}}, "items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Version.items", "name": "items", "type": null}}, "keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Version.keys", "name": "keys", "type": null}}, "nodes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.zone.Version.nodes", "name": "nodes", "setter_type": null, "type": {".class": "Instance", "args": ["dns.name.Name", "dns.node.Node"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}}}, "origin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.zone.Version.origin", "name": "origin", "setter_type": null, "type": {".class": "UnionType", "items": ["dns.name.Name", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "zone": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.zone.Version.zone", "name": "zone", "setter_type": null, "type": "dns.zone.Zone"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.zone.Version.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.zone.Version", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "VersionedNode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.node.Node"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.zone.VersionedNode", "name": "VersionedNode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.zone.VersionedNode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.zone", "mro": ["dns.zone.VersionedNode", "dns.node.Node", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.VersionedNode.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "dns.zone.VersionedNode.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.zone.VersionedNode.id", "name": "id", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.zone.VersionedNode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.zone.VersionedNode", "values": [], "variance": 0}, "slots": ["id", "rdatasets"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WritableVersion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.zone.Version"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.zone.WritableVersion", "name": "WritableVersion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.zone.WritableVersion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.zone", "mro": ["dns.zone.WritableVersion", "dns.zone.Version", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "zone", "replacement"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.WritableVersion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "zone", "replacement"], "arg_types": ["dns.zone.WritableVersion", "dns.zone.Zone", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of WritableVersion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_maybe_cow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.WritableVersion._maybe_cow", "name": "_maybe_cow", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["dns.zone.WritableVersion", "dns.name.Name"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_maybe_cow of WritableVersion", "ret_type": "dns.node.Node", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "changed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "dns.zone.WritableVersion.changed", "name": "changed", "setter_type": null, "type": {".class": "Instance", "args": ["dns.name.Name"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "delete_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.WritableVersion.delete_node", "name": "delete_node", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["dns.zone.WritableVersion", "dns.name.Name"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_node of WritableVersion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_rdataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "rdtype", "covers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.WritableVersion.delete_rdataset", "name": "delete_rdataset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "rdtype", "covers"], "arg_types": ["dns.zone.WritableVersion", "dns.name.Name", "dns.rdatatype.RdataType", "dns.rdatatype.RdataType"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_rdataset of WritableVersion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "put_rdataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "rdataset"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.WritableVersion.put_rdataset", "name": "put_rdataset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "rdataset"], "arg_types": ["dns.zone.WritableVersion", "dns.name.Name", "dns.rdataset.Rdataset"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "put_rdataset of WritableVersion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.zone.WritableVersion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.zone.WritableVersion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Zone": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.transaction.TransactionManager"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.zone.Zone", "name": "Zone", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.zone.Zone", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.zone", "mro": ["dns.zone.Zone", "dns.transaction.TransactionManager", "builtins.object"], "names": {".class": "SymbolTable", "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Zone.__contains__", "name": "__contains__", "type": null}}, "__delitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Zone.__delitem__", "name": "__delitem__", "type": null}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Zone.__eq__", "name": "__eq__", "type": null}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Zone.__getitem__", "name": "__getitem__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "origin", "rdclass", "relativize"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Zone.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "origin", "rdclass", "relativize"], "arg_types": ["dns.zone.Zone", {".class": "UnionType", "items": ["dns.name.Name", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "dns.rdataclass.RdataClass", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Zone", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Zone.__iter__", "name": "__iter__", "type": null}}, "__ne__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Zone.__ne__", "name": "__ne__", "type": null}}, "__setitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Zone.__setitem__", "name": "__setitem__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "dns.zone.Zone.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_commit_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "_", "version", "origin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Zone._commit_version", "name": "_commit_version", "type": null}}, "_compute_digest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "hash_algorithm", "scheme"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Zone._compute_digest", "name": "_compute_digest", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "hash_algorithm", "scheme"], "arg_types": ["dns.zone.Zone", "dns.zonetypes.DigestHashAlgorithm", "dns.zonetypes.DigestScheme"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_digest of Zone", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_end_read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "txn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Zone._end_read", "name": "_end_read", "type": null}}, "_end_write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "txn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Zone._end_write", "name": "_end_write", "type": null}}, "_get_next_version_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Zone._get_next_version_id", "name": "_get_next_version_id", "type": null}}, "_validate_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Zone._validate_name", "name": "_validate_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["dns.zone.Zone", {".class": "UnionType", "items": ["dns.name.Name", "builtins.str"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_validate_name of Zone", "ret_type": "dns.name.Name", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_origin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Zone.check_origin", "name": "check_origin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["dns.zone.Zone"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "check_origin of Zone", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_digest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "hash_algorithm", "scheme"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Zone.compute_digest", "name": "compute_digest", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "hash_algorithm", "scheme"], "arg_types": ["dns.zone.Zone", "dns.zonetypes.DigestHashAlgorithm", "dns.zonetypes.DigestScheme"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_digest of Zone", "ret_type": "dns.rdtypes.ANY.ZONEMD.ZONEMD", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Zone.delete_node", "name": "delete_node", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["dns.zone.Zone", {".class": "UnionType", "items": ["dns.name.Name", "builtins.str"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_node of Zone", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_rdataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "name", "rdtype", "covers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Zone.delete_rdataset", "name": "delete_rdataset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "name", "rdtype", "covers"], "arg_types": ["dns.zone.Zone", {".class": "UnionType", "items": ["dns.name.Name", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.rdatatype.RdataType", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.rdatatype.RdataType", "builtins.str"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_rdataset of Zone", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "create"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Zone.find_node", "name": "find_node", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "create"], "arg_types": ["dns.zone.Zone", {".class": "UnionType", "items": ["dns.name.Name", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_node of Zone", "ret_type": "dns.node.Node", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_rdataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "name", "rdtype", "covers", "create"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Zone.find_rdataset", "name": "find_rdataset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "name", "rdtype", "covers", "create"], "arg_types": ["dns.zone.Zone", {".class": "UnionType", "items": ["dns.name.Name", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.rdatatype.RdataType", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.rdatatype.RdataType", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_rdataset of Zone", "ret_type": "dns.rdataset.Rdataset", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_rrset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "name", "rdtype", "covers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Zone.find_rrset", "name": "find_rrset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "name", "rdtype", "covers"], "arg_types": ["dns.zone.Zone", {".class": "UnionType", "items": ["dns.name.Name", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.rdatatype.RdataType", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.rdatatype.RdataType", "builtins.str"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_rrset of Zone", "ret_type": "dns.rrset.RRset", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Zone.get", "name": "get", "type": null}}, "get_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Zone.get_class", "name": "get_class", "type": null}}, "get_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "create"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Zone.get_node", "name": "get_node", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "create"], "arg_types": ["dns.zone.Zone", {".class": "UnionType", "items": ["dns.name.Name", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_node of Zone", "ret_type": {".class": "UnionType", "items": ["dns.node.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_rdataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "name", "rdtype", "covers", "create"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Zone.get_rdataset", "name": "get_rdataset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "name", "rdtype", "covers", "create"], "arg_types": ["dns.zone.Zone", {".class": "UnionType", "items": ["dns.name.Name", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.rdatatype.RdataType", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.rdatatype.RdataType", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_rdataset of Zone", "ret_type": {".class": "UnionType", "items": ["dns.rdataset.Rdataset", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_rrset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "name", "rdtype", "covers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Zone.get_rrset", "name": "get_rrset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "name", "rdtype", "covers"], "arg_types": ["dns.zone.Zone", {".class": "UnionType", "items": ["dns.name.Name", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.rdatatype.RdataType", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.rdatatype.RdataType", "builtins.str"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_rrset of Zone", "ret_type": {".class": "UnionType", "items": ["dns.rrset.RRset", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_soa": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "txn"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Zone.get_soa", "name": "get_soa", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "txn"], "arg_types": ["dns.zone.Zone", {".class": "UnionType", "items": ["dns.transaction.Transaction", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_soa of Zone", "ret_type": "dns.rdtypes.ANY.SOA.SOA", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "immutable_version_factory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "dns.zone.Zone.immutable_version_factory", "name": "immutable_version_factory", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "dns.zone.ImmutableVersion", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Zone.items", "name": "items", "type": null}}, "iterate_rdatas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "rdtype", "covers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Zone.iterate_rdatas", "name": "iterate_rdatas", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "rdtype", "covers"], "arg_types": ["dns.zone.Zone", {".class": "UnionType", "items": ["dns.rdatatype.RdataType", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.rdatatype.RdataType", "builtins.str"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "iterate_rdatas of Zone", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["dns.name.Name", "builtins.int", "dns.rdata.Rdata"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iterate_rdatasets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "rdtype", "covers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Zone.iterate_rdatasets", "name": "iterate_rdatasets", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "rdtype", "covers"], "arg_types": ["dns.zone.Zone", {".class": "UnionType", "items": ["dns.rdatatype.RdataType", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.rdatatype.RdataType", "builtins.str"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "iterate_rdatasets of Zone", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["dns.name.Name", "dns.rdataset.Rdataset"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Zone.keys", "name": "keys", "type": null}}, "map_factory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "dns.zone.Zone.map_factory", "name": "map_factory", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["dns.name.Name", "dns.node.Node"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "node_factory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "dns.zone.Zone.node_factory", "name": "node_factory", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "dns.node.Node", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "nodes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "dns.zone.Zone.nodes", "name": "nodes", "setter_type": null, "type": {".class": "Instance", "args": ["dns.name.Name", "dns.node.Node"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}}}, "origin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.zone.Zone.origin", "name": "origin", "setter_type": null, "type": "dns.name.Name"}}, "origin_information": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Zone.origin_information", "name": "origin_information", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["dns.zone.Zone"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "origin_information of Zone", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["dns.name.Name", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["dns.name.Name", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rdclass": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.zone.Zone.rdclass", "name": "rdclass", "setter_type": null, "type": "dns.rdataclass.RdataClass"}}, "reader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Zone.reader", "name": "reader", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["dns.zone.Zone"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "reader of Zone", "ret_type": "dns.zone.Transaction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "relativize": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.zone.Zone.relativize", "name": "relativize", "setter_type": null, "type": "builtins.bool"}}, "replace_rdataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "replacement"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Zone.replace_rdataset", "name": "replace_rdataset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "replacement"], "arg_types": ["dns.zone.Zone", {".class": "UnionType", "items": ["dns.name.Name", "builtins.str"], "uses_pep604_syntax": false}, "dns.rdataset.Rdataset"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "replace_rdataset of Zone", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "f", "sorted", "relativize", "nl", "want_comments", "want_origin"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Zone.to_file", "name": "to_file", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "f", "sorted", "relativize", "nl", "want_comments", "want_origin"], "arg_types": ["dns.zone.Zone", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_file of Zone", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "sorted", "relativize", "nl", "want_comments", "want_origin"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Zone.to_text", "name": "to_text", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "sorted", "relativize", "nl", "want_comments", "want_origin"], "arg_types": ["dns.zone.Zone", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_text of Zone", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.Zone.values", "name": "values", "type": null}}, "verify_digest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "zonemd"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Zone.verify_digest", "name": "verify_digest", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "zonemd"], "arg_types": ["dns.zone.Zone", {".class": "UnionType", "items": ["dns.rdtypes.ANY.ZONEMD.ZONEMD", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "verify_digest of Zone", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "writable_version_factory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "dns.zone.Zone.writable_version_factory", "name": "writable_version_factory", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "dns.zone.WritableVersion", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "writer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "replacement"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.zone.Zone.writer", "name": "writer", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "replacement"], "arg_types": ["dns.zone.Zone", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "writer of Zone", "ret_type": "dns.zone.Transaction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.zone.Zone.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.zone.Zone", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.zone.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.zone.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.zone.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.zone.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.zone.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.zone.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_digest_hashers": {".class": "SymbolTableNode", "cross_ref": "dns.zonetypes._digest_hashers", "kind": "Gdef"}, "_from_text": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["text", "origin", "rdclass", "relativize", "zone_factory", "filename", "allow_include", "check_origin", "idna_codec", "allow_directives"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone._from_text", "name": "_from_text", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["text", "origin", "rdclass", "relativize", "zone_factory", "filename", "allow_include", "check_origin", "idna_codec", "allow_directives"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["dns.name.Name", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "dns.rdataclass.RdataClass", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["dns.name.IDNACodec", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_from_text", "ret_type": "dns.zone.Zone", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_validate_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["name", "origin", "relativize"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone._validate_name", "name": "_validate_name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["name", "origin", "relativize"], "arg_types": ["dns.name.Name", {".class": "UnionType", "items": ["dns.name.Name", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_validate_name", "ret_type": "dns.name.Name", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "dns": {".class": "SymbolTableNode", "cross_ref": "dns", "kind": "Gdef"}, "from_file": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["f", "origin", "rdclass", "relativize", "zone_factory", "filename", "allow_include", "check_origin", "idna_codec", "allow_directives"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.from_file", "name": "from_file", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["f", "origin", "rdclass", "relativize", "zone_factory", "filename", "allow_include", "check_origin", "idna_codec", "allow_directives"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["dns.name.Name", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "dns.rdataclass.RdataClass", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["dns.name.IDNACodec", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_file", "ret_type": "dns.zone.Zone", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_text": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["text", "origin", "rdclass", "relativize", "zone_factory", "filename", "allow_include", "check_origin", "idna_codec", "allow_directives"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.from_text", "name": "from_text", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["text", "origin", "rdclass", "relativize", "zone_factory", "filename", "allow_include", "check_origin", "idna_codec", "allow_directives"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["dns.name.Name", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "dns.rdataclass.RdataClass", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["dns.name.IDNACodec", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_text", "ret_type": "dns.zone.Zone", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_xfr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["xfr", "zone_factory", "relativize", "check_origin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.zone.from_xfr", "name": "from_xfr", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["xfr", "zone_factory", "relativize", "check_origin"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_xfr", "ret_type": "dns.zone.Zone", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "struct": {".class": "SymbolTableNode", "cross_ref": "struct", "kind": "Gdef"}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/dns/zone.py"}