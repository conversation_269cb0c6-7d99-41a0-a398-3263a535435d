{"data_mtime": 1751559218, "dep_lines": [2, 4, 5, 6, 7, 8, 1, 4, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30], "dependencies": ["urllib.parse", "dns.asyncbackend", "dns.as<PERSON><PERSON>y", "dns.inet", "dns.message", "dns.query", "typing", "dns", "builtins", "_frozen_importlib", "abc", "dns._asyncbackend", "enum"], "hash": "73eaabf437559e86ba803cfa532e8bf05a33185d", "id": "dns.nameserver", "ignore_all": true, "interface_hash": "9b8d2dacbfd5045f5977ffd011f53f169753130a", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/dns/nameserver.py", "plugin_data": null, "size": 10115, "suppressed": [], "version_id": "1.16.1"}