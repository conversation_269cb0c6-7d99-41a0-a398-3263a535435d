{".class": "MypyFile", "_fullname": "dns.reversename", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.reversename.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.reversename.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.reversename.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.reversename.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.reversename.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.reversename.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "binascii": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "dns": {".class": "SymbolTableNode", "cross_ref": "dns", "kind": "Gdef"}, "from_address": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["text", "v4_origin", "v6_origin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.reversename.from_address", "name": "from_address", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["text", "v4_origin", "v6_origin"], "arg_types": ["builtins.str", "dns.name.Name", "dns.name.Name"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_address", "ret_type": "dns.name.Name", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ipv4_reverse_domain": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "dns.reversename.ipv4_reverse_domain", "name": "ipv4_reverse_domain", "setter_type": null, "type": "dns.name.Name"}}, "ipv6_reverse_domain": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "dns.reversename.ipv6_reverse_domain", "name": "ipv6_reverse_domain", "setter_type": null, "type": "dns.name.Name"}}, "to_address": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["name", "v4_origin", "v6_origin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.reversename.to_address", "name": "to_address", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["name", "v4_origin", "v6_origin"], "arg_types": ["dns.name.Name", "dns.name.Name", "dns.name.Name"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_address", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/dns/reversename.py"}