{"data_mtime": 1751559216, "dep_lines": [46, 47, 46, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 48, 49, 50, 51, 52, 53, 20, 21, 22, 23, 24, 37, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 20, 10, 10, 10, 10, 10, 10, 10, 10, 10, 20, 10, 10, 10, 10, 10, 5, 10, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["dns.rdtypes.ANY.SOA", "dns.rdtypes.ANY.ZONEMD", "dns.rdtypes.ANY", "dns.exception", "dns.grange", "dns.immutable", "dns.name", "dns.node", "dns.rdata", "dns.rdataclass", "dns.rdataset", "dns.rdatatype", "dns.rdtypes", "dns.rrset", "dns.tokenizer", "dns.transaction", "dns.ttl", "dns.zonefile", "dns.zonetypes", "contextlib", "io", "os", "struct", "typing", "dns", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "dns._immutable_ctx", "dns.enum", "dns.set", "enum", "types"], "hash": "3e681883181f380d65532987184cf80afe1a6643", "id": "dns.zone", "ignore_all": true, "interface_hash": "340893c1378dd43a234b5c60eccc8438a175a2b5", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/dns/zone.py", "plugin_data": null, "size": 52086, "suppressed": [], "version_id": "1.16.1"}