{"data_mtime": 1751559218, "dep_lines": [66, 25, 31, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 66, 136, 20, 21, 22, 23, 24, 26, 27, 28, 29, 30, 31, 32, 34, 66, 67, 154, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 20, 20, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 20, 5, 20, 20, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["httpcore._backends.sync", "os.path", "urllib.parse", "dns._features", "dns.exception", "dns.inet", "dns.message", "dns.name", "dns.quic", "dns.rcode", "dns.rdataclass", "dns.rdatatype", "dns.serial", "dns.transaction", "dns.tsig", "dns.xfr", "httpcore._backends", "dns.resolver", "base64", "contextlib", "enum", "errno", "os", "random", "selectors", "socket", "struct", "time", "urllib", "typing", "dns", "httpcore", "httpx", "ssl", "builtins", "_frozen_importlib", "_socket", "_ssl", "abc", "dns.enum", "dns.quic._common", "dns.quic._sync", "httpcore._backends.base", "httpx._transports", "httpx._transports.base", "httpx._transports.default"], "hash": "5f803a6b7d04817abc1a02fcb149fa257f99615b", "id": "dns.query", "ignore_all": true, "interface_hash": "a8d813892a157771712a7fe8172c0e1536f9f992", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/dns/query.py", "plugin_data": null, "size": 56298, "suppressed": [], "version_id": "1.16.1"}