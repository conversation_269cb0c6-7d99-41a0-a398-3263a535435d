{"data_mtime": 1751559218, "dep_lines": [26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 20, 21, 22, 23, 24, 25, 26, 27, 29, 56, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 10, 10, 10, 10, 10, 10, 20, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["urllib.parse", "dns.asyncbackend", "dns.exception", "dns.inet", "dns.message", "dns.name", "dns.quic", "dns.rcode", "dns.rdataclass", "dns.rdatatype", "dns.transaction", "dns._asyncbackend", "dns.query", "base64", "contextlib", "random", "socket", "struct", "time", "urllib", "typing", "dns", "httpx", "builtins", "_collections_abc", "_frozen_importlib", "_ssl", "_struct", "abc", "dns.enum", "dns.quic._common", "dns.rdataset", "dns.rrset", "dns.set", "dns.tsig", "dns.xfr", "enum", "httpx._client", "ssl"], "hash": "d3ac34928c7c1936416028152746f24b7437bb9c", "id": "dns.as<PERSON><PERSON>y", "ignore_all": true, "interface_hash": "e0a18a7e6f5ebedf2704eb49bc1bc7f055136720", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/dns/asyncquery.py", "plugin_data": null, "size": 30821, "suppressed": [], "version_id": "1.16.1"}