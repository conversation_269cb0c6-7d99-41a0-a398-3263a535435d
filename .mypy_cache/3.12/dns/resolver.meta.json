{"data_mtime": 1751559218, "dep_lines": [45, 28, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 20, 21, 22, 23, 24, 25, 26, 27, 30, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 20, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["dns.rdtypes.svcbbase", "urllib.parse", "dns._ddr", "dns.edns", "dns.exception", "dns.flags", "dns.inet", "dns.ipv4", "dns.ipv6", "dns.message", "dns.name", "dns.rdata", "dns.nameserver", "dns.query", "dns.rcode", "dns.rdataclass", "dns.rdatatype", "dns.rdtypes", "dns.reversename", "dns.tsig", "contextlib", "random", "socket", "sys", "threading", "time", "warnings", "typing", "dns", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_socket", "_thread", "_typeshed", "abc", "dns.enum", "dns.rdataset", "dns.rrset", "dns.set", "enum", "io", "os", "types"], "hash": "641479ce014a56aaa189796e92850a11f5368fc8", "id": "dns.resolver", "ignore_all": true, "interface_hash": "d4419a088437474c2968bccc29015680bbb7ebc7", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/dns/resolver.py", "plugin_data": null, "size": 73730, "suppressed": [], "version_id": "1.16.1"}