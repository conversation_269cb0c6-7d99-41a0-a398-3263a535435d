{"data_mtime": 1751559178, "dep_lines": [6, 7, 8, 9, 3, 4, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["dns.immutable", "dns.rdata", "dns.rdatatype", "dns.zonetypes", "<PERSON><PERSON><PERSON><PERSON>", "struct", "dns", "builtins", "_collections_abc", "_frozen_importlib", "_hashlib", "abc", "dns._immutable_ctx", "dns.enum", "enum", "typing"], "hash": "d75df59aeb51aeed7f96e3cb3f7ada7ab3aa60be", "id": "dns.rdtypes.ANY.ZONEMD", "ignore_all": true, "interface_hash": "d5da48ad3f938c41d4232c00a6f4ce0dadb0da1a", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/dns/rdtypes/ANY/ZONEMD.py", "plugin_data": null, "size": 2393, "suppressed": [], "version_id": "1.16.1"}