{".class": "MypyFile", "_fullname": "dns._ddr", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "_SVCBInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns._ddr._SVCBInfo", "name": "_SVCBInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns._ddr._SVCBInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns._ddr", "mro": ["dns._ddr._SVCBInfo", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "bootstrap_address", "port", "hostname", "nameservers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns._ddr._SVCBInfo.__init__", "name": "__init__", "type": null}}, "bootstrap_address": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns._ddr._SVCBInfo.bootstrap_address", "name": "bootstrap_address", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ddr_check_certificate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cert"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns._ddr._SVCBInfo.ddr_check_certificate", "name": "ddr_check_certificate", "type": null}}, "ddr_tls_check_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "lifetime", "backend"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns._ddr._SVCBInfo.ddr_tls_check_async", "name": "ddr_tls_check_async", "type": null}}, "ddr_tls_check_sync": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lifetime"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns._ddr._SVCBInfo.ddr_tls_check_sync", "name": "ddr_tls_check_sync", "type": null}}, "hostname": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns._ddr._SVCBInfo.hostname", "name": "hostname", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "make_tls_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns._ddr._SVCBInfo.make_tls_context", "name": "make_tls_context", "type": null}}, "nameservers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns._ddr._SVCBInfo.nameservers", "name": "nameservers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "port": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns._ddr._SVCBInfo.port", "name": "port", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns._ddr._SVCBInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns._ddr._SVCBInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns._ddr.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns._ddr.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns._ddr.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns._ddr.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns._ddr.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns._ddr.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_extract_nameservers_from_svcb": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["answer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns._ddr._extract_nameservers_from_svcb", "name": "_extract_nameservers_from_svcb", "type": null}}, "_get_nameservers_async": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["answer", "lifetime"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns._ddr._get_nameservers_async", "name": "_get_nameservers_async", "type": null}}, "_get_nameservers_sync": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["answer", "lifetime"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns._ddr._get_nameservers_sync", "name": "_get_nameservers_sync", "type": null}}, "_local_resolver_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "dns._ddr._local_resolver_name", "name": "_local_resolver_name", "setter_type": null, "type": "dns.name.Name"}}, "dns": {".class": "SymbolTableNode", "cross_ref": "dns", "kind": "Gdef"}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "urlparse": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlparse", "kind": "Gdef"}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/dns/_ddr.py"}