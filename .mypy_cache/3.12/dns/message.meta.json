{"data_mtime": 1751559211, "dep_lines": [37, 38, 37, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 20, 21, 22, 23, 24, 26, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 20, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 20, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["dns.rdtypes.ANY.OPT", "dns.rdtypes.ANY.TSIG", "dns.rdtypes.ANY", "dns.edns", "dns.entropy", "dns.enum", "dns.exception", "dns.flags", "dns.name", "dns.opcode", "dns.rcode", "dns.rdata", "dns.rdataclass", "dns.rdatatype", "dns.rdtypes", "dns.renderer", "dns.rrset", "dns.tsig", "dns.ttl", "dns.wire", "contextlib", "enum", "io", "time", "typing", "dns", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "dns.rdataset", "dns.set", "dns.tokenizer", "types"], "hash": "a4b5a09bdfc78eef21ac7b7c909960c29892b172", "id": "dns.message", "ignore_all": true, "interface_hash": "07a553fe9a13cc288871bb94da0d28a1e70f7e8d", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/dns/message.py", "plugin_data": null, "size": 68185, "suppressed": [], "version_id": "1.16.1"}