{".class": "MypyFile", "_fullname": "dns.query", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BadResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.exception.FormError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.query.BadResponse", "name": "BadResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.query.BadResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.query", "mro": ["dns.query.BadResponse", "dns.exception.FormError", "dns.exception.DNSException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.query.BadResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.query.BadResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "HTTPVersion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.query.HTTPVersion", "name": "HTTPVersion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "dns.query.HTTPVersion", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "dns.query", "mro": ["dns.query.HTTPVersion", "enum.IntEnum", "builtins.int", "enum.ReprEnum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "DEFAULT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "dns.query.HTTPVersion.DEFAULT", "name": "DEFAULT", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "H1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "dns.query.HTTPVersion.H1", "name": "H1", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "H2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "dns.query.HTTPVersion.H2", "name": "H2", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "H3": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "dns.query.HTTPVersion.H3", "name": "H3", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "HTTP_1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "dns.query.HTTPVersion.HTTP_1", "name": "HTTP_1", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "HTTP_2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "dns.query.HTTPVersion.HTTP_2", "name": "HTTP_2", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "HTTP_3": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "dns.query.HTTPVersion.HTTP_3", "name": "HTTP_3", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.query.HTTPVersion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.query.HTTPVersion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoDOH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.exception.DNSException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.query.NoDOH", "name": "NoDOH", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.query.NoDOH", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.query", "mro": ["dns.query.NoDOH", "dns.exception.DNSException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.query.NoDOH.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.query.NoDOH", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoDOQ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.exception.DNSException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.query.NoDOQ", "name": "NoDOQ", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.query.NoDOQ", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.query", "mro": ["dns.query.NoDOQ", "dns.exception.DNSException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.query.NoDOQ.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.query.NoDOQ", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "TransferError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "dns.query.TransferError", "line": 201, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "dns.xfr.<PERSON><PERSON>r"}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "UDPMode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.query.UDPMode", "name": "UDPMode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "dns.query.UDPMode", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "dns.query", "mro": ["dns.query.UDPMode", "enum.IntEnum", "builtins.int", "enum.ReprEnum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "NEVER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "dns.query.UDPMode.NEVER", "name": "NEVER", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "ONLY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "dns.query.UDPMode.ONLY", "name": "ONLY", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "TRY_FIRST": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "dns.query.UDPMode.TRY_FIRST", "name": "TRY_FIRST", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.query.UDPMode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.query.UDPMode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnexpectedSource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.exception.DNSException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.query.UnexpectedSource", "name": "UnexpectedSource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.query.UnexpectedSource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.query", "mro": ["dns.query.UnexpectedSource", "dns.exception.DNSException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.query.UnexpectedSource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.query.UnexpectedSource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_CoreNetworkBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "dns.query._CoreNetworkBackend", "line": 69, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "httpcore._backends.base.NetworkBackend"}}, "_CoreSyncStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "dns.query._CoreSyncStream", "line": 70, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "httpcore._backends.sync.SyncStream"}}, "_HTTPTransport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpx._transports.default.HTTPTransport"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.query._HTTPTransport", "name": "_HTTPTransport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.query._HTTPTransport", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.query", "mro": ["dns.query._HTTPTransport", "httpx._transports.default.HTTPTransport", "httpx._transports.base.BaseTransport", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "local_port", "bootstrap_address", "resolver", "family", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._HTTPTransport.__init__", "name": "__init__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.query._HTTPTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.query._HTTPTransport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_NetworkBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["httpcore._backends.base.NetworkBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.query._NetworkBackend", "name": "_NetworkBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.query._NetworkBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.query", "mro": ["dns.query._NetworkBackend", "httpcore._backends.base.NetworkBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "resolver", "local_port", "bootstrap_address", "family"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._NetworkBackend.__init__", "name": "__init__", "type": null}}, "_bootstrap_address": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.query._NetworkBackend._bootstrap_address", "name": "_bootstrap_address", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_family": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.query._NetworkBackend._family", "name": "_family", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_local_port": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.query._NetworkBackend._local_port", "name": "_local_port", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_resolver": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.query._NetworkBackend._resolver", "name": "_resolver", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "connect_tcp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "host", "port", "timeout", "local_address", "socket_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._NetworkBackend.connect_tcp", "name": "connect_tcp", "type": null}}, "connect_unix_socket": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "path", "timeout", "socket_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._NetworkBackend.connect_unix_socket", "name": "connect_unix_socket", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.query._NetworkBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.query._NetworkBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.query.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.query.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.query.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.query.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.query.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.query.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_addresses_equal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["af", "a1", "a2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._addresses_equal", "name": "_addresses_equal", "type": null}}, "_check_status": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["headers", "peer", "wire"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._check_status", "name": "_check_status", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["headers", "peer", "wire"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "dns.quic.Headers"}, "builtins.str", "builtins.bytes"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_check_status", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_times": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._compute_times", "name": "_compute_times", "type": null}}, "_connect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["s", "address", "expiration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._connect", "name": "_connect", "type": null}}, "_destination_and_source": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["where", "port", "source", "source_port", "where_must_be_address"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._destination_and_source", "name": "_destination_and_source", "type": null}}, "_expiration_for_this_attempt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["timeout", "expiration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._expiration_for_this_attempt", "name": "_expiration_for_this_attempt", "type": null}}, "_find_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["headers", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._find_header", "name": "_find_header", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["headers", "name"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "dns.quic.Headers"}, "builtins.bytes"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_find_header", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_have_httpx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "dns.query._have_httpx", "name": "_have_httpx", "setter_type": null, "type": "builtins.bool"}}, "_http3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["q", "where", "url", "timeout", "port", "source", "source_port", "one_rr_per_rrset", "ignore_trailing", "verify", "hostname", "post"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._http3", "name": "_http3", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["q", "where", "url", "timeout", "port", "source", "source_port", "one_rr_per_rrset", "ignore_trailing", "verify", "hostname", "post"], "arg_types": ["dns.message.Message", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_http3", "ret_type": "dns.message.Message", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_inbound_xfr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["txn_manager", "s", "query", "serial", "timeout", "expiration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._inbound_xfr", "name": "_inbound_xfr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["txn_manager", "s", "query", "serial", "timeout", "expiration"], "arg_types": ["dns.transaction.TransactionManager", "socket.socket", "dns.message.Message", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_inbound_xfr", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_make_dot_ssl_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["server_hostname", "verify"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._make_dot_ssl_context", "name": "_make_dot_ssl_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["server_hostname", "verify"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_make_dot_ssl_context", "ret_type": "ssl.SSLContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_make_socket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["af", "type", "source", "ssl_context", "server_hostname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._make_socket", "name": "_make_socket", "type": null}}, "_matches_destination": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["af", "from_address", "destination", "ignore_unexpected"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._matches_destination", "name": "_matches_destination", "type": null}}, "_maybe_get_resolver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["resolver"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._maybe_get_resolver", "name": "_maybe_get_resolver", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["resolver"], "arg_types": [{".class": "UnionType", "items": ["dns.resolver.Resolver", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_maybe_get_resolver", "ret_type": "dns.resolver.Resolver", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_net_read": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["sock", "count", "expiration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._net_read", "name": "_net_read", "type": null}}, "_net_write": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["sock", "data", "expiration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._net_write", "name": "_net_write", "type": null}}, "_remaining": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["expiration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._remaining", "name": "_remaining", "type": null}}, "_tls_handshake": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["s", "expiration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._tls_handshake", "name": "_tls_handshake", "type": null}}, "_udp_recv": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["sock", "max_size", "expiration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._udp_recv", "name": "_udp_recv", "type": null}}, "_udp_send": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["sock", "data", "destination", "expiration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._udp_send", "name": "_udp_send", "type": null}}, "_wait_for": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["fd", "readable", "writable", "_", "expiration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._wait_for", "name": "_wait_for", "type": null}}, "_wait_for_readable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["s", "expiration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._wait_for_readable", "name": "_wait_for_readable", "type": null}}, "_wait_for_writable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["s", "expiration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query._wait_for_writable", "name": "_wait_for_writable", "type": null}}, "base64": {".class": "SymbolTableNode", "cross_ref": "base64", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "dns": {".class": "SymbolTableNode", "cross_ref": "dns", "kind": "Gdef"}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef"}, "errno": {".class": "SymbolTableNode", "cross_ref": "errno", "kind": "Gdef"}, "have_doh": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "dns.query.have_doh", "name": "have_doh", "setter_type": null, "type": "builtins.bool"}}, "httpcore": {".class": "SymbolTableNode", "cross_ref": "httpcore", "kind": "Gdef"}, "https": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["q", "where", "timeout", "port", "source", "source_port", "one_rr_per_rrset", "ignore_trailing", "session", "path", "post", "bootstrap_address", "verify", "resolver", "family", "http_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query.https", "name": "https", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["q", "where", "timeout", "port", "source", "source_port", "one_rr_per_rrset", "ignore_trailing", "session", "path", "post", "bootstrap_address", "verify", "resolver", "family", "http_version"], "arg_types": ["dns.message.Message", "builtins.str", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.resolver.Resolver", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "dns.query.HTTPVersion"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "https", "ret_type": "dns.message.Message", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "httpx": {".class": "SymbolTableNode", "cross_ref": "httpx", "kind": "Gdef"}, "inbound_xfr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["where", "txn_manager", "query", "port", "timeout", "lifetime", "source", "source_port", "udp_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query.inbound_xfr", "name": "inbound_xfr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["where", "txn_manager", "query", "port", "timeout", "lifetime", "source", "source_port", "udp_mode"], "arg_types": ["builtins.str", "dns.transaction.TransactionManager", {".class": "UnionType", "items": ["dns.message.Message", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "dns.query.UDPMode"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "inbound_xfr", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "quic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["q", "where", "timeout", "port", "source", "source_port", "one_rr_per_rrset", "ignore_trailing", "connection", "verify", "hostname", "server_hostname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query.quic", "name": "quic", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["q", "where", "timeout", "port", "source", "source_port", "one_rr_per_rrset", "ignore_trailing", "connection", "verify", "hostname", "server_hostname"], "arg_types": ["dns.message.Message", "builtins.str", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["dns.quic._sync.SyncQuicConnection", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "quic", "ret_type": "dns.message.Message", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "receive_tcp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["sock", "expiration", "one_rr_per_rrset", "keyring", "request_mac", "ignore_trailing"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query.receive_tcp", "name": "receive_tcp", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["sock", "expiration", "one_rr_per_rrset", "keyring", "request_mac", "ignore_trailing"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["dns.name.Name", "dns.tsig.Key"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "receive_tcp", "ret_type": {".class": "TupleType", "implicit": false, "items": ["dns.message.Message", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "receive_udp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["sock", "destination", "expiration", "ignore_unexpected", "one_rr_per_rrset", "keyring", "request_mac", "ignore_trailing", "raise_on_truncation", "ignore_errors", "query"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query.receive_udp", "name": "receive_udp", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["sock", "destination", "expiration", "ignore_unexpected", "one_rr_per_rrset", "keyring", "request_mac", "ignore_trailing", "raise_on_truncation", "ignore_errors", "query"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["dns.name.Name", "dns.tsig.Key"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["dns.message.Message", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "receive_udp", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "selectors": {".class": "SymbolTableNode", "cross_ref": "selectors", "kind": "Gdef"}, "send_tcp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["sock", "what", "expiration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query.send_tcp", "name": "send_tcp", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["sock", "what", "expiration"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["dns.message.Message", "builtins.bytes"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send_tcp", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_udp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["sock", "what", "destination", "expiration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query.send_udp", "name": "send_udp", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["sock", "what", "destination", "expiration"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["dns.message.Message", "builtins.bytes"], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send_udp", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef"}, "socket_factory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "dns.query.socket_factory", "line": 179, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "socket.socket"}}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef"}, "struct": {".class": "SymbolTableNode", "cross_ref": "struct", "kind": "Gdef"}, "tcp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["q", "where", "timeout", "port", "source", "source_port", "one_rr_per_rrset", "ignore_trailing", "sock"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query.tcp", "name": "tcp", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["q", "where", "timeout", "port", "source", "source_port", "one_rr_per_rrset", "ignore_trailing", "sock"], "arg_types": ["dns.message.Message", "builtins.str", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "tcp", "ret_type": "dns.message.Message", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "tls": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["q", "where", "timeout", "port", "source", "source_port", "one_rr_per_rrset", "ignore_trailing", "sock", "ssl_context", "server_hostname", "verify"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query.tls", "name": "tls", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["q", "where", "timeout", "port", "source", "source_port", "one_rr_per_rrset", "ignore_trailing", "sock", "ssl_context", "server_hostname", "verify"], "arg_types": ["dns.message.Message", "builtins.str", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["ssl.SSLSocket", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "tls", "ret_type": "dns.message.Message", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "udp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["q", "where", "timeout", "port", "source", "source_port", "ignore_unexpected", "one_rr_per_rrset", "ignore_trailing", "raise_on_truncation", "sock", "ignore_errors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query.udp", "name": "udp", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["q", "where", "timeout", "port", "source", "source_port", "ignore_unexpected", "one_rr_per_rrset", "ignore_trailing", "raise_on_truncation", "sock", "ignore_errors"], "arg_types": ["dns.message.Message", "builtins.str", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "udp", "ret_type": "dns.message.Message", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "udp_with_fallback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["q", "where", "timeout", "port", "source", "source_port", "ignore_unexpected", "one_rr_per_rrset", "ignore_trailing", "udp_sock", "tcp_sock", "ignore_errors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query.udp_with_fallback", "name": "udp_with_fallback", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["q", "where", "timeout", "port", "source", "source_port", "ignore_unexpected", "one_rr_per_rrset", "ignore_trailing", "udp_sock", "tcp_sock", "ignore_errors"], "arg_types": ["dns.message.Message", "builtins.str", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "udp_with_fallback", "ret_type": {".class": "TupleType", "implicit": false, "items": ["dns.message.Message", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "urllib": {".class": "SymbolTableNode", "cross_ref": "urllib", "kind": "Gdef"}, "xfr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["where", "zone", "rdtype", "rdclass", "timeout", "port", "keyring", "keyname", "relativize", "lifetime", "source", "source_port", "serial", "use_udp", "keyalgorithm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.query.xfr", "name": "xfr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["where", "zone", "rdtype", "rdclass", "timeout", "port", "keyring", "keyname", "relativize", "lifetime", "source", "source_port", "serial", "use_udp", "keyalgorithm"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["dns.name.Name", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.rdatatype.RdataType", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.rdataclass.RdataClass", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["dns.name.Name", "dns.tsig.Key"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.name.Name", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["dns.name.Name", "builtins.str"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "xfr", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/dns/query.py"}