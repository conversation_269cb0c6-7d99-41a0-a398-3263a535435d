{".class": "MypyFile", "_fullname": "dns.rdataset", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DifferingCovers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.exception.DNSException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.rdataset.DifferingCovers", "name": "DifferingCovers", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.rdataset.DifferingCovers", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.rdataset", "mro": ["dns.rdataset.DifferingCovers", "dns.exception.DNSException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.rdataset.DifferingCovers.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.rdataset.DifferingCovers", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImmutableRdataset": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.rdataset.Rdataset"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.rdataset.ImmutableRdataset", "name": "ImmutableRdataset", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.rdataset.ImmutableRdataset", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.rdataset", "mro": ["dns.rdataset.ImmutableRdataset", "dns.rdataset.Rdataset", "dns.set.Set", "builtins.object"], "names": {".class": "SymbolTable", "__copy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.ImmutableRdataset.__copy__", "name": "__copy__", "type": null}}, "__delitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.ImmutableRdataset.__delitem__", "name": "__delitem__", "type": null}}, "__iadd__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.ImmutableRdataset.__iadd__", "name": "__iadd__", "type": null}}, "__iand__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.ImmutableRdataset.__iand__", "name": "__iand__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "rdataset"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.rdataset.ImmutableRdataset.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "rdataset"], "arg_types": ["dns.rdataset.ImmutableRdataset", "dns.rdataset.Rdataset"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ImmutableRdataset", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__ior__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.ImmutableRdataset.__ior__", "name": "__ior__", "type": null}}, "__isub__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.ImmutableRdataset.__isub__", "name": "__isub__", "type": null}}, "_clone_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "dns.rdataset.ImmutableRdataset._clone_class", "name": "_clone_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["rdclass", "rdtype", "covers", "ttl"], "arg_types": ["dns.rdataclass.RdataClass", "dns.rdatatype.RdataType", "dns.rdatatype.RdataType", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": "dns.rdataset.Rdataset", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "rd", "ttl"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.ImmutableRdataset.add", "name": "add", "type": null}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.ImmutableRdataset.clear", "name": "clear", "type": null}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.ImmutableRdataset.copy", "name": "copy", "type": null}}, "difference": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.ImmutableRdataset.difference", "name": "difference", "type": null}}, "intersection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.ImmutableRdataset.intersection", "name": "intersection", "type": null}}, "intersection_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.ImmutableRdataset.intersection_update", "name": "intersection_update", "type": null}}, "symmetric_difference": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.ImmutableRdataset.symmetric_difference", "name": "symmetric_difference", "type": null}}, "union": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.ImmutableRdataset.union", "name": "union", "type": null}}, "union_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.ImmutableRdataset.union_update", "name": "union_update", "type": null}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.ImmutableRdataset.update", "name": "update", "type": null}}, "update_ttl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ttl"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.ImmutableRdataset.update_ttl", "name": "update_ttl", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.rdataset.ImmutableRdataset.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.rdataset.ImmutableRdataset", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IncompatibleTypes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.exception.DNSException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.rdataset.IncompatibleTypes", "name": "IncompatibleTypes", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.rdataset.IncompatibleTypes", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.rdataset", "mro": ["dns.rdataset.IncompatibleTypes", "dns.exception.DNSException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.rdataset.IncompatibleTypes.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.rdataset.IncompatibleTypes", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Rdataset": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.set.Set"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.rdataset.Rdataset", "name": "Rdataset", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.rdataset.Rdataset", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.rdataset", "mro": ["dns.rdataset.Rdataset", "dns.set.Set", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.Rdataset.__eq__", "name": "__eq__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "rdclass", "rdtype", "covers", "ttl"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.rdataset.Rdataset.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "rdclass", "rdtype", "covers", "ttl"], "arg_types": ["dns.rdataset.Rdataset", "dns.rdataclass.RdataClass", "dns.rdatatype.RdataType", "dns.rdatatype.RdataType", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Rdataset", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__ne__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.Rdataset.__ne__", "name": "__ne__", "type": null}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.Rdataset.__repr__", "name": "__repr__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "dns.rdataset.Rdataset.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.Rdataset.__str__", "name": "__str__", "type": null}}, "_clone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.Rdataset._clone", "name": "_clone", "type": null}}, "_rdata_repr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.Rdataset._rdata_repr", "name": "_rdata_repr", "type": null}}, "add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "rd", "ttl"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.rdataset.Rdataset.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "rd", "ttl"], "arg_types": ["dns.rdataset.Rdataset", "dns.rdata.Rdata", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add of Rdataset", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "covers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "dns.rdataset.Rdataset.covers", "name": "covers", "setter_type": null, "type": "dns.rdatatype.RdataType"}}, "intersection_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.Rdataset.intersection_update", "name": "intersection_update", "type": null}}, "match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "rdclass", "rdtype", "covers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.rdataset.Rdataset.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "rdclass", "rdtype", "covers"], "arg_types": ["dns.rdataset.Rdataset", "dns.rdataclass.RdataClass", "dns.rdatatype.RdataType", "dns.rdatatype.RdataType"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "match of Rdataset", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "processing_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.rdataset.Rdataset.processing_order", "name": "processing_order", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["dns.rdataset.Rdataset"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "processing_order of Rdataset", "ret_type": {".class": "Instance", "args": ["dns.rdata.Rdata"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rdclass": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.rdataset.Rdataset.rdclass", "name": "rdclass", "setter_type": null, "type": "dns.rdataclass.RdataClass"}}, "rdtype": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "dns.rdataset.Rdataset.rdtype", "name": "rdtype", "setter_type": null, "type": "dns.rdatatype.RdataType"}}, "to_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "name", "origin", "relativize", "override_rdclass", "want_comments", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.rdataset.Rdataset.to_text", "name": "to_text", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "name", "origin", "relativize", "override_rdclass", "want_comments", "kw"], "arg_types": ["dns.rdataset.Rdataset", {".class": "UnionType", "items": ["dns.name.Name", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.name.Name", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["dns.rdataclass.RdataClass", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_text of Rdataset", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_wire": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "name", "file", "compress", "origin", "override_rdclass", "want_shuffle"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.rdataset.Rdataset.to_wire", "name": "to_wire", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "name", "file", "compress", "origin", "override_rdclass", "want_shuffle"], "arg_types": ["dns.rdataset.Rdataset", "dns.name.Name", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "dns.name.CompressType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.name.Name", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.rdataclass.RdataClass", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_wire of Rdataset", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ttl": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.rdataset.Rdataset.ttl", "name": "ttl", "setter_type": null, "type": "builtins.int"}}, "union_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.Rdataset.union_update", "name": "union_update", "type": null}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.Rdataset.update", "name": "update", "type": null}}, "update_ttl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ttl"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "dns.rdataset.Rdataset.update_ttl", "name": "update_ttl", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ttl"], "arg_types": ["dns.rdataset.Rdataset", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_ttl of Rdataset", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.rdataset.Rdataset.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.rdataset.Rdataset", "values": [], "variance": 0}, "slots": ["covers", "items", "rdclass", "rdtype", "ttl"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SimpleSet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "dns.rdataset.SimpleSet", "line": 36, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "dns.set.Set"}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.rdataset.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.rdataset.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.rdataset.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.rdataset.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.rdataset.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.rdataset.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "dns": {".class": "SymbolTableNode", "cross_ref": "dns", "kind": "Gdef"}, "from_rdata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["ttl", "rdatas"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.from_rdata", "name": "from_rdata", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["ttl", "rdatas"], "arg_types": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_rdata", "ret_type": "dns.rdataset.Rdataset", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_rdata_list": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["ttl", "rdatas"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.from_rdata_list", "name": "from_rdata_list", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ttl", "rdatas"], "arg_types": ["builtins.int", {".class": "Instance", "args": ["dns.rdata.Rdata"], "extra_attrs": null, "type_ref": "typing.Collection"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_rdata_list", "ret_type": "dns.rdataset.Rdataset", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_text": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2], "arg_names": ["rdclass", "rdtype", "ttl", "text_rdatas"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.from_text", "name": "from_text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2], "arg_names": ["rdclass", "rdtype", "ttl", "text_rdatas"], "arg_types": [{".class": "UnionType", "items": ["dns.rdataclass.RdataClass", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.rdatatype.RdataType", "builtins.str"], "uses_pep604_syntax": false}, "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_text", "ret_type": "dns.rdataset.Rdataset", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_text_list": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["rdclass", "rdtype", "ttl", "text_rdatas", "idna_codec", "origin", "relativize", "relativize_to"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdataset.from_text_list", "name": "from_text_list", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["rdclass", "rdtype", "ttl", "text_rdatas", "idna_codec", "origin", "relativize", "relativize_to"], "arg_types": [{".class": "UnionType", "items": ["dns.rdataclass.RdataClass", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.rdatatype.RdataType", "builtins.str"], "uses_pep604_syntax": false}, "builtins.int", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Collection"}, {".class": "UnionType", "items": ["dns.name.IDNACodec", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.name.Name", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["dns.name.Name", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_text_list", "ret_type": "dns.rdataset.Rdataset", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "struct": {".class": "SymbolTableNode", "cross_ref": "struct", "kind": "Gdef"}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/dns/rdataset.py"}