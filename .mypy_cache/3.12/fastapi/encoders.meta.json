{"data_mtime": 1751559223, "dep_lines": [20, 22, 23, 24, 27, 1, 2, 3, 4, 5, 6, 14, 15, 16, 17, 18, 21, 25, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["fastapi.types", "pydantic.color", "pydantic.networks", "pydantic.types", "fastapi._compat", "dataclasses", "datetime", "collections", "decimal", "enum", "ipaddress", "pathlib", "re", "types", "typing", "uuid", "pydantic", "typing_extensions", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "os", "pydantic._internal", "pydantic._internal._repr", "pydantic_core", "pydantic_core._pydantic_core"], "hash": "cf14073db7680b3bd1b0bcb0ed3e80a21af9a10d", "id": "fastapi.encoders", "ignore_all": true, "interface_hash": "4db95a18ba0d2ed27e635947960db4bbd48ec5f2", "mtime": 1747044683, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/fastapi/encoders.py", "plugin_data": null, "size": 11068, "suppressed": [], "version_id": "1.16.1"}