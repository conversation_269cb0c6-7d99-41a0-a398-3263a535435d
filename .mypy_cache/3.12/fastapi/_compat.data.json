{".class": "MypyFile", "_fullname": "fastapi._compat", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Annotated", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "fastapi._compat.BaseConfig", "name": "BaseConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "fastapi._compat.BaseConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "fastapi._compat", "mro": ["fastapi._compat.BaseConfig", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi._compat.BaseConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fastapi._compat.BaseConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CoreSchema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema.CoreSchema", "kind": "Gdef"}, "Deque": {".class": "SymbolTableNode", "cross_ref": "typing.Deque", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "ErrorWrapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "fastapi._compat.ErrorWrapper", "name": "ErrorWrapper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "fastapi._compat.ErrorWrapper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "fastapi._compat", "mro": ["fastapi._compat.ErrorWrapper", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi._compat.ErrorWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fastapi._compat.ErrorWrapper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FieldInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.FieldInfo", "kind": "Gdef"}, "FrozenSet": {".class": "SymbolTableNode", "cross_ref": "typing.FrozenSet", "kind": "Gdef"}, "GenerateJsonSchema": {".class": "SymbolTableNode", "cross_ref": "pydantic.json_schema.GenerateJsonSchema", "kind": "Gdef"}, "GetJsonSchemaHandler": {".class": "SymbolTableNode", "cross_ref": "pydantic.annotated_handlers.GetJsonSchemaHandler", "kind": "Gdef"}, "IncEx": {".class": "SymbolTableNode", "cross_ref": "fastapi.types.IncEx", "kind": "Gdef"}, "JsonSchemaValue": {".class": "SymbolTableNode", "cross_ref": "pydantic.json_schema.JsonSchemaValue", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "MissingError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "from_module_getattr"], "fullname": "pydantic.errors.Missing<PERSON><PERSON>r", "name": "Missing<PERSON><PERSON><PERSON>", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "ModelField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "fastapi._compat.ModelField", "name": "ModelField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "fastapi._compat.ModelField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 8, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 89, "name": "field_info", "type": "pydantic.fields.FieldInfo"}, {"alias": null, "column": 8, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 90, "name": "name", "type": "builtins.str"}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 91, "name": "mode", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "fastapi._compat", "mro": ["fastapi._compat.ModelField", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "fastapi._compat.ModelField.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "fastapi._compat.ModelField.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["fastapi._compat.ModelField"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__hash__ of ModelField", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "field_info", "name", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.ModelField.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "field_info", "name", "mode"], "arg_types": ["fastapi._compat.ModelField", "pydantic.fields.FieldInfo", "builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ModelField", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "fastapi._compat.ModelField.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "field_info"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mode"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-post_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.ModelField.__mypy-post_init", "name": "__mypy-post_init", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["fastapi._compat.ModelField"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-post_init of ModelField", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["field_info", "name", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "fastapi._compat.ModelField.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["field_info", "name", "mode"], "arg_types": ["pydantic.fields.FieldInfo", "builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "fastapi._compat.ModelField.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["field_info", "name", "mode"], "arg_types": ["pydantic.fields.FieldInfo", "builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__post_init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "fastapi._compat.ModelField.__post_init__", "name": "__post_init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["fastapi._compat.ModelField"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__post_init__ of ModelField", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_type_adapter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "fastapi._compat.ModelField._type_adapter", "name": "_type_adapter", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "pydantic.type_adapter.TypeAdapter"}}}, "alias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "fastapi._compat.ModelField.alias", "name": "alias", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["fastapi._compat.ModelField"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "alias of <PERSON><PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "fastapi._compat.ModelField.alias", "name": "alias", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["fastapi._compat.ModelField"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "alias of <PERSON><PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "fastapi._compat.ModelField.default", "name": "default", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["fastapi._compat.ModelField"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "default of ModelField", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "fastapi._compat.ModelField.default", "name": "default", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["fastapi._compat.ModelField"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "default of ModelField", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "field_info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "fastapi._compat.ModelField.field_info", "name": "field_info", "setter_type": null, "type": "pydantic.fields.FieldInfo"}}, "get_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "fastapi._compat.ModelField.get_default", "name": "get_default", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["fastapi._compat.ModelField"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_default of ModelField", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "fastapi._compat.ModelField.mode", "name": "mode", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}], "uses_pep604_syntax": false}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "fastapi._compat.ModelField.name", "name": "name", "setter_type": null, "type": "builtins.str"}}, "required": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "fastapi._compat.ModelField.required", "name": "required", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["fastapi._compat.ModelField"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "required of ModelField", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "fastapi._compat.ModelField.required", "name": "required", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["fastapi._compat.ModelField"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "required of ModelField", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "serialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "value", "mode", "include", "exclude", "by_alias", "exclude_unset", "exclude_defaults", "exclude_none"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "fastapi._compat.ModelField.serialize", "name": "serialize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "value", "mode", "include", "exclude", "by_alias", "exclude_unset", "exclude_defaults", "exclude_none"], "arg_types": ["fastapi._compat.ModelField", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "json"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "python"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "fastapi.types.IncEx"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "fastapi.types.IncEx"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "serialize of ModelField", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "type_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "fastapi._compat.ModelField.type_", "name": "type_", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["fastapi._compat.ModelField"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "type_ of ModelField", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "fastapi._compat.ModelField.type_", "name": "type_", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["fastapi._compat.ModelField"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "type_ of ModelField", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "validate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "value", "values", "loc"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "fastapi._compat.ModelField.validate", "name": "validate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "value", "values", "loc"], "arg_types": ["fastapi._compat.ModelField", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "validate of ModelField", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi._compat.ModelField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fastapi._compat.ModelField", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ModelNameMap": {".class": "SymbolTableNode", "cross_ref": "fastapi.types.ModelNameMap", "kind": "Gdef"}, "PYDANTIC_V2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "fastapi._compat.PYDANTIC_V2", "name": "PYDANTIC_V2", "setter_type": null, "type": "builtins.bool"}}, "PYDANTIC_VERSION": {".class": "SymbolTableNode", "cross_ref": "pydantic.version.VERSION", "kind": "Gdef"}, "PYDANTIC_VERSION_MINOR_TUPLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "fastapi._compat.PYDANTIC_VERSION_MINOR_TUPLE", "name": "PYDANTIC_VERSION_MINOR_TUPLE", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "PydanticSchemaGenerationError": {".class": "SymbolTableNode", "cross_ref": "pydantic.errors.PydanticSchemaGenerationError", "kind": "Gdef"}, "PydanticUndefined": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticUndefined", "kind": "Gdef"}, "PydanticUndefinedType": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticUndefinedType", "kind": "Gdef"}, "REF_PREFIX": {".class": "SymbolTableNode", "cross_ref": "fastapi.openapi.constants.REF_PREFIX", "kind": "Gdef"}, "RequestErrorModel": {".class": "SymbolTableNode", "cross_ref": "fastapi.exceptions.RequestErrorModel", "kind": "Gdef"}, "RequiredParam": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "fastapi._compat.RequiredParam", "name": "RequiredParam", "setter_type": null, "type": "pydantic_core._pydantic_core.PydanticUndefinedType"}}, "SHAPE_FROZENSET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "fastapi._compat.SHAPE_FROZENSET", "name": "SHAPE_FROZENSET", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "fastapi._compat.SHAPE_FROZENSET", "source_any": null, "type_of_any": 3}}}, "SHAPE_LIST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "fastapi._compat.SHAPE_LIST", "name": "SHAPE_LIST", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "fastapi._compat.SHAPE_LIST", "source_any": null, "type_of_any": 3}}}, "SHAPE_SEQUENCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "fastapi._compat.SHAPE_SEQUENCE", "name": "SHAPE_SEQUENCE", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "fastapi._compat.SHAPE_SEQUENCE", "source_any": null, "type_of_any": 3}}}, "SHAPE_SET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "fastapi._compat.SHAPE_SET", "name": "SHAPE_SET", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "fastapi._compat.SHAPE_SET", "source_any": null, "type_of_any": 3}}}, "SHAPE_SINGLETON": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "fastapi._compat.SHAPE_SINGLETON", "name": "SHAPE_SINGLETON", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "fastapi._compat.SHAPE_SINGLETON", "source_any": null, "type_of_any": 3}}}, "SHAPE_TUPLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "fastapi._compat.SHAPE_TUPLE", "name": "SHAPE_TUPLE", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "fastapi._compat.SHAPE_TUPLE", "source_any": null, "type_of_any": 3}}}, "SHAPE_TUPLE_ELLIPSIS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "fastapi._compat.SHAPE_TUPLE_ELLIPSIS", "name": "SHAPE_TUPLE_ELLIPSIS", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "fastapi._compat.SHAPE_TUPLE_ELLIPSIS", "source_any": null, "type_of_any": 3}}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeAdapter": {".class": "SymbolTableNode", "cross_ref": "pydantic.type_adapter.TypeAdapter", "kind": "Gdef"}, "Undefined": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "fastapi._compat.Undefined", "name": "Undefined", "setter_type": null, "type": "pydantic_core._pydantic_core.PydanticUndefinedType"}}, "UndefinedType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "fastapi._compat.UndefinedType", "line": 77, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "pydantic_core._pydantic_core.PydanticUndefinedType"}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UnionType": {".class": "SymbolTableNode", "cross_ref": "fastapi.types.UnionType", "kind": "Gdef"}, "UploadFile": {".class": "SymbolTableNode", "cross_ref": "starlette.datastructures.UploadFile", "kind": "Gdef"}, "Url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi._compat.Url", "name": "Url", "setter_type": null, "type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "ValidationError": {".class": "SymbolTableNode", "cross_ref": "pydantic.ValidationError", "kind": "Gdef"}, "Validator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "fastapi._compat.Validator", "line": 79, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi._compat.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi._compat.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi._compat.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi._compat.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi._compat.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi._compat.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_annotation_is_complex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["annotation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat._annotation_is_complex", "name": "_annotation_is_complex", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["annotation"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_annotation_is_complex", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_annotation_is_sequence": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["annotation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat._annotation_is_sequence", "name": "_annotation_is_sequence", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["annotation"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_annotation_is_sequence", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_model_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat._get_model_config", "name": "_get_model_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["model"], "arg_types": ["pydantic.main.BaseModel"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_model_config", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_model_dump": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["model", "mode", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat._model_dump", "name": "_model_dump", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["model", "mode", "kwargs"], "arg_types": ["pydantic.main.BaseModel", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "json"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "python"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_model_dump", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_model_rebuild": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat._model_rebuild", "name": "_model_rebuild", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["model"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_model_rebuild", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_normalize_errors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["errors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat._normalize_errors", "name": "_normalize_errors", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["errors"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_normalize_errors", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_regenerate_error_with_loc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3], "arg_names": ["errors", "loc_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat._regenerate_error_with_loc", "name": "_regenerate_error_with_loc", "type": {".class": "CallableType", "arg_kinds": [3, 3], "arg_names": ["errors", "loc_prefix"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_regenerate_error_with_loc", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy.copy", "kind": "Gdef"}, "copy_field_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3], "arg_names": ["field_info", "annotation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.copy_field_info", "name": "copy_field_info", "type": {".class": "CallableType", "arg_kinds": [3, 3], "arg_names": ["field_info", "annotation"], "arg_types": ["pydantic.fields.FieldInfo", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "copy_field_info", "ret_type": "pydantic.fields.FieldInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_body_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3], "arg_names": ["fields", "model_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.create_body_model", "name": "create_body_model", "type": {".class": "CallableType", "arg_kinds": [3, 3], "arg_names": ["fields", "model_name"], "arg_types": [{".class": "Instance", "args": ["fastapi._compat.ModelField"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_body_model", "ret_type": {".class": "TypeType", "item": "pydantic.main.BaseModel"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_model": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.create_model", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "deque": {".class": "SymbolTableNode", "cross_ref": "collections.deque", "kind": "Gdef"}, "eval_type_lenient": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._typing_extra.eval_type_lenient", "kind": "Gdef"}, "evaluate_forwardref": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "fastapi._compat.evaluate_forwardref", "name": "evaluate_forwardref", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["value", "globalns", "localns"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.GlobalsNamespace"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "field_annotation_is_complex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["annotation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.field_annotation_is_complex", "name": "field_annotation_is_complex", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["annotation"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_annotation_is_complex", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "field_annotation_is_scalar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["annotation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.field_annotation_is_scalar", "name": "field_annotation_is_scalar", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["annotation"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_annotation_is_scalar", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "field_annotation_is_scalar_sequence": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["annotation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.field_annotation_is_scalar_sequence", "name": "field_annotation_is_scalar_sequence", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["annotation"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_annotation_is_scalar_sequence", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "field_annotation_is_sequence": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["annotation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.field_annotation_is_sequence", "name": "field_annotation_is_sequence", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["annotation"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_annotation_is_sequence", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "field_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "from_module_getattr"], "fullname": "pydantic.schema.field_schema", "name": "field_schema", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "get_annotation_from_field_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["annotation", "field_info", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.get_annotation_from_field_info", "name": "get_annotation_from_field_info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["annotation", "field_info", "field_name"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "pydantic.fields.FieldInfo", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_annotation_from_field_info", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_args": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.get_args", "kind": "Gdef"}, "get_cached_model_fields": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "fastapi._compat.get_cached_model_fields", "name": "get_cached_model_fields", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["model"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_cached_model_fields", "ret_type": {".class": "Instance", "args": ["fastapi._compat.ModelField"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fastapi._compat.get_cached_model_fields", "name": "get_cached_model_fields", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["fastapi._compat.ModelField"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "get_compat_model_name_map": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["fields"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.get_compat_model_name_map", "name": "get_compat_model_name_map", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["fields"], "arg_types": [{".class": "Instance", "args": ["fastapi._compat.ModelField"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_compat_model_name_map", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "fastapi.types.ModelNameMap"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_definitions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3, 3, 5], "arg_names": ["fields", "schema_generator", "model_name_map", "separate_input_output_schemas"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.get_definitions", "name": "get_definitions", "type": {".class": "CallableType", "arg_kinds": [3, 3, 3, 5], "arg_names": ["fields", "schema_generator", "model_name_map", "separate_input_output_schemas"], "arg_types": [{".class": "Instance", "args": ["fastapi._compat.ModelField"], "extra_attrs": null, "type_ref": "builtins.list"}, "pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "fastapi.types.ModelNameMap"}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_definitions", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["fastapi._compat.ModelField", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_flat_models_from_fields": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "from_module_getattr"], "fullname": "pydantic.schema.get_flat_models_from_fields", "name": "get_flat_models_from_fields", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "get_missing_field_error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["loc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.get_missing_field_error", "name": "get_missing_field_error", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["loc"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_missing_field_error", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_model_definitions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3], "arg_names": ["flat_models", "model_name_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.get_model_definitions", "name": "get_model_definitions", "type": {".class": "CallableType", "arg_kinds": [3, 3], "arg_names": ["flat_models", "model_name_map"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "TypeType", "item": "enum.Enum"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "TypeType", "item": "enum.Enum"}], "uses_pep604_syntax": false}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_model_definitions", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_model_fields": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.get_model_fields", "name": "get_model_fields", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["model"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_model_fields", "ret_type": {".class": "Instance", "args": ["fastapi._compat.ModelField"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_model_name_map": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "from_module_getattr"], "fullname": "pydantic.schema.get_model_name_map", "name": "get_model_name_map", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "get_origin": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.get_origin", "kind": "Gdef"}, "get_schema_from_model_field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3, 3, 3, 5], "arg_names": ["field", "schema_generator", "model_name_map", "field_mapping", "separate_input_output_schemas"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.get_schema_from_model_field", "name": "get_schema_from_model_field", "type": {".class": "CallableType", "arg_kinds": [3, 3, 3, 3, 5], "arg_names": ["field", "schema_generator", "model_name_map", "field_mapping", "separate_input_output_schemas"], "arg_types": ["fastapi._compat.ModelField", "pydantic.json_schema.GenerateJsonSchema", {".class": "TypeAliasType", "args": [], "type_ref": "fastapi.types.ModelNameMap"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["fastapi._compat.ModelField", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_schema_from_model_field", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_bytes_field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["field"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.is_bytes_field", "name": "is_bytes_field", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["field"], "arg_types": ["fastapi._compat.ModelField"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_bytes_field", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_bytes_or_nonable_bytes_annotation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["annotation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.is_bytes_or_nonable_bytes_annotation", "name": "is_bytes_or_nonable_bytes_annotation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["annotation"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_bytes_or_nonable_bytes_annotation", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_bytes_sequence_annotation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["annotation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.is_bytes_sequence_annotation", "name": "is_bytes_sequence_annotation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["annotation"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_bytes_sequence_annotation", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_bytes_sequence_field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["field"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.is_bytes_sequence_field", "name": "is_bytes_sequence_field", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["field"], "arg_types": ["fastapi._compat.ModelField"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_bytes_sequence_field", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.is_dataclass", "kind": "Gdef"}, "is_pv1_scalar_field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["field"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.is_pv1_scalar_field", "name": "is_pv1_scalar_field", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["field"], "arg_types": ["fastapi._compat.ModelField"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_pv1_scalar_field", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_pv1_scalar_sequence_field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["field"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.is_pv1_scalar_sequence_field", "name": "is_pv1_scalar_sequence_field", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["field"], "arg_types": ["fastapi._compat.ModelField"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_pv1_scalar_sequence_field", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_scalar_field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["field"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.is_scalar_field", "name": "is_scalar_field", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["field"], "arg_types": ["fastapi._compat.ModelField"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_scalar_field", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_scalar_sequence_field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["field"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.is_scalar_sequence_field", "name": "is_scalar_sequence_field", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["field"], "arg_types": ["fastapi._compat.ModelField"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_scalar_sequence_field", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_sequence_field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["field"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.is_sequence_field", "name": "is_sequence_field", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["field"], "arg_types": ["fastapi._compat.ModelField"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_sequence_field", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_uploadfile_or_nonable_uploadfile_annotation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["annotation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.is_uploadfile_or_nonable_uploadfile_annotation", "name": "is_uploadfile_or_nonable_uploadfile_annotation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["annotation"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_uploadfile_or_nonable_uploadfile_annotation", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_uploadfile_sequence_annotation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["annotation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.is_uploadfile_sequence_annotation", "name": "is_uploadfile_sequence_annotation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["annotation"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_uploadfile_sequence_annotation", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lenient_issubclass": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._utils.lenient_issubclass", "kind": "Gdef"}, "lru_cache": {".class": "SymbolTableNode", "cross_ref": "functools.lru_cache", "kind": "Gdef"}, "model_process_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "from_module_getattr"], "fullname": "pydantic.schema.model_process_schema", "name": "model_process_schema", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "sequence_annotation_to_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "fastapi._compat.sequence_annotation_to_type", "name": "sequence_annotation_to_type", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.object", "abc.ABCMeta"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "sequence_shape_to_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "fastapi._compat.sequence_shape_to_type", "name": "sequence_shape_to_type", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "fastapi._compat.SHAPE_LIST", "source_any": {".class": "AnyType", "missing_import_name": "fastapi._compat.SHAPE_LIST", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, "abc.ABCMeta"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "sequence_shapes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "fastapi._compat.sequence_shapes", "name": "sequence_shapes", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "fastapi._compat.SHAPE_LIST", "source_any": {".class": "AnyType", "missing_import_name": "fastapi._compat.SHAPE_LIST", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "sequence_types": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "fastapi._compat.sequence_types", "name": "sequence_types", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "serialize_sequence_value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3], "arg_names": ["field", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.serialize_sequence_value", "name": "serialize_sequence_value", "type": {".class": "CallableType", "arg_kinds": [3, 3], "arg_names": ["field", "value"], "arg_types": ["fastapi._compat.ModelField", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "serialize_sequence_value", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "value_is_sequence": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fastapi._compat.value_is_sequence", "name": "value_is_sequence", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "value_is_sequence", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "with_info_plain_validator_function": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema.with_info_plain_validator_function", "kind": "Gdef"}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/fastapi/_compat.py"}