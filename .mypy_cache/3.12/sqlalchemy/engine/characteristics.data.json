{".class": "MypyFile", "_fullname": "sqlalchemy.engine.characteristics", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef"}, "Connection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Connection", "kind": "Gdef"}, "ConnectionCharacteristic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get_characteristic", 1], ["reset_characteristic", 1], ["set_characteristic", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.characteristics.ConnectionCharacteristic", "name": "ConnectionCharacteristic", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "sqlalchemy.engine.characteristics.ConnectionCharacteristic", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.engine.characteristics", "mro": ["sqlalchemy.engine.characteristics.ConnectionCharacteristic", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.engine.characteristics.ConnectionCharacteristic.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "get_characteristic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "sqlalchemy.engine.characteristics.ConnectionCharacteristic.get_characteristic", "name": "get_characteristic", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn"], "arg_types": ["sqlalchemy.engine.characteristics.ConnectionCharacteristic", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPIConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_characteristic of ConnectionCharacteristic", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.characteristics.ConnectionCharacteristic.get_characteristic", "name": "get_characteristic", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn"], "arg_types": ["sqlalchemy.engine.characteristics.ConnectionCharacteristic", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPIConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_characteristic of ConnectionCharacteristic", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_connection_characteristic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dialect", "conn", "dbapi_conn"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.engine.characteristics.ConnectionCharacteristic.get_connection_characteristic", "name": "get_connection_characteristic", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dialect", "conn", "dbapi_conn"], "arg_types": ["sqlalchemy.engine.characteristics.ConnectionCharacteristic", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "sqlalchemy.engine.interfaces.DBAPIConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_connection_characteristic of ConnectionCharacteristic", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset_characteristic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "sqlalchemy.engine.characteristics.ConnectionCharacteristic.reset_characteristic", "name": "reset_characteristic", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn"], "arg_types": ["sqlalchemy.engine.characteristics.ConnectionCharacteristic", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPIConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "reset_characteristic of ConnectionCharacteristic", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.characteristics.ConnectionCharacteristic.reset_characteristic", "name": "reset_characteristic", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn"], "arg_types": ["sqlalchemy.engine.characteristics.ConnectionCharacteristic", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPIConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "reset_characteristic of ConnectionCharacteristic", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "set_characteristic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "sqlalchemy.engine.characteristics.ConnectionCharacteristic.set_characteristic", "name": "set_characteristic", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn", "value"], "arg_types": ["sqlalchemy.engine.characteristics.ConnectionCharacteristic", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPIConnection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_characteristic of ConnectionCharacteristic", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.characteristics.ConnectionCharacteristic.set_characteristic", "name": "set_characteristic", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn", "value"], "arg_types": ["sqlalchemy.engine.characteristics.ConnectionCharacteristic", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPIConnection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_characteristic of ConnectionCharacteristic", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "set_connection_characteristic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "dialect", "conn", "dbapi_conn", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.engine.characteristics.ConnectionCharacteristic.set_connection_characteristic", "name": "set_connection_characteristic", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "dialect", "conn", "dbapi_conn", "value"], "arg_types": ["sqlalchemy.engine.characteristics.ConnectionCharacteristic", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "sqlalchemy.engine.interfaces.DBAPIConnection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_connection_characteristic of ConnectionCharacteristic", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "transactional": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.characteristics.ConnectionCharacteristic.transactional", "name": "transactional", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.characteristics.ConnectionCharacteristic.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.characteristics.ConnectionCharacteristic", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DBAPIConnection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.DBAPIConnection", "kind": "Gdef"}, "Dialect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.Dialect", "kind": "Gdef"}, "IsolationLevelCharacteristic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.characteristics.ConnectionCharacteristic"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.characteristics.IsolationLevelCharacteristic", "name": "IsolationLevelCharacteristic", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.characteristics.IsolationLevelCharacteristic", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.engine.characteristics", "mro": ["sqlalchemy.engine.characteristics.IsolationLevelCharacteristic", "sqlalchemy.engine.characteristics.ConnectionCharacteristic", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "get_characteristic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.engine.characteristics.IsolationLevelCharacteristic.get_characteristic", "name": "get_characteristic", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn"], "arg_types": ["sqlalchemy.engine.characteristics.IsolationLevelCharacteristic", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPIConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_characteristic of IsolationLevelCharacteristic", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset_characteristic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.engine.characteristics.IsolationLevelCharacteristic.reset_characteristic", "name": "reset_characteristic", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn"], "arg_types": ["sqlalchemy.engine.characteristics.IsolationLevelCharacteristic", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPIConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "reset_characteristic of IsolationLevelCharacteristic", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_characteristic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.engine.characteristics.IsolationLevelCharacteristic.set_characteristic", "name": "set_characteristic", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn", "value"], "arg_types": ["sqlalchemy.engine.characteristics.IsolationLevelCharacteristic", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPIConnection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_characteristic of IsolationLevelCharacteristic", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "transactional": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.characteristics.IsolationLevelCharacteristic.transactional", "name": "transactional", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.characteristics.IsolationLevelCharacteristic.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.characteristics.IsolationLevelCharacteristic", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LoggingTokenCharacteristic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.characteristics.ConnectionCharacteristic"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.characteristics.LoggingTokenCharacteristic", "name": "LoggingTokenCharacteristic", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.characteristics.LoggingTokenCharacteristic", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.engine.characteristics", "mro": ["sqlalchemy.engine.characteristics.LoggingTokenCharacteristic", "sqlalchemy.engine.characteristics.ConnectionCharacteristic", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "get_characteristic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.engine.characteristics.LoggingTokenCharacteristic.get_characteristic", "name": "get_characteristic", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn"], "arg_types": ["sqlalchemy.engine.characteristics.LoggingTokenCharacteristic", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPIConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_characteristic of LoggingTokenCharacteristic", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_connection_characteristic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dialect", "conn", "dbapi_conn"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.engine.characteristics.LoggingTokenCharacteristic.get_connection_characteristic", "name": "get_connection_characteristic", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dialect", "conn", "dbapi_conn"], "arg_types": ["sqlalchemy.engine.characteristics.LoggingTokenCharacteristic", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "sqlalchemy.engine.interfaces.DBAPIConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_connection_characteristic of LoggingTokenCharacteristic", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset_characteristic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.engine.characteristics.LoggingTokenCharacteristic.reset_characteristic", "name": "reset_characteristic", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn"], "arg_types": ["sqlalchemy.engine.characteristics.LoggingTokenCharacteristic", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPIConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "reset_characteristic of LoggingTokenCharacteristic", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_characteristic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.engine.characteristics.LoggingTokenCharacteristic.set_characteristic", "name": "set_characteristic", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dialect", "dbapi_conn", "value"], "arg_types": ["sqlalchemy.engine.characteristics.LoggingTokenCharacteristic", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.interfaces.DBAPIConnection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_characteristic of LoggingTokenCharacteristic", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_connection_characteristic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "dialect", "conn", "dbapi_conn", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.engine.characteristics.LoggingTokenCharacteristic.set_connection_characteristic", "name": "set_connection_characteristic", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "dialect", "conn", "dbapi_conn", "value"], "arg_types": ["sqlalchemy.engine.characteristics.LoggingTokenCharacteristic", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "sqlalchemy.engine.interfaces.DBAPIConnection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_connection_characteristic of LoggingTokenCharacteristic", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "transactional": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.characteristics.LoggingTokenCharacteristic.transactional", "name": "transactional", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.characteristics.LoggingTokenCharacteristic.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.characteristics.LoggingTokenCharacteristic", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.characteristics.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.characteristics.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.characteristics.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.characteristics.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.characteristics.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.characteristics.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/engine/characteristics.py"}