{"data_mtime": 1751559210, "dep_lines": [30, 37, 44, 45, 62, 63, 70, 72, 75, 76, 79, 40, 41, 42, 43, 44, 48, 64, 66, 10, 12, 13, 14, 40, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 25, 25, 25, 25, 25, 25, 25, 10, 10, 10, 10, 20, 25, 25, 25, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.interfaces", "sqlalchemy.engine.util", "sqlalchemy.sql.compiler", "sqlalchemy.sql.util", "sqlalchemy.engine.reflection", "sqlalchemy.engine.url", "sqlalchemy.sql._typing", "sqlalchemy.sql.ddl", "sqlalchemy.sql.functions", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.util", "sqlalchemy.sql", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.pool", "__future__", "contextlib", "sys", "typing", "sqlalchemy", "builtins", "_collections_abc", "_frozen_importlib", "abc", "enum", "sqlalchemy.engine.cursor", "sqlalchemy.engine.result", "sqlalchemy.event.attr", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.pool.base", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "sqlalchemy.util._concurrency_py3k", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "0a1cdecc137a72fb6a2364116c5756d836d8ab9f", "id": "sqlalchemy.engine.base", "ignore_all": true, "interface_hash": "911a95ef70c98f90f7730360026a0e0aa4deb798", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", "plugin_data": null, "size": 122901, "suppressed": [], "version_id": "1.16.1"}