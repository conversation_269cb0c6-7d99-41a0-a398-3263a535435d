{"data_mtime": 1751559210, "dep_lines": [42, 43, 44, 45, 51, 57, 58, 59, 60, 61, 62, 63, 67, 68, 87, 88, 99, 42, 53, 54, 55, 56, 57, 17, 19, 20, 21, 22, 23, 24, 40, 53, 72, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 25, 25, 25, 20, 10, 10, 10, 10, 20, 5, 10, 10, 10, 10, 5, 5, 10, 20, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.characteristics", "sqlalchemy.engine.cursor", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.base", "sqlalchemy.engine.reflection", "sqlalchemy.sql.compiler", "sqlalchemy.sql.dml", "sqlalchemy.sql.expression", "sqlalchemy.sql.type_api", "sqlalchemy.sql.util", "sqlalchemy.sql._typing", "sqlalchemy.sql.base", "sqlalchemy.sql.elements", "sqlalchemy.util.typing", "sqlalchemy.engine.row", "sqlalchemy.engine.url", "sqlalchemy.sql.schema", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.pool", "sqlalchemy.util", "sqlalchemy.sql", "__future__", "functools", "operator", "random", "re", "time", "typing", "weakref", "sqlalchemy", "types", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "sqlalchemy.engine._py_row", "sqlalchemy.engine.result", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.pool.base", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.ddl", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers"], "hash": "94059e035826390096ebe5157b5385d4ad0e62fb", "id": "sqlalchemy.engine.default", "ignore_all": true, "interface_hash": "a85d8898f87a525cad6caed93115bcaab4946272", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py", "plugin_data": null, "size": 85334, "suppressed": [], "version_id": "1.16.1"}