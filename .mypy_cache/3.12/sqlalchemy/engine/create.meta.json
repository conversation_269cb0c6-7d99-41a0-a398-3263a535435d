{"data_mtime": 1751559210, "dep_lines": [22, 23, 24, 25, 31, 45, 22, 26, 27, 28, 29, 31, 40, 8, 10, 11, 26, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 10, 25, 20, 10, 10, 5, 5, 20, 25, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.base", "sqlalchemy.engine.url", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.mock", "sqlalchemy.sql.compiler", "sqlalchemy.util.typing", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.pool", "sqlalchemy.sql", "sqlalchemy.log", "__future__", "inspect", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "enum", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.pool.base", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "types"], "hash": "fc86f11ef7f98f41118a95b22cfb417f238caa65", "id": "sqlalchemy.engine.create", "ignore_all": true, "interface_hash": "1181fda116d295729620a74966e84fb633539246", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/engine/create.py", "plugin_data": null, "size": 33217, "suppressed": [], "version_id": "1.16.1"}