{"data_mtime": 1751559210, "dep_lines": [32, 34, 37, 42, 13, 32, 33, 10, 12, 13, 14, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 25, 25, 10, 20, 5, 5, 5, 20, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.util", "sqlalchemy.util._has_cy", "sqlalchemy.engine._py_row", "sqlalchemy.engine.result", "collections.abc", "sqlalchemy.sql", "sqlalchemy.util", "__future__", "abc", "collections", "operator", "typing", "builtins", "_frozen_importlib", "sqlalchemy.exc", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.type_api", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "types"], "hash": "ecd2dac0f9f6347230bf71b7e3ff2256b6162437", "id": "sqlalchemy.engine.row", "ignore_all": true, "interface_hash": "17fba14ea4799e86f41d892e78ab7299dcf650b7", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/engine/row.py", "plugin_data": null, "size": 12031, "suppressed": [], "version_id": "1.16.1"}