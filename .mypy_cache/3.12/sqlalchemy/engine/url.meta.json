{"data_mtime": 1751559210, "dep_lines": [39, 19, 34, 40, 41, 42, 17, 19, 20, 21, 40, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 10, 10, 5, 5, 20, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.interfaces", "collections.abc", "urllib.parse", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.dialects", "__future__", "collections", "re", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations"], "hash": "f5e37065f0e3ae1d47ffde54c0e17c83f0a0b2bb", "id": "sqlalchemy.engine.url", "ignore_all": true, "interface_hash": "27a73bbd5bee18d8228416ffa2bc3e11b298b947", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/engine/url.py", "plugin_data": null, "size": 31069, "suppressed": [], "version_id": "1.16.1"}