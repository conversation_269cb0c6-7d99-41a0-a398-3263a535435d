{"data_mtime": 1751559210, "dep_lines": [19, 24, 25, 29, 30, 32, 19, 20, 8, 10, 11, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 25, 25, 25, 25, 25, 20, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.url", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.sql.base", "sqlalchemy.sql.ddl", "sqlalchemy.sql.schema", "sqlalchemy.engine", "sqlalchemy.util", "__future__", "operator", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.sql", "sqlalchemy.sql.compiler", "sqlalchemy.sql.roles", "sqlalchemy.sql.visitors", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers"], "hash": "b3ca3ba7594fedae661d6aeb61bc18b151869057", "id": "sqlalchemy.engine.mock", "ignore_all": true, "interface_hash": "65604f5fff9bb9f122a42e1a6cf6e46a6944ddbb", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/engine/mock.py", "plugin_data": null, "size": 4181, "suppressed": [], "version_id": "1.16.1"}