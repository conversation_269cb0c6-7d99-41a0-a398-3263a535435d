{"data_mtime": 1751559210, "dep_lines": [36, 40, 45, 46, 50, 55, 56, 38, 39, 10, 12, 13, 14, 15, 16, 38, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 25, 25, 25, 10, 5, 5, 5, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.row", "sqlalchemy.sql.base", "sqlalchemy.util._has_cy", "sqlalchemy.util.typing", "sqlalchemy.engine._py_row", "sqlalchemy.sql.elements", "sqlalchemy.sql.type_api", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "enum", "functools", "itertools", "operator", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "_typeshed", "abc", "sqlalchemy.sql", "sqlalchemy.sql.operators", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "1645c3ee8290780aa6a18168ab662bca51a37b12", "id": "sqlalchemy.engine.result", "ignore_all": true, "interface_hash": "e6ba503b7ffd7a26b324c2c22466573c7cf8d60a", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/engine/result.py", "plugin_data": null, "size": 77636, "suppressed": [], "version_id": "1.16.1"}