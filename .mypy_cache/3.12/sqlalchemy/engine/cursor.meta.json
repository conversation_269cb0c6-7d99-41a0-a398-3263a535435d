{"data_mtime": 1751559210, "dep_lines": [35, 41, 44, 45, 46, 47, 48, 53, 54, 55, 60, 61, 62, 42, 43, 44, 13, 15, 16, 17, 18, 42, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 5, 5, 5, 10, 5, 25, 25, 25, 10, 10, 20, 5, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.result", "sqlalchemy.engine.row", "sqlalchemy.sql.elements", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.util", "sqlalchemy.sql.base", "sqlalchemy.sql.compiler", "sqlalchemy.sql.type_api", "sqlalchemy.util.compat", "sqlalchemy.util.typing", "sqlalchemy.engine.base", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.sql", "__future__", "collections", "functools", "operator", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "_operator", "_typeshed", "abc", "sqlalchemy.engine._py_row", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util.langhelpers", "types"], "hash": "806e0b229266073187dcca939a55dd6c00f98685", "id": "sqlalchemy.engine.cursor", "ignore_all": true, "interface_hash": "9f866aa998117e5369595daacc8b4271d2ba7a21", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/engine/cursor.py", "plugin_data": null, "size": 76489, "suppressed": [], "version_id": "1.16.1"}