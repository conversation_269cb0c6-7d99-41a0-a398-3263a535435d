{".class": "MypyFile", "_fullname": "sqlalchemy.future", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Connection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Connection", "kind": "Gdef"}, "Engine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Engine", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.future.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.future.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.future.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.future.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.future.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.future.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.future.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "create_engine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.create.create_engine", "kind": "Gdef"}, "select": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.select", "kind": "Gdef"}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/future/__init__.py"}