{"data_mtime": 1751559209, "dep_lines": [44, 43, 31, 33, 43, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 20, 5, 30, 30, 30], "dependencies": ["sqlalchemy.util.typing", "sqlalchemy.exc", "__future__", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "types"], "hash": "1e5776bd6f7e9539952d7d270d4e018ec6b50e69", "id": "sqlalchemy.inspection", "ignore_all": true, "interface_hash": "a9f8a649f4c348afca2adf4fe8ae1cf760e30c0c", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/inspection.py", "plugin_data": null, "size": 5063, "suppressed": [], "version_id": "1.16.1"}