{"data_mtime": 1751559210, "dep_lines": [86, 12, 13, 47, 48, 60, 82, 223, 8, 10, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["sqlalchemy.sql.expression", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.inspection", "sqlalchemy.pool", "sqlalchemy.schema", "sqlalchemy.sql", "sqlalchemy.types", "__future__", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "f210f1d6e83484dc6b21f2b579acf7b865e18e0e", "id": "sqlalchemy", "ignore_all": true, "interface_hash": "52b8b3cc0e9331bf8a3d1d49bda358d65dcdbab7", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/__init__.py", "plugin_data": null, "size": 13033, "suppressed": [], "version_id": "1.16.1"}