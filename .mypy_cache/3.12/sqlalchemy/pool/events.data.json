{".class": "MypyFile", "_fullname": "sqlalchemy.pool.events", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ConnectionPoolEntry": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base.ConnectionPoolEntry", "kind": "Gdef"}, "DBAPIConnection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.DBAPIConnection", "kind": "Gdef"}, "Engine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Engine", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Pool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base.Pool", "kind": "Gdef"}, "PoolEvents": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["sqlalchemy.pool.base.Pool"], "extra_attrs": null, "type_ref": "sqlalchemy.event.base.Events"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.pool.events.PoolEvents", "name": "PoolEvents", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.pool.events.PoolEvents", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.pool.events", "mro": ["sqlalchemy.pool.events.PoolEvents", "sqlalchemy.event.base.Events", "sqlalchemy.event.base._HasEventsDispatch", "builtins.object"], "names": {".class": "SymbolTable", "_accept_with": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "sqlalchemy.pool.events.PoolEvents._accept_with", "name": "_accept_with", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.pool.events.PoolEvents"}, {".class": "UnionType", "items": ["sqlalchemy.pool.base.Pool", {".class": "TypeType", "item": "sqlalchemy.pool.base.Pool"}, "sqlalchemy.engine.base.Engine", {".class": "TypeType", "item": "sqlalchemy.engine.base.Engine"}], "uses_pep604_syntax": false}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_accept_with of PoolEvents", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.pool.base.Pool", {".class": "TypeType", "item": "sqlalchemy.pool.base.Pool"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.pool.events.PoolEvents._accept_with", "name": "_accept_with", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "target", "identifier"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.pool.events.PoolEvents"}, {".class": "UnionType", "items": ["sqlalchemy.pool.base.Pool", {".class": "TypeType", "item": "sqlalchemy.pool.base.Pool"}, "sqlalchemy.engine.base.Engine", {".class": "TypeType", "item": "sqlalchemy.engine.base.Engine"}], "uses_pep604_syntax": false}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_accept_with of PoolEvents", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.pool.base.Pool", {".class": "TypeType", "item": "sqlalchemy.pool.base.Pool"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_dispatch_target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.pool.events.PoolEvents._dispatch_target", "name": "_dispatch_target", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["creator", "recycle", "echo", "logging_name", "reset_on_return", "events", "dialect", "pre_ping", "_dispatch"], "arg_types": [{".class": "UnionType", "items": ["sqlalchemy.pool.base._CreatorFnType", "sqlalchemy.pool.base._CreatorWRecFnType"], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.log._EchoFlagType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.pool.base._ResetStyleArgType"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.pool.base._ConnDialect", "sqlalchemy.engine.interfaces.Dialect", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.pool.base.Pool"], "extra_attrs": null, "type_ref": "sqlalchemy.event.base._DispatchCommon"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.pool.base.Pool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_listen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["cls", "event_key", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "sqlalchemy.pool.events.PoolEvents._listen", "name": "_listen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "event_key", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.pool.events.PoolEvents"}, {".class": "Instance", "args": ["sqlalchemy.pool.base.Pool"], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_listen of PoolEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.pool.events.PoolEvents._listen", "name": "_listen", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "event_key", "kw"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.pool.events.PoolEvents"}, {".class": "Instance", "args": ["sqlalchemy.pool.base.Pool"], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_listen of PoolEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_target_class_doc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.pool.events.PoolEvents._target_class_doc", "name": "_target_class_doc", "setter_type": null, "type": "builtins.str"}}, "checkin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dbapi_connection", "connection_record"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.pool.events.PoolEvents.checkin", "name": "checkin", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dbapi_connection", "connection_record"], "arg_types": ["sqlalchemy.pool.events.PoolEvents", {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.DBAPIConnection", {".class": "NoneType"}], "uses_pep604_syntax": false}, "sqlalchemy.pool.base.ConnectionPoolEntry"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "checkin of PoolEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "checkout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dbapi_connection", "connection_record", "connection_proxy"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.pool.events.PoolEvents.checkout", "name": "checkout", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dbapi_connection", "connection_record", "connection_proxy"], "arg_types": ["sqlalchemy.pool.events.PoolEvents", "sqlalchemy.engine.interfaces.DBAPIConnection", "sqlalchemy.pool.base.ConnectionPoolEntry", "sqlalchemy.pool.base.PoolProxiedConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "checkout of PoolEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dbapi_connection", "connection_record"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.pool.events.PoolEvents.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dbapi_connection", "connection_record"], "arg_types": ["sqlalchemy.pool.events.PoolEvents", "sqlalchemy.engine.interfaces.DBAPIConnection", "sqlalchemy.pool.base.ConnectionPoolEntry"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of PoolEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "close_detached": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.pool.events.PoolEvents.close_detached", "name": "close_detached", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "arg_types": ["sqlalchemy.pool.events.PoolEvents", "sqlalchemy.engine.interfaces.DBAPIConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close_detached of PoolEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dbapi_connection", "connection_record"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.pool.events.PoolEvents.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dbapi_connection", "connection_record"], "arg_types": ["sqlalchemy.pool.events.PoolEvents", "sqlalchemy.engine.interfaces.DBAPIConnection", "sqlalchemy.pool.base.ConnectionPoolEntry"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "connect of PoolEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "detach": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dbapi_connection", "connection_record"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.pool.events.PoolEvents.detach", "name": "detach", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dbapi_connection", "connection_record"], "arg_types": ["sqlalchemy.pool.events.PoolEvents", "sqlalchemy.engine.interfaces.DBAPIConnection", "sqlalchemy.pool.base.ConnectionPoolEntry"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "detach of PoolEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "first_connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dbapi_connection", "connection_record"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.pool.events.PoolEvents.first_connect", "name": "first_connect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dbapi_connection", "connection_record"], "arg_types": ["sqlalchemy.pool.events.PoolEvents", "sqlalchemy.engine.interfaces.DBAPIConnection", "sqlalchemy.pool.base.ConnectionPoolEntry"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "first_connect of PoolEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "invalidate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dbapi_connection", "connection_record", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.pool.events.PoolEvents.invalidate", "name": "invalidate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dbapi_connection", "connection_record", "exception"], "arg_types": ["sqlalchemy.pool.events.PoolEvents", "sqlalchemy.engine.interfaces.DBAPIConnection", "sqlalchemy.pool.base.ConnectionPoolEntry", {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "invalidate of PoolEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dbapi_connection", "connection_record", "reset_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "sqlalchemy.pool.events.PoolEvents.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dbapi_connection", "connection_record", "reset_state"], "arg_types": ["sqlalchemy.pool.events.PoolEvents", "sqlalchemy.engine.interfaces.DBAPIConnection", "sqlalchemy.pool.base.ConnectionPoolEntry", "sqlalchemy.pool.base.PoolResetState"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "reset of PoolEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.pool.events.PoolEvents.reset", "name": "reset", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "reset of PoolEvents", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "soft_invalidate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dbapi_connection", "connection_record", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.pool.events.PoolEvents.soft_invalidate", "name": "soft_invalidate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dbapi_connection", "connection_record", "exception"], "arg_types": ["sqlalchemy.pool.events.PoolEvents", "sqlalchemy.engine.interfaces.DBAPIConnection", "sqlalchemy.pool.base.ConnectionPoolEntry", {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "soft_invalidate of PoolEvents", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.pool.events.PoolEvents.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.pool.events.PoolEvents", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PoolProxiedConnection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base.PoolProxiedConnection", "kind": "Gdef"}, "PoolResetState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base.PoolResetState", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.pool.events.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.pool.events.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.pool.events.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.pool.events.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.pool.events.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.pool.events.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "event": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/pool/events.py"}