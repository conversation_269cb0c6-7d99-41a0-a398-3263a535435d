{"data_mtime": 1751559209, "dep_lines": [27, 28, 29, 35, 36, 12, 27, 34, 9, 11, 13, 15, 16, 34, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 5, 10, 20, 10, 5, 5, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.operators", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.visitors", "sqlalchemy.util.langhelpers", "sqlalchemy.util.typing", "collections.abc", "sqlalchemy.sql", "sqlalchemy.util", "__future__", "collections", "itertools", "operator", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "enum", "sqlalchemy.util.preloaded", "types"], "hash": "5aac8fb46262ddbbf7bb4732923e46fb108ff336", "id": "sqlalchemy.sql.traversals", "ignore_all": true, "interface_hash": "2f8d1b4a7281b7d14f2c47342be1ec04b387c3e3", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/sql/traversals.py", "plugin_data": null, "size": 33664, "suppressed": [], "version_id": "1.16.1"}