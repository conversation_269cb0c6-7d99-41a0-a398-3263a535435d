{"data_mtime": 1751559209, "dep_lines": [27, 31, 43, 44, 47, 49, 55, 57, 58, 70, 72, 27, 28, 29, 30, 73, 8, 10, 11, 28, 36, 40, 41, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 20, 10, 10, 5, 25, 5, 10, 5, 20, 25, 25, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.roles", "sqlalchemy.util.typing", "sqlalchemy.sql.base", "sqlalchemy.sql.compiler", "sqlalchemy.sql.dml", "sqlalchemy.sql.elements", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.type_api", "sqlalchemy.sql", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.inspection", "sqlalchemy.engine", "__future__", "operator", "typing", "sqlalchemy", "datetime", "decimal", "uuid", "builtins", "_frozen_importlib", "_typeshed", "abc", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "6bdbe7a9c78dd2f8a39399aa668518e26c2da785", "id": "sqlalchemy.sql._typing", "ignore_all": true, "interface_hash": "d70f8e07a45813577a99c9e4eff9664f7704c683", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/sql/_typing.py", "plugin_data": null, "size": 12847, "suppressed": [], "version_id": "1.16.1"}