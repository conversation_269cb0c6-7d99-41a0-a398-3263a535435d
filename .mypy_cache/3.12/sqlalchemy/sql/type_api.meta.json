{"data_mtime": 1751559210, "dep_lines": [33, 34, 36, 37, 40, 48, 49, 52, 61, 38, 39, 12, 14, 15, 16, 38, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 25, 25, 25, 25, 10, 10, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.visitors", "sqlalchemy.util.typing", "sqlalchemy.sql._typing", "sqlalchemy.sql.elements", "sqlalchemy.sql.sqltypes", "sqlalchemy.engine.interfaces", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "enum", "types", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql.annotation", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "typing_extensions"], "hash": "d25bb38aedc746ad3f9832cd2c0eaa2e2b3068c1", "id": "sqlalchemy.sql.type_api", "ignore_all": true, "interface_hash": "c3538e828ebcfc2aaffc3b81054c20ba773e5c51", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/sql/type_api.py", "plugin_data": null, "size": 84915, "suppressed": [], "version_id": "1.16.1"}