{"data_mtime": 1751559209, "dep_lines": [45, 46, 47, 49, 59, 65, 66, 67, 68, 70, 71, 79, 81, 86, 45, 54, 55, 56, 84, 14, 16, 17, 18, 20, 21, 22, 54, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 20, 10, 10, 5, 25, 5, 10, 5, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.roles", "sqlalchemy.sql.visitors", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.traversals", "sqlalchemy.util.typing", "sqlalchemy.sql.coercions", "sqlalchemy.sql.elements", "sqlalchemy.sql.type_api", "sqlalchemy.sql._orm_types", "sqlalchemy.sql._typing", "sqlalchemy.sql.compiler", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.engine.interfaces", "sqlalchemy.sql", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "collections", "enum", "itertools", "operator", "re", "typing", "sqlalchemy", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "sqlalchemy.engine.base", "sqlalchemy.engine.cursor", "sqlalchemy.engine.result", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.operators", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types"], "hash": "8ede24d48c8c68f63b44717b3d09a78ba3979ae6", "id": "sqlalchemy.sql.base", "ignore_all": true, "interface_hash": "e8fadf19a09fc00974debafb78859111ef42490d", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/sql/base.py", "plugin_data": null, "size": 73946, "suppressed": [], "version_id": "1.16.1"}