{"data_mtime": 1751559209, "dep_lines": [16, 19, 20, 21, 15, 7, 9, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 25, 25, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.util.typing", "sqlalchemy.sql._typing", "sqlalchemy.sql.elements", "sqlalchemy.sql.selectable", "sqlalchemy.util", "__future__", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "75766b195203eda0720e45a613be4c6c5f257d63", "id": "sqlalchemy.sql.roles", "ignore_all": true, "interface_hash": "9692fb933e1ff708cb0474837d1393a6838b7fbe", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/sql/roles.py", "plugin_data": null, "size": 7662, "suppressed": [], "version_id": "1.16.1"}