{"data_mtime": 1751559209, "dep_lines": [35, 36, 37, 41, 45, 35, 40, 18, 20, 40, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 25, 20, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.operators", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.visitors", "sqlalchemy.util.typing", "sqlalchemy.sql.base", "sqlalchemy.sql", "sqlalchemy.util", "__future__", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "enum", "sqlalchemy.sql._py_util", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "9b356268e724e1e09d949a14dad6630ec9a63572", "id": "sqlalchemy.sql.annotation", "ignore_all": true, "interface_hash": "039f91a73e410e336c4475974c52ea2ca7e94959", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/sql/annotation.py", "plugin_data": null, "size": 18245, "suppressed": [], "version_id": "1.16.1"}