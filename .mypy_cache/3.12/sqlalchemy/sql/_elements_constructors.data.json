{".class": "MypyFile", "_fullname": "sqlalchemy.sql._elements_constructors", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BinaryExpression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.BinaryExpression", "kind": "Gdef"}, "BindParameter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.BindParameter", "kind": "Gdef"}, "BooleanClauseList": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.BooleanClauseList", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Case": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Case", "kind": "Gdef"}, "Cast": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Cast", "kind": "Gdef"}, "CollationClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.CollationClause", "kind": "Gdef"}, "CollectionAggregate": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.CollectionAggregate", "kind": "Gdef"}, "ColumnClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnClause", "kind": "Gdef"}, "ColumnElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnElement", "kind": "Gdef"}, "Extract": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Extract", "kind": "Gdef"}, "False_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.False_", "kind": "Gdef"}, "FromClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.FromClause", "kind": "Gdef"}, "FunctionElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions.FunctionElement", "kind": "Gdef"}, "FunctionFilter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.FunctionFilter", "kind": "Gdef"}, "Label": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Label", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "Null": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Null", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Over": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Over", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TextClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.TextClause", "kind": "Gdef"}, "True_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.True_", "kind": "Gdef"}, "TryCast": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.TryCast", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.<PERSON>", "kind": "Gdef"}, "TypeCoerce": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.TypeCoerce", "kind": "Gdef"}, "TypeEngine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api.TypeEngine", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "UnaryExpression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.UnaryExpression", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WithinGroup": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.WithinGroup", "kind": "Gdef"}, "_ByArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._ByArgument", "kind": "Gdef"}, "_ColumnExpressionArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument", "kind": "Gdef"}, "_ColumnExpressionOrLiteralArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._ColumnExpressionOrLiteralArgument", "kind": "Gdef"}, "_ColumnExpressionOrStrLabelArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._ColumnExpressionOrStrLabelArgument", "kind": "Gdef"}, "_NoArg": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._NoArg", "kind": "Gdef"}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_TypeEngineArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._TypeEngineArgument", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql._elements_constructors.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql._elements_constructors.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql._elements_constructors.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql._elements_constructors.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql._elements_constructors.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql._elements_constructors.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_document_text_coercion": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.coercions._document_text_coercion", "kind": "Gdef"}, "all_": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["expr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.all_", "name": "all_", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["expr"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.all_", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "all_", "ret_type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.CollectionAggregate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.all_", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "and_": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["initial_clause", "clauses"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.and_", "name": "and_", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["initial_clause", "clauses"], "arg_types": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "TypeAliasType", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "and_", "ret_type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "any_": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["expr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.any_", "name": "any_", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["expr"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.any_", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "any_", "ret_type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.CollectionAggregate"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.any_", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "asc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["column"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.asc", "name": "asc", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["column"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.asc", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionOrStrLabelArgument"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "asc", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.asc", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.UnaryExpression"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.asc", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "between": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["expr", "lower_bound", "upper_bound", "symmetric"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.between", "name": "between", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["expr", "lower_bound", "upper_bound", "symmetric"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.between", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionOrLiteralArgument"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "between", "ret_type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BinaryExpression"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.between", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "bindparam": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["key", "value", "type_", "unique", "required", "quote", "callable_", "expanding", "isoutparam", "literal_execute"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.bindparam", "name": "<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["key", "value", "type_", "unique", "required", "quote", "callable_", "expanding", "isoutparam", "literal_execute"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.bindparam", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypeEngineArgument"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "LiteralType", "fallback": "sqlalchemy.sql.base._NoArg", "value": "NO_ARG"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON>", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.bindparam", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BindParameter"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.bindparam", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "bitwise_not": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["expr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.bitwise_not", "name": "bitwise_not", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["expr"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.bitwise_not", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "bitwise_not", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.bitwise_not", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.UnaryExpression"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.bitwise_not", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "case": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 5, 5], "arg_names": ["whens", "value", "else_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.case", "name": "case", "type": {".class": "CallableType", "arg_kinds": [2, 5, 5], "arg_names": ["whens", "value", "else_"], "arg_types": [{".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "case", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.Case"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cast": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["expression", "type_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.cast", "name": "cast", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["expression", "type_"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionOrLiteralArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.cast", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypeEngineArgument"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cast", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.cast", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.Cast"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.cast", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "coercions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.coercions", "kind": "Gdef"}, "collate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["expression", "collation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.collate", "name": "collate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["expression", "collation"], "arg_types": [{".class": "TypeAliasType", "args": ["builtins.str"], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}, "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "collate", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BinaryExpression"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "column": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["text", "type_", "is_literal", "_selectable"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.column", "name": "column", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["text", "type_", "is_literal", "_selectable"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.column", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypeEngineArgument"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.FromClause", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "column", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.column", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnClause"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.column", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "desc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["column"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.desc", "name": "desc", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["column"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.desc", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionOrStrLabelArgument"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "desc", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.desc", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.UnaryExpression"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.desc", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "distinct": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["expr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.distinct", "name": "distinct", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["expr"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.distinct", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "distinct", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.distinct", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.UnaryExpression"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.distinct", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "extract": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["field", "expr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.extract", "name": "extract", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["field", "expr"], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "extract", "ret_type": "sqlalchemy.sql.elements.Extract", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "false": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.false", "name": "false", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "false", "ret_type": "sqlalchemy.sql.elements.False_", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "funcfilter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["func", "criterion"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.funcfilter", "name": "funcfilter", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["func", "criterion"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.funcfilter", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.functions.FunctionElement"}, {".class": "TypeAliasType", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "funcfilter", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.funcfilter", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.FunctionFilter"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.funcfilter", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "label": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["name", "element", "type_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.label", "name": "label", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["name", "element", "type_"], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.label", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.label", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypeEngineArgument"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "label", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.label", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.Label"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.label", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "not_": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.not_", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "sqlalchemy.sql._elements_constructors.not_", "name": "not_", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["clause"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.not_", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "not_", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.not_", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.not_", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql._elements_constructors.not_", "name": "not_", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["clause"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.not_#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BinaryExpression"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "not_", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.not_#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BinaryExpression"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.not_#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql._elements_constructors.not_", "name": "not_", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["clause"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.not_#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BinaryExpression"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "not_", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.not_#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BinaryExpression"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.not_#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "sqlalchemy.sql._elements_constructors.not_", "name": "not_", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["clause"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.not_#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "not_", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.not_#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.not_#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql._elements_constructors.not_", "name": "not_", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["clause"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.not_#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "not_", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.not_#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.not_#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["clause"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.not_#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BinaryExpression"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "not_", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.not_#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BinaryExpression"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.not_#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["clause"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.not_#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "not_", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.not_#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.not_#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "null": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.null", "name": "null", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "null", "ret_type": "sqlalchemy.sql.elements.Null", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "nulls_first": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["column"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.nulls_first", "name": "nulls_first", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["column"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.nulls_first", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "nulls_first", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.nulls_first", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.UnaryExpression"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.nulls_first", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "nulls_last": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["column"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.nulls_last", "name": "nulls_last", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["column"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.nulls_last", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "nulls_last", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.nulls_last", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.UnaryExpression"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.nulls_last", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "or_": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["initial_clause", "clauses"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.or_", "name": "or_", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["initial_clause", "clauses"], "arg_types": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "TypeAliasType", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "or_", "ret_type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "outparam": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["key", "type_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.outparam", "name": "outparam", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key", "type_"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.outparam", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "outparam", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.outparam", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BindParameter"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.outparam", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "over": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["element", "partition_by", "order_by", "range_", "rows", "groups"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.over", "name": "over", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["element", "partition_by", "order_by", "range_", "rows", "groups"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.over", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.functions.FunctionElement"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._ByArgument"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._ByArgument"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "over", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.over", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.Over"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.over", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "roles": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.roles", "kind": "Gdef"}, "text": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["text"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql._elements_constructors.text", "name": "text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["text"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "text", "ret_type": "sqlalchemy.sql.elements.TextClause", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.sql._elements_constructors.text", "name": "text", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["text"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "text", "ret_type": "sqlalchemy.sql.elements.TextClause", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "true": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.true", "name": "true", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "true", "ret_type": "sqlalchemy.sql.elements.True_", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "try_cast": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["expression", "type_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.try_cast", "name": "try_cast", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["expression", "type_"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionOrLiteralArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.try_cast", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypeEngineArgument"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "try_cast", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.try_cast", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.TryCast"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.try_cast", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "tuple_": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 5], "arg_names": ["clauses", "types"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.tuple_", "name": "tuple_", "type": {".class": "CallableType", "arg_kinds": [2, 5], "arg_names": ["clauses", "types"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql._typing._TypeEngineArgument"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "tuple_", "ret_type": "sqlalchemy.sql.elements.<PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "type_coerce": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["expression", "type_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.type_coerce", "name": "type_coerce", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["expression", "type_"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionOrLiteralArgument"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.type_coerce", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.sql._typing._TypeEngineArgument"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "type_coerce", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.type_coerce", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.TypeCoerce"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.type_coerce", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "typing_Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "within_group": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["element", "order_by"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql._elements_constructors.within_group", "name": "within_group", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["element", "order_by"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.within_group", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.functions.FunctionElement"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "within_group", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.within_group", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.WithinGroup"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql._elements_constructors._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql._elements_constructors.within_group", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/sql/_elements_constructors.py"}