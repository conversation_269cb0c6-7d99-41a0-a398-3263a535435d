{"data_mtime": 1751559210, "dep_lines": [35, 36, 37, 38, 39, 40, 43, 45, 46, 47, 60, 61, 71, 86, 90, 35, 69, 70, 12, 14, 15, 16, 17, 69, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 25, 20, 10, 10, 5, 5, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.coercions", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.visitors", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.ddl", "sqlalchemy.sql.elements", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.util.typing", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.row", "sqlalchemy.sql", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "collections", "copy", "itertools", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "_typeshed", "abc", "sqlalchemy.engine", "sqlalchemy.engine._py_row", "sqlalchemy.inspection", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.util._collections", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types"], "hash": "5635646a4cf0ddcc1985ea12d68199c2a82346ca", "id": "sqlalchemy.sql.util", "ignore_all": true, "interface_hash": "2167fe130c16f297d88991b0c59af35ab529582d", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/sql/util.py", "plugin_data": null, "size": 48128, "suppressed": [], "version_id": "1.16.1"}