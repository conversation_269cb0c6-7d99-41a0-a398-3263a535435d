{"data_mtime": 1751559210, "dep_lines": [29, 30, 33, 36, 37, 41, 44, 51, 52, 53, 29, 34, 35, 14, 16, 17, 34, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 5, 25, 25, 25, 25, 25, 20, 10, 10, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.roles", "sqlalchemy.sql.base", "sqlalchemy.sql.elements", "sqlalchemy.util.topological", "sqlalchemy.util.typing", "sqlalchemy.sql.compiler", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.sql", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "contextlib", "typing", "sqlalchemy", "builtins", "_collections_abc", "_frozen_importlib", "abc", "enum", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "c0b5ecac4e6a8bbe22512f2b89752cbc7f7589c9", "id": "sqlalchemy.sql.ddl", "ignore_all": true, "interface_hash": "4def07d28777d552bbe9447326cf1feb4fca3168", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/sql/ddl.py", "plugin_data": null, "size": 47884, "suppressed": [], "version_id": "1.16.1"}