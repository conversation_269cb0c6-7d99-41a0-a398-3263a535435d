{"data_mtime": 1751559210, "dep_lines": [31, 32, 33, 34, 35, 36, 37, 41, 45, 11, 31, 42, 43, 44, 9, 11, 12, 13, 14, 15, 16, 18, 29, 42, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 10, 10, 5, 5, 5, 10, 20, 10, 10, 10, 5, 20, 10, 10, 10, 10, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.cache_key", "sqlalchemy.sql.coercions", "sqlalchemy.sql.elements", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.visitors", "sqlalchemy.sql.base", "sqlalchemy.sql.operators", "sqlalchemy.util.typing", "collections.abc", "sqlalchemy.sql", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.util", "__future__", "collections", "inspect", "itertools", "operator", "threading", "types", "typing", "weakref", "sqlalchemy", "builtins", "_collections_abc", "_frozen_importlib", "_thread", "_typeshed", "abc", "enum", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql._py_util", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers"], "hash": "6e7642cbd379a1f2b3e0b762f1e03fac2c9e720e", "id": "sqlalchemy.sql.lambdas", "ignore_all": true, "interface_hash": "823e265220e1b6d2084e5d43b68382cbfd221a4c", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/sql/lambdas.py", "plugin_data": null, "size": 49196, "suppressed": [], "version_id": "1.16.1"}