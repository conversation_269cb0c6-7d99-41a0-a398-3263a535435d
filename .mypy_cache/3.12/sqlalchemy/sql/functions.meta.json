{"data_mtime": 1751559210, "dep_lines": [29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 43, 56, 61, 77, 78, 79, 81, 29, 62, 11, 13, 14, 15, 62, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 5, 5, 10, 5, 5, 5, 5, 5, 25, 25, 25, 25, 20, 10, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.annotation", "sqlalchemy.sql.coercions", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.type_api", "sqlalchemy.sql.util", "sqlalchemy.sql._typing", "sqlalchemy.sql.base", "sqlalchemy.sql.elements", "sqlalchemy.sql.selectable", "sqlalchemy.sql.visitors", "sqlalchemy.engine.base", "sqlalchemy.engine.cursor", "sqlalchemy.engine.interfaces", "sqlalchemy.util.typing", "sqlalchemy.sql", "sqlalchemy.util", "__future__", "datetime", "decimal", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "_typeshed", "abc", "collections", "enum", "sqlalchemy.engine", "sqlalchemy.engine.result", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.traversals", "sqlalchemy.util.langhelpers", "types"], "hash": "59d375bc0e0142e2d101354538d0ef29ea91beae", "id": "sqlalchemy.sql.functions", "ignore_all": true, "interface_hash": "88254254bc4724f26dbb3f12f78336985c965556", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/sql/functions.py", "plugin_data": null, "size": 65088, "suppressed": [], "version_id": "1.16.1"}