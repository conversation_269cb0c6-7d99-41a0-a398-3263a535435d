{"data_mtime": 1751559210, "dep_lines": [41, 42, 43, 44, 45, 46, 47, 48, 57, 59, 81, 94, 101, 134, 135, 142, 143, 41, 98, 99, 14, 16, 17, 18, 19, 98, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 25, 25, 25, 25, 20, 10, 5, 5, 10, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.cache_key", "sqlalchemy.sql.coercions", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.elements", "sqlalchemy.sql.sqltypes", "sqlalchemy.util.typing", "sqlalchemy.sql.compiler", "sqlalchemy.sql.dml", "sqlalchemy.sql.functions", "sqlalchemy.sql.schema", "sqlalchemy.sql", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "collections", "enum", "itertools", "typing", "sqlalchemy", "builtins", "_collections_abc", "_frozen_importlib", "abc", "datetime", "decimal", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.util", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types", "uuid"], "hash": "4ea74c97cd59c5f355260e261871056655b60f93", "id": "sqlalchemy.sql.selectable", "ignore_all": true, "interface_hash": "3733131bec24500735714a222d60411ab72e4897", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/sql/selectable.py", "plugin_data": null, "size": 241055, "suppressed": [], "version_id": "1.16.1"}