{"data_mtime": 1751559210, "dep_lines": [60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 80, 84, 90, 94, 95, 96, 128, 133, 134, 2318, 29, 60, 81, 82, 2318, 26, 28, 30, 31, 32, 33, 34, 35, 36, 37, 81, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 10, 10, 10, 10, 10, 5, 10, 5, 5, 5, 25, 25, 25, 25, 25, 25, 25, 20, 10, 20, 10, 5, 20, 5, 10, 10, 5, 10, 10, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.base", "sqlalchemy.sql.coercions", "sqlalchemy.sql.crud", "sqlalchemy.sql.elements", "sqlalchemy.sql.functions", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.util", "sqlalchemy.sql._typing", "sqlalchemy.sql.visitors", "sqlalchemy.util.typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.ddl", "sqlalchemy.sql.dml", "sqlalchemy.sql.type_api", "sqlalchemy.engine.cursor", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.result", "collections.abc", "sqlalchemy.sql", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "collections", "contextlib", "enum", "functools", "itertools", "operator", "re", "time", "typing", "sqlalchemy", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql._py_util", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.traversals", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types"], "hash": "522c4934b2d08b1fe97f72e1dda7758d85e8b773", "id": "sqlalchemy.sql.compiler", "ignore_all": true, "interface_hash": "2dd8d99951a1a5df444eb2bc3ea91909cd302468", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/sql/compiler.py", "plugin_data": null, "size": 280383, "suppressed": [], "version_id": "1.16.1"}