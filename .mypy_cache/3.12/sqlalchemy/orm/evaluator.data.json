{".class": "MypyFile", "_fullname": "sqlalchemy.orm.evaluator", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Concatenable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Concatenable", "kind": "Gdef"}, "Integer": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Integer", "kind": "Gdef"}, "LoaderCallableStatus": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.LoaderCallableStatus", "kind": "Gdef"}, "Numeric": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Numeric", "kind": "Gdef"}, "PassiveFlag": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.PassiveFlag", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "UnevaluatableError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.exc.InvalidRequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.evaluator.UnevaluatableError", "name": "UnevaluatableError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator.UnevaluatableError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.evaluator", "mro": ["sqlalchemy.orm.evaluator.UnevaluatableError", "sqlalchemy.exc.InvalidRequestError", "sqlalchemy.exc.SQLAlchemyError", "sqlalchemy.exc.HasDescriptionCode", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.evaluator.UnevaluatableError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.evaluator.UnevaluatableError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_EXPIRED_OBJECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.evaluator._EXPIRED_OBJECT", "name": "_EXPIRED_OBJECT", "setter_type": null, "type": "sqlalchemy.orm.evaluator._ExpiredObject"}}, "_EvaluatorCompiler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler", "name": "_EvaluatorCompiler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.evaluator", "mro": ["sqlalchemy.orm.evaluator._EvaluatorCompiler", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "target_cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.__init__", "name": "__init__", "type": null}}, "_straight_evaluate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "operator", "eval_left", "eval_right", "clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler._straight_evaluate", "name": "_straight_evaluate", "type": null}}, "_straight_evaluate_numeric_only": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "operator", "eval_left", "eval_right", "clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler._straight_evaluate_numeric_only", "name": "_straight_evaluate_numeric_only", "type": null}}, "process": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "clause", "clauses"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.process", "name": "process", "type": null}}, "target_cls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.target_cls", "name": "target_cls", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "visit_add_binary_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_add_binary_op", "name": "visit_add_binary_op", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "operator", "eval_left", "eval_right", "clause"], "arg_types": ["sqlalchemy.orm.evaluator._EvaluatorCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_and_clauselist_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "operator", "evaluators", "clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_and_clauselist_op", "name": "visit_and_clauselist_op", "type": null}}, "visit_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_binary", "name": "visit_binary", "type": null}}, "visit_bindparam": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_bindparam", "name": "visit_bindparam", "type": null}}, "visit_clauselist": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_clauselist", "name": "visit_clauselist", "type": null}}, "visit_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_column", "name": "visit_column", "type": null}}, "visit_comma_op_clauselist_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "operator", "evaluators", "clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_comma_op_clauselist_op", "name": "visit_comma_op_clauselist_op", "type": null}}, "visit_concat_op_binary_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "operator", "eval_left", "eval_right", "clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_concat_op_binary_op", "name": "visit_concat_op_binary_op", "type": null}}, "visit_custom_op_binary_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "operator", "eval_left", "eval_right", "clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_custom_op_binary_op", "name": "visit_custom_op_binary_op", "type": null}}, "visit_endswith_op_binary_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "operator", "eval_left", "eval_right", "clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_endswith_op_binary_op", "name": "visit_endswith_op_binary_op", "type": null}}, "visit_eq_binary_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_eq_binary_op", "name": "visit_eq_binary_op", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "operator", "eval_left", "eval_right", "clause"], "arg_types": ["sqlalchemy.orm.evaluator._EvaluatorCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_expression_clauselist": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_expression_clauselist", "name": "visit_expression_clauselist", "type": null}}, "visit_false": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_false", "name": "visit_false", "type": null}}, "visit_ge_binary_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_ge_binary_op", "name": "visit_ge_binary_op", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "operator", "eval_left", "eval_right", "clause"], "arg_types": ["sqlalchemy.orm.evaluator._EvaluatorCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_grouping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_grouping", "name": "visit_grouping", "type": null}}, "visit_gt_binary_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_gt_binary_op", "name": "visit_gt_binary_op", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "operator", "eval_left", "eval_right", "clause"], "arg_types": ["sqlalchemy.orm.evaluator._EvaluatorCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_in_op_binary_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "operator", "eval_left", "eval_right", "clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_in_op_binary_op", "name": "visit_in_op_binary_op", "type": null}}, "visit_is_binary_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "operator", "eval_left", "eval_right", "clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_is_binary_op", "name": "visit_is_binary_op", "type": null}}, "visit_is_not_binary_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "operator", "eval_left", "eval_right", "clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_is_not_binary_op", "name": "visit_is_not_binary_op", "type": null}}, "visit_le_binary_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_le_binary_op", "name": "visit_le_binary_op", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "operator", "eval_left", "eval_right", "clause"], "arg_types": ["sqlalchemy.orm.evaluator._EvaluatorCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_lt_binary_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_lt_binary_op", "name": "visit_lt_binary_op", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "operator", "eval_left", "eval_right", "clause"], "arg_types": ["sqlalchemy.orm.evaluator._EvaluatorCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_mod_binary_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_mod_binary_op", "name": "visit_mod_binary_op", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "operator", "eval_left", "eval_right", "clause"], "arg_types": ["sqlalchemy.orm.evaluator._EvaluatorCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_mul_binary_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_mul_binary_op", "name": "visit_mul_binary_op", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "operator", "eval_left", "eval_right", "clause"], "arg_types": ["sqlalchemy.orm.evaluator._EvaluatorCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_ne_binary_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_ne_binary_op", "name": "visit_ne_binary_op", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "operator", "eval_left", "eval_right", "clause"], "arg_types": ["sqlalchemy.orm.evaluator._EvaluatorCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_not_in_op_binary_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "operator", "eval_left", "eval_right", "clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_not_in_op_binary_op", "name": "visit_not_in_op_binary_op", "type": null}}, "visit_null": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_null", "name": "visit_null", "type": null}}, "visit_or_clauselist_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "operator", "evaluators", "clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_or_clauselist_op", "name": "visit_or_clauselist_op", "type": null}}, "visit_startswith_op_binary_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "operator", "eval_left", "eval_right", "clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_startswith_op_binary_op", "name": "visit_startswith_op_binary_op", "type": null}}, "visit_sub_binary_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_sub_binary_op", "name": "visit_sub_binary_op", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "operator", "eval_left", "eval_right", "clause"], "arg_types": ["sqlalchemy.orm.evaluator._EvaluatorCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_true": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_true", "name": "visit_true", "type": null}}, "visit_truediv_binary_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_truediv_binary_op", "name": "visit_truediv_binary_op", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "operator", "eval_left", "eval_right", "clause"], "arg_types": ["sqlalchemy.orm.evaluator._EvaluatorCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_tuple", "name": "visit_tuple", "type": null}}, "visit_unary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.visit_unary", "name": "visit_unary", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.evaluator._EvaluatorCompiler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.evaluator._EvaluatorCompiler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ExpiredObject": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.operators.ColumnOperators"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.evaluator._ExpiredObject", "name": "_ExpiredObject", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._ExpiredObject", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.evaluator", "mro": ["sqlalchemy.orm.evaluator._ExpiredObject", "sqlalchemy.sql.operators.ColumnOperators", "sqlalchemy.sql.operators.Operators", "builtins.object"], "names": {".class": "SymbolTable", "operate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "arg", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._ExpiredObject.operate", "name": "operate", "type": null}}, "reverse_operate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "arg", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._ExpiredObject.reverse_operate", "name": "reverse_operate", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.evaluator._ExpiredObject.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.evaluator._ExpiredObject", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_NO_OBJECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.evaluator._NO_OBJECT", "name": "_NO_OBJECT", "setter_type": null, "type": "sqlalchemy.orm.evaluator._NoObject"}}, "_NoObject": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.operators.ColumnOperators"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.evaluator._NoObject", "name": "_NoObject", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._NoObject", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.evaluator", "mro": ["sqlalchemy.orm.evaluator._NoObject", "sqlalchemy.sql.operators.ColumnOperators", "sqlalchemy.sql.operators.Operators", "builtins.object"], "names": {".class": "SymbolTable", "operate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "arg", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._NoObject.operate", "name": "operate", "type": null}}, "reverse_operate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "arg", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator._NoObject.reverse_operate", "name": "reverse_operate", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.evaluator._NoObject.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.evaluator._NoObject", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.evaluator.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.evaluator.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.evaluator.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__getattr__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.evaluator.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__getattr__", "ret_type": {".class": "TypeType", "item": "sqlalchemy.orm.evaluator._EvaluatorCompiler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.evaluator.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.evaluator.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.evaluator.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "and_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.and_", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.inspection.inspect", "kind": "Gdef"}, "operators": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.operators", "kind": "Gdef"}, "orm_exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.exc", "kind": "Gdef"}, "warn_deprecated": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.deprecations.warn_deprecated", "kind": "Gdef"}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/orm/evaluator.py"}