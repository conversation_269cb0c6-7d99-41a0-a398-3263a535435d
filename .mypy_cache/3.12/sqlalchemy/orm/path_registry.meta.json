{"data_mtime": 1751559210, "dep_lines": [29, 30, 33, 34, 38, 39, 40, 41, 43, 45, 29, 31, 32, 33, 11, 13, 14, 15, 16, 17, 31, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 5, 25, 25, 25, 25, 25, 25, 20, 10, 10, 20, 5, 5, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.base", "sqlalchemy.orm._typing", "sqlalchemy.sql.visitors", "sqlalchemy.sql.cache_key", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.mapper", "sqlalchemy.orm.relationships", "sqlalchemy.orm.util", "sqlalchemy.sql.elements", "sqlalchemy.util.typing", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.sql", "__future__", "functools", "itertools", "logging", "operator", "typing", "sqlalchemy", "builtins", "_collections_abc", "_frozen_importlib", "abc", "enum", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm.query", "sqlalchemy.sql._py_util", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.util.langhelpers", "types"], "hash": "fd1386392deebcaa11d69b35475338811cb47488", "id": "sqlalchemy.orm.path_registry", "ignore_all": true, "interface_hash": "21e4e32c859a8e5ab3539409950e3506947cf731", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/orm/path_registry.py", "plugin_data": null, "size": 25916, "suppressed": [], "version_id": "1.16.1"}