{".class": "MypyFile", "_fullname": "sqlalchemy.orm.interfaces", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbstractEntityRegistry": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.path_registry.AbstractEntityRegistry", "kind": "Gdef"}, "AliasedInsp": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util.AliasedInsp", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef"}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef"}, "ColumnElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnElement", "kind": "Gdef"}, "ColumnOperators": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.operators.ColumnOperators", "kind": "Gdef"}, "CompileStateOption": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.orm.interfaces.ORMOption"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.interfaces.CompileStateOption", "name": "CompileStateOption", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.interfaces.CompileStateOption", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.interfaces", "mro": ["sqlalchemy.orm.interfaces.CompileStateOption", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.orm.interfaces.ORMOption", "sqlalchemy.sql.base.ExecutableOption", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.HasTraverseInternals", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.interfaces.CompileStateOption.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_is_compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.CompileStateOption._is_compile_state", "name": "_is_compile_state", "setter_type": null, "type": "builtins.bool"}}, "process_compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "compile_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.CompileStateOption.process_compile_state", "name": "process_compile_state", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "compile_state"], "arg_types": ["sqlalchemy.orm.interfaces.CompileStateOption", "sqlalchemy.orm.context.ORMCompileState"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "process_compile_state of CompileStateOption", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "process_compile_state_replaced_entities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "compile_state", "mapper_entities"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.CompileStateOption.process_compile_state_replaced_entities", "name": "process_compile_state_replaced_entities", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "compile_state", "mapper_entities"], "arg_types": ["sqlalchemy.orm.interfaces.CompileStateOption", "sqlalchemy.orm.context.ORMCompileState", {".class": "Instance", "args": ["sqlalchemy.orm.context._MapperEntity"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "process_compile_state_replaced_entities of CompileStateOption", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces.CompileStateOption.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.interfaces.CompileStateOption", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CriteriaOption": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.interfaces.CompileStateOption"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.interfaces.CriteriaOption", "name": "CriteriaOption", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.interfaces.CriteriaOption", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.interfaces", "mro": ["sqlalchemy.orm.interfaces.CriteriaOption", "sqlalchemy.orm.interfaces.CompileStateOption", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.orm.interfaces.ORMOption", "sqlalchemy.sql.base.ExecutableOption", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.HasTraverseInternals", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.interfaces.CriteriaOption.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_is_criteria_option": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.CriteriaOption._is_criteria_option", "name": "_is_criteria_option", "setter_type": null, "type": "builtins.bool"}}, "get_global_criteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.CriteriaOption.get_global_criteria", "name": "get_global_criteria", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attributes"], "arg_types": ["sqlalchemy.orm.interfaces.CriteriaOption", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_global_criteria of CriteriaOption", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces.CriteriaOption.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.interfaces.CriteriaOption", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EXT_CONTINUE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.EXT_CONTINUE", "kind": "Gdef"}, "EXT_SKIP": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.EXT_SKIP", "kind": "Gdef"}, "EXT_STOP": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.EXT_STOP", "kind": "Gdef"}, "ExecutableOption": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.ExecutableOption", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "HasCacheKey": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.cache_key.HasCacheKey", "kind": "Gdef"}, "InspectionAttr": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.InspectionAttr", "kind": "Gdef"}, "InspectionAttrInfo": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.InspectionAttrInfo", "kind": "Gdef"}, "InstanceState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.state.InstanceState", "kind": "Gdef"}, "InstrumentedAttribute": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes.InstrumentedAttribute", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "LoaderOption": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.interfaces.CompileStateOption"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.interfaces.LoaderOption", "name": "LoaderOption", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.interfaces.LoaderOption", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.interfaces", "mro": ["sqlalchemy.orm.interfaces.LoaderOption", "sqlalchemy.orm.interfaces.CompileStateOption", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.orm.interfaces.ORMOption", "sqlalchemy.sql.base.ExecutableOption", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.HasTraverseInternals", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.interfaces.LoaderOption.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "process_compile_state_replaced_entities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "compile_state", "mapper_entities"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.LoaderOption.process_compile_state_replaced_entities", "name": "process_compile_state_replaced_entities", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "compile_state", "mapper_entities"], "arg_types": ["sqlalchemy.orm.interfaces.LoaderOption", "sqlalchemy.orm.context.ORMCompileState", {".class": "Instance", "args": ["sqlalchemy.orm.context._MapperEntity"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "process_compile_state_replaced_entities of LoaderOption", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces.LoaderOption.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.interfaces.LoaderOption", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LoaderStrategy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.interfaces.LoaderStrategy", "name": "LoaderStrategy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.interfaces.LoaderStrategy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.interfaces", "mro": ["sqlalchemy.orm.interfaces.LoaderStrategy", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "strategy_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.LoaderStrategy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "strategy_key"], "arg_types": ["sqlalchemy.orm.interfaces.LoaderStrategy", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.interfaces._StrategyKey"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of LoaderStrategy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.interfaces.LoaderStrategy.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.LoaderStrategy.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.orm.interfaces.LoaderStrategy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__str__ of LoaderStrategy", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_strategy_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "sqlalchemy.orm.interfaces.LoaderStrategy._strategy_keys", "name": "_strategy_keys", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.interfaces._StrategyKey"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "create_row_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "query_entity", "path", "loadopt", "mapper", "result", "adapter", "populators"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.LoaderStrategy.create_row_processor", "name": "create_row_processor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "query_entity", "path", "loadopt", "mapper", "result", "adapter", "populators"], "arg_types": ["sqlalchemy.orm.interfaces.LoaderStrategy", "sqlalchemy.orm.context.ORMCompileState", "sqlalchemy.orm.context._MapperEntity", "sqlalchemy.orm.path_registry.AbstractEntityRegistry", {".class": "UnionType", "items": ["sqlalchemy.orm.strategy_options._LoadElement", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.result.Result"}, {".class": "UnionType", "items": ["sqlalchemy.orm.util.ORMAdapter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.loading._PopulatorDict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_row_processor of LoaderStrategy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "init_class_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mapper"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.LoaderStrategy.init_class_attribute", "name": "init_class_attribute", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mapper"], "arg_types": ["sqlalchemy.orm.interfaces.LoaderStrategy", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "init_class_attribute of LoaderStrategy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_class_level": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.interfaces.LoaderStrategy.is_class_level", "name": "is_class_level", "setter_type": null, "type": "builtins.bool"}}, "key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.interfaces.LoaderStrategy.key", "name": "key", "setter_type": null, "type": "builtins.str"}}, "parent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.interfaces.LoaderStrategy.parent", "name": "parent", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}}}, "parent_property": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.interfaces.LoaderStrategy.parent_property", "name": "parent_property", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}}}, "setup_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "compile_state", "query_entity", "path", "loadopt", "adapter", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.LoaderStrategy.setup_query", "name": "setup_query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "compile_state", "query_entity", "path", "loadopt", "adapter", "kwargs"], "arg_types": ["sqlalchemy.orm.interfaces.LoaderStrategy", "sqlalchemy.orm.context.ORMCompileState", "sqlalchemy.orm.context._MapperEntity", "sqlalchemy.orm.path_registry.AbstractEntityRegistry", {".class": "UnionType", "items": ["sqlalchemy.orm.strategy_options._LoadElement", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.orm.util.ORMAdapter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "setup_query of LoaderStrategy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "strategy_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.interfaces.LoaderStrategy.strategy_key", "name": "strategy_key", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.interfaces._StrategyKey"}}}, "strategy_opts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.interfaces.LoaderStrategy.strategy_opts", "name": "strategy_opts", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces.LoaderStrategy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.interfaces.LoaderStrategy", "values": [], "variance": 0}, "slots": ["is_class_level", "key", "parent", "parent_property", "strategy_key", "strategy_opts"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MANYTOMANY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.MANYTOMANY", "kind": "Gdef"}, "MANYTOONE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.MANYTOONE", "kind": "Gdef"}, "Mapped": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.Mapped", "kind": "Gdef"}, "Mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapper.Mapper", "kind": "Gdef"}, "MapperOption": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.interfaces.ORMOption"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.interfaces.MapperOption", "name": "MapperOption", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.interfaces.MapperOption", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.interfaces", "mro": ["sqlalchemy.orm.interfaces.MapperOption", "sqlalchemy.orm.interfaces.ORMOption", "sqlalchemy.sql.base.ExecutableOption", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.HasTraverseInternals", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.interfaces.MapperOption.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_is_legacy_option": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.MapperOption._is_legacy_option", "name": "_is_legacy_option", "setter_type": null, "type": "builtins.bool"}}, "process_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "query"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.MapperOption.process_query", "name": "process_query", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "query"], "arg_types": ["sqlalchemy.orm.interfaces.MapperOption", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.query.Query"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "process_query of MapperOption", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "process_query_conditionally": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "query"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.MapperOption.process_query_conditionally", "name": "process_query_conditionally", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "query"], "arg_types": ["sqlalchemy.orm.interfaces.MapperOption", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.query.Query"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "process_query_conditionally of MapperOption", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "propagate_to_loaders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.MapperOption.propagate_to_loaders", "name": "propagate_to_loaders", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces.MapperOption.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.interfaces.MapperOption", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MapperProperty": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.orm.interfaces._DCAttributeOptions", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.MapperProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base._MappedAttribute"}, "sqlalchemy.orm.base.InspectionAttrInfo", "sqlalchemy.util.langhelpers.MemoizedSlots"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.interfaces.MapperProperty", "name": "MapperProperty", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.MapperProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.interfaces.MapperProperty", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.interfaces", "mro": ["sqlalchemy.orm.interfaces.MapperProperty", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.orm.interfaces._DCAttributeOptions", "sqlalchemy.orm.base._MappedAttribute", "sqlalchemy.util.langhelpers.TypingOnly", "sqlalchemy.orm.base.InspectionAttrInfo", "sqlalchemy.orm.base.InspectionAttr", "sqlalchemy.util.langhelpers.MemoizedSlots", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "attribute_options", "_assume_readonly_dc_attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "attribute_options", "_assume_readonly_dc_attributes"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.MapperProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.interfaces._AttributeOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of MapperProperty", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.MapperProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of MapperProperty", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_cache_key_traversal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty._cache_key_traversal", "name": "_cache_key_traversal", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.visitors._TraverseInternalsType"}}}, "_configure_finished": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty._configure_finished", "name": "_configure_finished", "setter_type": null, "type": "builtins.bool"}}, "_configure_started": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty._configure_started", "name": "_configure_started", "setter_type": null, "type": "builtins.bool"}}, "_is_relationship": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty._is_relationship", "name": "_is_relationship", "setter_type": null, "type": "builtins.bool"}}, "_links_to_entity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty._links_to_entity", "name": "_links_to_entity", "setter_type": null, "type": "builtins.bool"}}, "_memoized_attr_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty._memoized_attr_info", "name": "_memoized_attr_info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.MapperProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_memoized_attr_info of MapperProperty", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cascade_iterator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "type_", "state", "dict_", "visited_states", "halt_on"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty.cascade_iterator", "name": "cascade_iterator", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "type_", "state", "dict_", "visited_states", "halt_on"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.MapperProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}, "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.state.InstanceState"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing._InstanceDict"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.state.InstanceState"}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.state.InstanceState"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cascade_iterator of MapperProperty", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.object", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.state.InstanceState"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing._InstanceDict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "class_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty.class_attribute", "name": "class_attribute", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.MapperProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "class_attribute of MapperProperty", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.MapperProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.attributes.InstrumentedAttribute"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty.class_attribute", "name": "class_attribute", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.MapperProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "class_attribute of MapperProperty", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.MapperProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.attributes.InstrumentedAttribute"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "comparator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty.comparator", "name": "comparator", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.MapperProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}}}, "create_row_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "query_entity", "path", "mapper", "result", "adapter", "populators"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty.create_row_processor", "name": "create_row_processor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "query_entity", "path", "mapper", "result", "adapter", "populators"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.MapperProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}, "sqlalchemy.orm.context.ORMCompileState", "sqlalchemy.orm.context._MapperEntity", "sqlalchemy.orm.path_registry.AbstractEntityRegistry", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.result.Result"}, {".class": "UnionType", "items": ["sqlalchemy.orm.util.ORMAdapter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.loading._PopulatorDict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_row_processor of MapperProperty", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "do_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty.do_init", "name": "do_init", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.MapperProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "do_init of MapperProperty", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "doc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty.doc", "name": "doc", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty.info", "name": "info", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}}}, "init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty.init", "name": "init", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.MapperProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "init of MapperProperty", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "instrument_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mapper"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty.instrument_class", "name": "instrument_class", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mapper"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.MapperProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "instrument_class of MapperProperty", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_property": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty.is_property", "name": "is_property", "setter_type": null, "type": "builtins.bool"}}, "key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty.key", "name": "key", "setter_type": null, "type": "builtins.str"}}, "merge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "session", "source_state", "source_dict", "dest_state", "dest_dict", "load", "_recursive", "_resolve_conflict_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty.merge", "name": "merge", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "session", "source_state", "source_dict", "dest_state", "dest_dict", "load", "_recursive", "_resolve_conflict_map"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.MapperProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}, "sqlalchemy.orm.session.Session", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.state.InstanceState"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing._InstanceDict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.state.InstanceState"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing._InstanceDict"}, "builtins.bool", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._IdentityKeyType"}, "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "merge of MapperProperty", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty.parent", "name": "parent", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}}}, "post_instrument_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mapper"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty.post_instrument_class", "name": "post_instrument_class", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mapper"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.MapperProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_instrument_class of MapperProperty", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "init"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty.set_parent", "name": "set_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "init"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.MapperProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_parent of MapperProperty", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "context", "query_entity", "path", "adapter", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.MapperProperty.setup", "name": "setup", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "context", "query_entity", "path", "adapter", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.MapperProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}, "sqlalchemy.orm.context.ORMCompileState", "sqlalchemy.orm.context._MapperEntity", "sqlalchemy.orm.path_registry.AbstractEntityRegistry", {".class": "UnionType", "items": ["sqlalchemy.orm.util.ORMAdapter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "setup of MapperProperty", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces.MapperProperty.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.MapperProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}, "values": [], "variance": 0}, "slots": ["_attribute_options", "_configure_finished", "_configure_started", "_has_dataclass_arguments", "doc", "info", "key", "parent"], "tuple_type": null, "type_vars": ["_T"], "typeddict_type": null}}, "NO_KEY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.NO_KEY", "kind": "Gdef"}, "NO_VALUE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.NO_VALUE", "kind": "Gdef"}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef"}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef"}, "NotExtension": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.NotExtension", "kind": "Gdef"}, "ONETOMANY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.ONETOMANY", "kind": "Gdef"}, "ORMAdapter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util.ORMAdapter", "kind": "Gdef"}, "ORMColumnDescription": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.interfaces.ORMColumnDescription", "name": "ORMColumnDescription", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.interfaces.ORMColumnDescription", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.interfaces", "mro": ["sqlalchemy.orm.interfaces.ORMColumnDescription", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["name", "builtins.str"], ["type", {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "uses_pep604_syntax": false}], ["aliased", "builtins.bool"], ["expr", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql._typing._ColumnsClauseArgument"}], ["entity", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.sql._typing._ColumnsClauseArgument"}, {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": ["aliased", "entity", "expr", "name", "type"]}}}, "ORMColumnsClauseRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.ColumnsClauseRole", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.ORMColumnsClauseRole", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.TypedColumnsClauseRole"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.interfaces.ORMColumnsClauseRole", "name": "ORMColumnsClauseRole", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.ORMColumnsClauseRole", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.interfaces.ORMColumnsClauseRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.interfaces", "mro": ["sqlalchemy.orm.interfaces.ORMColumnsClauseRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.interfaces.ORMColumnsClauseRole.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.ORMColumnsClauseRole._role_name", "name": "_role_name", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces.ORMColumnsClauseRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.ORMColumnsClauseRole", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.ORMColumnsClauseRole"}, "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": ["_T"], "typeddict_type": null}}, "ORMCompileState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context.ORMCompileState", "kind": "Gdef"}, "ORMEntityColumnsClauseRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.ORMEntityColumnsClauseRole", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.ORMColumnsClauseRole"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.interfaces.ORMEntityColumnsClauseRole", "name": "ORMEntityColumnsClauseRole", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.ORMEntityColumnsClauseRole", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.interfaces.ORMEntityColumnsClauseRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.interfaces", "mro": ["sqlalchemy.orm.interfaces.ORMEntityColumnsClauseRole", "sqlalchemy.orm.interfaces.ORMColumnsClauseRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.interfaces.ORMEntityColumnsClauseRole.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.ORMEntityColumnsClauseRole._role_name", "name": "_role_name", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces.ORMEntityColumnsClauseRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.ORMEntityColumnsClauseRole", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.ORMEntityColumnsClauseRole"}, "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": ["_T"], "typeddict_type": null}}, "ORMFromClauseRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.StrictFromClauseRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.interfaces.ORMFromClauseRole", "name": "ORMFromClauseRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.interfaces.ORMFromClauseRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.interfaces", "mro": ["sqlalchemy.orm.interfaces.ORMFromClauseRole", "sqlalchemy.sql.roles.StrictFromClauseRole", "sqlalchemy.sql.roles.FromClauseRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.JoinTargetRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.interfaces.ORMFromClauseRole.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.ORMFromClauseRole._role_name", "name": "_role_name", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces.ORMFromClauseRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.interfaces.ORMFromClauseRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ORMOption": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.base.ExecutableOption"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.interfaces.ORMOption", "name": "ORMOption", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.interfaces.ORMOption", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.interfaces", "mro": ["sqlalchemy.orm.interfaces.ORMOption", "sqlalchemy.sql.base.ExecutableOption", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.HasTraverseInternals", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.interfaces.ORMOption.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_adapt_cached_option_to_uncached_option": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "uncached_opt"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.ORMOption._adapt_cached_option_to_uncached_option", "name": "_adapt_cached_option_to_uncached_option", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "uncached_opt"], "arg_types": ["sqlalchemy.orm.interfaces.ORMOption", "sqlalchemy.orm.context.QueryContext", "sqlalchemy.orm.interfaces.ORMOption"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_adapt_cached_option_to_uncached_option of ORMOption", "ret_type": "sqlalchemy.orm.interfaces.ORMOption", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.ORMOption._is_compile_state", "name": "_is_compile_state", "setter_type": null, "type": "builtins.bool"}}, "_is_core": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.ORMOption._is_core", "name": "_is_core", "setter_type": null, "type": "builtins.bool"}}, "_is_criteria_option": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.ORMOption._is_criteria_option", "name": "_is_criteria_option", "setter_type": null, "type": "builtins.bool"}}, "_is_legacy_option": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.ORMOption._is_legacy_option", "name": "_is_legacy_option", "setter_type": null, "type": "builtins.bool"}}, "_is_strategy_option": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.ORMOption._is_strategy_option", "name": "_is_strategy_option", "setter_type": null, "type": "builtins.bool"}}, "_is_user_defined": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.ORMOption._is_user_defined", "name": "_is_user_defined", "setter_type": null, "type": "builtins.bool"}}, "propagate_to_loaders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.ORMOption.propagate_to_loaders", "name": "propagate_to_loaders", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces.ORMOption.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.interfaces.ORMOption", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ORMStatementRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.StatementRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.interfaces.ORMStatementRole", "name": "ORMStatementRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.interfaces.ORMStatementRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.interfaces", "mro": ["sqlalchemy.orm.interfaces.ORMStatementRole", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.interfaces.ORMStatementRole.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.ORMStatementRole._role_name", "name": "_role_name", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces.ORMStatementRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.interfaces.ORMStatementRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OperatorType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.operators.OperatorType", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PropComparator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.SQLORMOperations"}, "sqlalchemy.sql.operators.ColumnOperators"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.interfaces.PropComparator", "name": "PropComparator", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.interfaces.PropComparator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.interfaces", "mro": ["sqlalchemy.orm.interfaces.PropComparator", "sqlalchemy.orm.base.SQLORMOperations", "sqlalchemy.sql.elements.SQLCoreOperations", "sqlalchemy.sql.operators.ColumnOperators", "sqlalchemy.sql.operators.Operators", "sqlalchemy.util.langhelpers.TypingOnly", "builtins.object"], "names": {".class": "SymbolTable", "__clause_element__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.PropComparator.__clause_element__", "name": "__clause_element__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__clause_element__ of PropComparator", "ret_type": "sqlalchemy.sql.roles.ColumnsClauseRole", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "prop", "parentmapper", "adapt_to_entity"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.PropComparator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "prop", "parentmapper", "adapt_to_entity"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.PropComparator.__init__", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.util.AliasedInsp"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of PropComparator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.PropComparator.__init__", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.interfaces.PropComparator.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.PropComparator.__visit_name__", "name": "__visit_name__", "setter_type": null, "type": "builtins.str"}}, "_adapt_to_entity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.interfaces.PropComparator._adapt_to_entity", "name": "_adapt_to_entity", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.util.AliasedInsp"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_any_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["a", "b", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "sqlalchemy.orm.interfaces.PropComparator._any_op", "name": "_any_op", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["a", "b", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_any_op of PropComparator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.interfaces.PropComparator._any_op", "name": "_any_op", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["a", "b", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_any_op of PropComparator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_bulk_update_tuples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.PropComparator._bulk_update_tuples", "name": "_bulk_update_tuples", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_bulk_update_tuples of PropComparator", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._DMLColumnArgument"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_criterion_exists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "criterion", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.PropComparator._criterion_exists", "name": "_criterion_exists", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "criterion", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_criterion_exists of PropComparator", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_has_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["left", "other", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "sqlalchemy.orm.interfaces.PropComparator._has_op", "name": "_has_op", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["left", "other", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_has_op of PropComparator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.interfaces.PropComparator._has_op", "name": "_has_op", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["left", "other", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_has_op of PropComparator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_of_type_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "class_"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "sqlalchemy.orm.interfaces.PropComparator._of_type_op", "name": "_of_type_op", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "class_"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_of_type_op of PropComparator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.interfaces.PropComparator._of_type_op", "name": "_of_type_op", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "class_"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_of_type_op of PropComparator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_parententity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.interfaces.PropComparator._parententity", "name": "_parententity", "setter_type": null, "type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}}}, "_parentmapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.PropComparator._parentmapper", "name": "_parentmapper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_parentmapper of PropComparator", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.interfaces.PropComparator._parentmapper", "name": "_parentmapper", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_parentmapper of PropComparator", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "adapt_to_entity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "adapt_to_entity"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.PropComparator.adapt_to_entity", "name": "adapt_to_entity", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "adapt_to_entity"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.util.AliasedInsp"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "adapt_to_entity of PropComparator", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "adapter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.PropComparator.adapter", "name": "adapter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "adapter of PropComparator", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm._typing._ORMAdapterProto", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.interfaces.PropComparator.adapter", "name": "adapter", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "adapter of PropComparator", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm._typing._ORMAdapterProto", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "and_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "criteria"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.PropComparator.and_", "name": "and_", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "criteria"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}, {".class": "TypeAliasType", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "and_ of PropComparator", "ret_type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "any": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "criterion", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.PropComparator.any", "name": "any", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "criterion", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "any of PropComparator", "ret_type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "any_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.PropComparator.any_op", "name": "any_op", "setter_type": null, "type": "sqlalchemy.sql.operators.OperatorType"}}, "has": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "criterion", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.PropComparator.has", "name": "has", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "criterion", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "has of PropComparator", "ret_type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.PropComparator.has_op", "name": "has_op", "setter_type": null, "type": "sqlalchemy.sql.operators.OperatorType"}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.PropComparator.info", "name": "info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "info of PropComparator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.interfaces.PropComparator.info", "name": "info", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "info of PropComparator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "of_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "class_"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.PropComparator.of_type", "name": "of_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "class_"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._EntityType"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "of_type of PropComparator", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "of_type_op": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.PropComparator.of_type_op", "name": "of_type_op", "setter_type": null, "type": "sqlalchemy.sql.operators.OperatorType"}}, "operate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "op", "other", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self", "is_mypy_only"], "fullname": "sqlalchemy.orm.interfaces.PropComparator.operate", "name": "operate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "op", "other", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}, "sqlalchemy.sql.operators.OperatorType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "operate of PropComparator", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.interfaces.PropComparator.prop", "name": "prop", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}], "extra_attrs": null, "type_ref": "sqlalchemy.util.typing.RODescriptorReference"}}}, "property": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.PropComparator.property", "name": "property", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "property of PropComparator", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.interfaces.PropComparator.property", "name": "property", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "reverse_operate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "op", "other", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self", "is_mypy_only"], "fullname": "sqlalchemy.orm.interfaces.PropComparator.reverse_operate", "name": "reverse_operate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "op", "other", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}, "sqlalchemy.sql.operators.OperatorType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "reverse_operate of PropComparator", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces.PropComparator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.interfaces.PropComparator", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}, "values": [], "variance": 0}, "slots": ["_adapt_to_entity", "_parententity", "prop"], "tuple_type": null, "type_vars": ["_T_co"], "typeddict_type": null}}, "Query": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.query.Query", "kind": "Gdef"}, "QueryContext": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context.QueryContext", "kind": "Gdef"}, "RODescriptorReference": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.typing.RODescriptorReference", "kind": "Gdef"}, "RegistryType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_api.RegistryType", "kind": "Gdef"}, "RelationshipDirection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.RelationshipDirection", "kind": "Gdef"}, "Result": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.Result", "kind": "Gdef"}, "SQLORMOperations": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.SQLORMOperations", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Session": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.Session", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "StrategizedProperty": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.StrategizedProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.interfaces.StrategizedProperty", "name": "StrategizedProperty", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.StrategizedProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.interfaces.StrategizedProperty", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.interfaces", "mro": ["sqlalchemy.orm.interfaces.StrategizedProperty", "sqlalchemy.orm.interfaces.MapperProperty", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.orm.interfaces._DCAttributeOptions", "sqlalchemy.orm.base._MappedAttribute", "sqlalchemy.util.langhelpers.TypingOnly", "sqlalchemy.orm.base.InspectionAttrInfo", "sqlalchemy.orm.base.InspectionAttr", "sqlalchemy.util.langhelpers.MemoizedSlots", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.interfaces.StrategizedProperty.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_all_strategies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.StrategizedProperty._all_strategies", "name": "_all_strategies", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.interfaces._StrategyKey"}, {".class": "TypeType", "item": "sqlalchemy.orm.interfaces.LoaderStrategy"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "collections.defaultdict"}}}, "_get_context_loader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.StrategizedProperty._get_context_loader", "name": "_get_context_loader", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "path"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.StrategizedProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.StrategizedProperty"}, "sqlalchemy.orm.context.ORMCompileState", "sqlalchemy.orm.path_registry.AbstractEntityRegistry"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_context_loader of StrategizedProperty", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.strategy_options._LoadElement", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.StrategizedProperty._get_strategy", "name": "_get_strategy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.StrategizedProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.StrategizedProperty"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.interfaces._StrategyKey"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_strategy of StrategizedProperty", "ret_type": "sqlalchemy.orm.interfaces.LoaderStrategy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_memoized_attr__default_path_loader_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.StrategizedProperty._memoized_attr__default_path_loader_key", "name": "_memoized_attr__default_path_loader_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.StrategizedProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.StrategizedProperty"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_memoized_attr__default_path_loader_key of StrategizedProperty", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_memoized_attr__wildcard_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.StrategizedProperty._memoized_attr__wildcard_token", "name": "_memoized_attr__wildcard_token", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.StrategizedProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.StrategizedProperty"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_memoized_attr__wildcard_token of StrategizedProperty", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_strategies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.interfaces.StrategizedProperty._strategies", "name": "_strategies", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.interfaces._StrategyKey"}, "sqlalchemy.orm.interfaces.LoaderStrategy"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_strategy_lookup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["cls", "requesting_property", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.StrategizedProperty._strategy_lookup", "name": "_strategy_lookup", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["cls", "requesting_property", "key"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.StrategizedProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.StrategizedProperty"}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_strategy_lookup of StrategizedProperty", "ret_type": {".class": "TypeType", "item": "sqlalchemy.orm.interfaces.LoaderStrategy"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.interfaces.StrategizedProperty._strategy_lookup", "name": "_strategy_lookup", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["cls", "requesting_property", "key"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.StrategizedProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.StrategizedProperty"}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_strategy_lookup of StrategizedProperty", "ret_type": {".class": "TypeType", "item": "sqlalchemy.orm.interfaces.LoaderStrategy"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_row_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "query_entity", "path", "mapper", "result", "adapter", "populators"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.StrategizedProperty.create_row_processor", "name": "create_row_processor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "query_entity", "path", "mapper", "result", "adapter", "populators"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.StrategizedProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.StrategizedProperty"}, "sqlalchemy.orm.context.ORMCompileState", "sqlalchemy.orm.context._MapperEntity", "sqlalchemy.orm.path_registry.AbstractEntityRegistry", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.result.Result"}, {".class": "UnionType", "items": ["sqlalchemy.orm.util.ORMAdapter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.loading._PopulatorDict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_row_processor of StrategizedProperty", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "do_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.StrategizedProperty.do_init", "name": "do_init", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.StrategizedProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.StrategizedProperty"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "do_init of StrategizedProperty", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inherit_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.StrategizedProperty.inherit_cache", "name": "inherit_cache", "setter_type": null, "type": "builtins.bool"}}, "post_instrument_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mapper"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.StrategizedProperty.post_instrument_class", "name": "post_instrument_class", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mapper"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.StrategizedProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.StrategizedProperty"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_instrument_class of StrategizedProperty", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "context", "query_entity", "path", "adapter", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.StrategizedProperty.setup", "name": "setup", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "context", "query_entity", "path", "adapter", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.StrategizedProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.StrategizedProperty"}, "sqlalchemy.orm.context.ORMCompileState", "sqlalchemy.orm.context._MapperEntity", "sqlalchemy.orm.path_registry.AbstractEntityRegistry", {".class": "UnionType", "items": ["sqlalchemy.orm.util.ORMAdapter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "setup of StrategizedProperty", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "strategy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.interfaces.StrategizedProperty.strategy", "name": "strategy", "setter_type": null, "type": "sqlalchemy.orm.interfaces.LoaderStrategy"}}, "strategy_for": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["cls", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.StrategizedProperty.strategy_for", "name": "strategy_for", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["cls", "kw"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.StrategizedProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.StrategizedProperty"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "strategy_for of StrategizedProperty", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._TLS", "id": -1, "name": "_TLS", "namespace": "", "upper_bound": {".class": "TypeType", "item": "sqlalchemy.orm.interfaces.LoaderStrategy"}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._TLS", "id": -1, "name": "_TLS", "namespace": "", "upper_bound": {".class": "TypeType", "item": "sqlalchemy.orm.interfaces.LoaderStrategy"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._TLS", "id": -1, "name": "_TLS", "namespace": "", "upper_bound": {".class": "TypeType", "item": "sqlalchemy.orm.interfaces.LoaderStrategy"}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.interfaces.StrategizedProperty.strategy_for", "name": "strategy_for", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["cls", "kw"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.StrategizedProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.StrategizedProperty"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "strategy_for of StrategizedProperty", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._TLS", "id": -1, "name": "_TLS", "namespace": "", "upper_bound": {".class": "TypeType", "item": "sqlalchemy.orm.interfaces.LoaderStrategy"}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._TLS", "id": -1, "name": "_TLS", "namespace": "", "upper_bound": {".class": "TypeType", "item": "sqlalchemy.orm.interfaces.LoaderStrategy"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._TLS", "id": -1, "name": "_TLS", "namespace": "", "upper_bound": {".class": "TypeType", "item": "sqlalchemy.orm.interfaces.LoaderStrategy"}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "strategy_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.interfaces.StrategizedProperty.strategy_key", "name": "strategy_key", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.interfaces._StrategyKey"}}}, "strategy_wildcard_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "sqlalchemy.orm.interfaces.StrategizedProperty.strategy_wildcard_key", "name": "strategy_wildcard_key", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces.StrategizedProperty.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces.StrategizedProperty", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.StrategizedProperty"}, "values": [], "variance": 0}, "slots": ["_attribute_options", "_configure_finished", "_configure_started", "_default_path_loader_key", "_has_dataclass_arguments", "_strategies", "_wildcard_token", "doc", "info", "key", "parent", "strategy", "strategy_key"], "tuple_type": null, "type_vars": ["_T"], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeEngine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api.TypeEngine", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UserDefinedOption": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.interfaces.ORMOption"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.interfaces.UserDefinedOption", "name": "UserDefinedOption", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.interfaces.UserDefinedOption", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.interfaces", "mro": ["sqlalchemy.orm.interfaces.UserDefinedOption", "sqlalchemy.orm.interfaces.ORMOption", "sqlalchemy.sql.base.ExecutableOption", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.HasTraverseInternals", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "payload"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces.UserDefinedOption.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "payload"], "arg_types": ["sqlalchemy.orm.interfaces.UserDefinedOption", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of UserDefinedOption", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.interfaces.UserDefinedOption.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_is_legacy_option": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.UserDefinedOption._is_legacy_option", "name": "_is_legacy_option", "setter_type": null, "type": "builtins.bool"}}, "_is_user_defined": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.UserDefinedOption._is_user_defined", "name": "_is_user_defined", "setter_type": null, "type": "builtins.bool"}}, "payload": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.interfaces.UserDefinedOption.payload", "name": "payload", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "propagate_to_loaders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces.UserDefinedOption.propagate_to_loaders", "name": "propagate_to_loaders", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces.UserDefinedOption.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.interfaces.UserDefinedOption", "values": [], "variance": 0}, "slots": ["payload"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_AnnotationScanType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.typing._AnnotationScanType", "kind": "Gdef"}, "_AttributeOptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.interfaces._AttributeOptions", "name": "_AttributeOptions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "sqlalchemy.orm.interfaces._AttributeOptions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["dataclasses_init", "dataclasses_repr", "dataclasses_default", "dataclasses_default_factory", "dataclasses_compare", "dataclasses_kw_only", "dataclasses_hash"]}}, "module_name": "sqlalchemy.orm.interfaces", "mro": ["sqlalchemy.orm.interfaces._AttributeOptions", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.interfaces._AttributeOptions.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.interfaces._AttributeOptions.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.interfaces._AttributeOptions.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "dataclasses_init"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataclasses_repr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataclasses_default"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataclasses_default_factory"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataclasses_compare"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataclasses_kw_only"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataclasses_hash"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "dataclasses_init", "dataclasses_repr", "dataclasses_default", "dataclasses_default_factory", "dataclasses_compare", "dataclasses_kw_only", "dataclasses_hash"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "sqlalchemy.orm.interfaces._AttributeOptions.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "dataclasses_init", "dataclasses_repr", "dataclasses_default", "dataclasses_default_factory", "dataclasses_compare", "dataclasses_kw_only", "dataclasses_hash"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.interfaces._AttributeOptions.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__new__ of _AttributeOptions", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.interfaces._AttributeOptions.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.interfaces._AttributeOptions.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_as_dataclass_field": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._as_dataclass_field", "name": "_as_dataclass_field", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": "sqlalchemy.orm.interfaces._AttributeOptions"}, "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_as_dataclass_field of _AttributeOptions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.interfaces._AttributeOptions._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_asdict of _AttributeOptions", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.interfaces._AttributeOptions._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._field_defaults", "name": "_field_defaults", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._field_types", "name": "_field_types", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._fields", "name": "_fields", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_get_arguments_for_make_dataclass": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "key", "annotation", "mapped_container", "elem"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._get_arguments_for_make_dataclass", "name": "_get_arguments_for_make_dataclass", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "key", "annotation", "mapped_container", "elem"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": "sqlalchemy.orm.interfaces._AttributeOptions"}}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces._AttributeOptions._get_arguments_for_make_dataclass", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_arguments_for_make_dataclass of _AttributeOptions", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces._AttributeOptions._get_arguments_for_make_dataclass", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._get_arguments_for_make_dataclass", "name": "_get_arguments_for_make_dataclass", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "key", "annotation", "mapped_container", "elem"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": "sqlalchemy.orm.interfaces._AttributeOptions"}}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces._AttributeOptions._get_arguments_for_make_dataclass", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_arguments_for_make_dataclass of _AttributeOptions", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces._AttributeOptions._get_arguments_for_make_dataclass", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.interfaces._AttributeOptions._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_make of _AttributeOptions", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.interfaces._AttributeOptions._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.interfaces._AttributeOptions._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._make", "name": "_make", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.interfaces._AttributeOptions._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_make of _AttributeOptions", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.interfaces._AttributeOptions._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.interfaces._AttributeOptions._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "dataclasses_init", "dataclasses_repr", "dataclasses_default", "dataclasses_default_factory", "dataclasses_compare", "dataclasses_kw_only", "dataclasses_hash"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "dataclasses_init", "dataclasses_repr", "dataclasses_default", "dataclasses_default_factory", "dataclasses_compare", "dataclasses_kw_only", "dataclasses_hash"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.interfaces._AttributeOptions._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_replace of _AttributeOptions", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.interfaces._AttributeOptions._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.interfaces._AttributeOptions._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.interfaces._AttributeOptions._source", "name": "_source", "setter_type": null, "type": "builtins.str"}}, "dataclasses_compare": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.orm.interfaces._AttributeOptions.dataclasses_compare", "name": "dataclasses_compare", "setter_type": null, "type": {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}}}, "dataclasses_compare-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces._AttributeOptions.dataclasses_compare", "kind": "<PERSON><PERSON><PERSON>"}, "dataclasses_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.orm.interfaces._AttributeOptions.dataclasses_default", "name": "dataclasses_default", "setter_type": null, "type": {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}}}, "dataclasses_default-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces._AttributeOptions.dataclasses_default", "kind": "<PERSON><PERSON><PERSON>"}, "dataclasses_default_factory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.orm.interfaces._AttributeOptions.dataclasses_default_factory", "name": "dataclasses_default_factory", "setter_type": null, "type": {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}}}, "dataclasses_default_factory-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces._AttributeOptions.dataclasses_default_factory", "kind": "<PERSON><PERSON><PERSON>"}, "dataclasses_hash": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.orm.interfaces._AttributeOptions.dataclasses_hash", "name": "dataclasses_hash", "setter_type": null, "type": {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "dataclasses_hash-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces._AttributeOptions.dataclasses_hash", "kind": "<PERSON><PERSON><PERSON>"}, "dataclasses_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.orm.interfaces._AttributeOptions.dataclasses_init", "name": "dataclasses_init", "setter_type": null, "type": {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}}}, "dataclasses_init-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces._AttributeOptions.dataclasses_init", "kind": "<PERSON><PERSON><PERSON>"}, "dataclasses_kw_only": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.orm.interfaces._AttributeOptions.dataclasses_kw_only", "name": "dataclasses_kw_only", "setter_type": null, "type": {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}}}, "dataclasses_kw_only-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces._AttributeOptions.dataclasses_kw_only", "kind": "<PERSON><PERSON><PERSON>"}, "dataclasses_repr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.orm.interfaces._AttributeOptions.dataclasses_repr", "name": "dataclasses_repr", "setter_type": null, "type": {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}}}, "dataclasses_repr-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces._AttributeOptions.dataclasses_repr", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._AttributeOptions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": "sqlalchemy.orm.interfaces._AttributeOptions"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "_ClassScanMapperConfig": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_base._ClassScanMapperConfig", "kind": "Gdef"}, "_ColumnExpressionArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument", "kind": "Gdef"}, "_ColumnsClauseArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._ColumnsClauseArgument", "kind": "Gdef"}, "_DCAttributeOptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.interfaces._DCAttributeOptions", "name": "_DCAttributeOptions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.interfaces._DCAttributeOptions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.interfaces", "mro": ["sqlalchemy.orm.interfaces._DCAttributeOptions", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.interfaces._DCAttributeOptions.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_attribute_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.interfaces._DCAttributeOptions._attribute_options", "name": "_attribute_options", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.interfaces._AttributeOptions"}}}, "_has_dataclass_arguments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.interfaces._DCAttributeOptions._has_dataclass_arguments", "name": "_has_dataclass_arguments", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._DCAttributeOptions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.interfaces._DCAttributeOptions", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_DEFAULT_ATTRIBUTE_OPTIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces._DEFAULT_ATTRIBUTE_OPTIONS", "name": "_DEFAULT_ATTRIBUTE_OPTIONS", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": "sqlalchemy.orm.interfaces._AttributeOptions"}}}, "_DEFAULT_READONLY_ATTRIBUTE_OPTIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.interfaces._DEFAULT_READONLY_ATTRIBUTE_OPTIONS", "name": "_DEFAULT_READONLY_ATTRIBUTE_OPTIONS", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": "sqlalchemy.orm.interfaces._AttributeOptions"}}}, "_DMLColumnArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._DMLColumnArgument", "kind": "Gdef"}, "_EntityType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._EntityType", "kind": "Gdef"}, "_IdentityKeyType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._IdentityKeyType", "kind": "Gdef"}, "_InfoType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._InfoType", "kind": "Gdef"}, "_InstanceDict": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._InstanceDict", "kind": "Gdef"}, "_InternalEntityType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._InternalEntityType", "kind": "Gdef"}, "_IntrospectsAnnotations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.interfaces._IntrospectsAnnotations", "name": "_IntrospectsAnnotations", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.interfaces._IntrospectsAnnotations", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.interfaces", "mro": ["sqlalchemy.orm.interfaces._IntrospectsAnnotations", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.interfaces._IntrospectsAnnotations.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_mapper_property_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces._IntrospectsAnnotations._mapper_property_name", "name": "_mapper_property_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.interfaces._IntrospectsAnnotations"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_mapper_property_name of _IntrospectsAnnotations", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.interfaces._IntrospectsAnnotations._mapper_property_name", "name": "_mapper_property_name", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.orm.interfaces._IntrospectsAnnotations"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_mapper_property_name of _IntrospectsAnnotations", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_raise_for_required": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces._IntrospectsAnnotations._raise_for_required", "name": "_raise_for_required", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "cls"], "arg_types": ["sqlalchemy.orm.interfaces._IntrospectsAnnotations", "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_raise_for_required of _IntrospectsAnnotations", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "declarative_scan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "decl_scan", "registry", "cls", "originating_module", "key", "mapped_container", "annotation", "extracted_mapped_annotation", "is_dataclass_field"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces._IntrospectsAnnotations.declarative_scan", "name": "declarative_scan", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "decl_scan", "registry", "cls", "originating_module", "key", "mapped_container", "annotation", "extracted_mapped_annotation", "is_dataclass_field"], "arg_types": ["sqlalchemy.orm.interfaces._IntrospectsAnnotations", "sqlalchemy.orm.decl_base._ClassScanMapperConfig", "sqlalchemy.orm.decl_api.registry", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.Mapped"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.util.typing._AnnotationScanType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "declarative_scan of _IntrospectsAnnotations", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "found_in_pep593_annotated": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces._IntrospectsAnnotations.found_in_pep593_annotated", "name": "found_in_pep593_annotated", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.interfaces._IntrospectsAnnotations"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "found_in_pep593_annotated of _IntrospectsAnnotations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._IntrospectsAnnotations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.interfaces._IntrospectsAnnotations", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_LoadElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategy_options._LoadElement", "kind": "Gdef"}, "_MappedAttribute": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base._MappedAttribute", "kind": "Gdef"}, "_MapperEntity": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context._MapperEntity", "kind": "Gdef"}, "_MapsColumns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.interfaces._DCAttributeOptions", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces._MapsColumns", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base._MappedAttribute"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.interfaces._MapsColumns", "name": "_MapsColumns", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces._MapsColumns", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.interfaces._MapsColumns", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.interfaces", "mro": ["sqlalchemy.orm.interfaces._MapsColumns", "sqlalchemy.orm.interfaces._DCAttributeOptions", "sqlalchemy.orm.base._MappedAttribute", "sqlalchemy.util.langhelpers.TypingOnly", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.interfaces._MapsColumns.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "columns_to_assign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces._MapsColumns.columns_to_assign", "name": "columns_to_assign", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces._MapsColumns", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces._MapsColumns"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "columns_to_assign of _MapsColumns", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces._MapsColumns", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.interfaces._MapsColumns.columns_to_assign", "name": "columns_to_assign", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces._MapsColumns", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces._MapsColumns"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "columns_to_assign of _MapsColumns", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces._MapsColumns", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "mapper_property_to_assign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "sqlalchemy.orm.interfaces._MapsColumns.mapper_property_to_assign", "name": "mapper_property_to_assign", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces._MapsColumns", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces._MapsColumns"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mapper_property_to_assign of _MapsColumns", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces._MapsColumns", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.interfaces._MapsColumns.mapper_property_to_assign", "name": "mapper_property_to_assign", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces._MapsColumns", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces._MapsColumns"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mapper_property_to_assign of _MapsColumns", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces._MapsColumns", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.MapperProperty"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._MapsColumns.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.orm.interfaces._MapsColumns", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces._MapsColumns"}, "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": ["_T"], "typeddict_type": null}}, "_NoArg": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._NoArg", "kind": "Gdef"}, "_ORMAdapterProto": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._ORMAdapterProto", "kind": "Gdef"}, "_PopulatorDict": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.loading._PopulatorDict", "kind": "Gdef"}, "_StrategyKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.orm.interfaces._StrategyKey", "line": 107, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T", "name": "_T", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "_TLS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._TLS", "name": "_TLS", "upper_bound": {".class": "TypeType", "item": "sqlalchemy.orm.interfaces.LoaderStrategy"}, "values": [], "variance": 0}}, "_T_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.interfaces._T_co", "name": "_T_co", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}}, "_TraverseInternalsType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors._TraverseInternalsType", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.interfaces.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.interfaces.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.interfaces.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.interfaces.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.interfaces.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.interfaces.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "inspection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.inspection", "kind": "Gdef"}, "operators": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.operators", "kind": "Gdef"}, "orm_exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.exc", "kind": "Gdef"}, "path_registry": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.path_registry", "kind": "Gdef"}, "roles": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.roles", "kind": "Gdef"}, "sa_exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}, "visitors": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors", "kind": "Gdef"}, "warn_deprecated": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.deprecations.warn_deprecated", "kind": "Gdef"}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/orm/interfaces.py"}