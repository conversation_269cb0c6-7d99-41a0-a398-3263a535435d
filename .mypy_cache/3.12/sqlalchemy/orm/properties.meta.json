{"data_mtime": 1751559210, "dep_lines": [30, 31, 32, 33, 35, 38, 45, 46, 51, 52, 53, 54, 56, 57, 67, 72, 73, 74, 75, 77, 78, 80, 30, 47, 49, 50, 51, 15, 17, 47, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 20, 10, 10, 10, 20, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.exc", "sqlalchemy.orm.strategy_options", "sqlalchemy.orm.base", "sqlalchemy.orm.descriptor_props", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.relationships", "sqlalchemy.orm.util", "sqlalchemy.sql.coercions", "sqlalchemy.sql.roles", "sqlalchemy.sql.base", "sqlalchemy.sql.schema", "sqlalchemy.sql.type_api", "sqlalchemy.util.typing", "sqlalchemy.orm._typing", "sqlalchemy.orm.decl_base", "sqlalchemy.orm.mapper", "sqlalchemy.orm.session", "sqlalchemy.orm.state", "sqlalchemy.sql._typing", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.log", "sqlalchemy.util", "sqlalchemy.sql", "__future__", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.orm.decl_api", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types", "typing_extensions"], "hash": "e5fa34596f66761865610370b070cb463e9bc117", "id": "sqlalchemy.orm.properties", "ignore_all": true, "interface_hash": "19561d8d51f74c2680ed22203100510ea2f353cc", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/orm/properties.py", "plugin_data": null, "size": 29533, "suppressed": [], "version_id": "1.16.1"}