{"data_mtime": 1751559210, "dep_lines": [27, 28, 29, 30, 31, 32, 33, 41, 42, 43, 44, 45, 47, 48, 55, 58, 61, 62, 66, 68, 69, 27, 38, 39, 40, 42, 15, 17, 38, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 10, 10, 10, 5, 10, 10, 5, 10, 10, 10, 5, 5, 25, 25, 25, 25, 25, 25, 20, 10, 5, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.context", "sqlalchemy.orm.evaluator", "sqlalchemy.orm.exc", "sqlalchemy.orm.loading", "sqlalchemy.orm.persistence", "sqlalchemy.orm.base", "sqlalchemy.engine.result", "sqlalchemy.sql.coercions", "sqlalchemy.sql.dml", "sqlalchemy.sql.expression", "sqlalchemy.sql.roles", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.base", "sqlalchemy.util.typing", "sqlalchemy.orm._typing", "sqlalchemy.orm.mapper", "sqlalchemy.orm.session", "sqlalchemy.orm.state", "sqlalchemy.engine.cursor", "sqlalchemy.engine.interfaces", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.sql", "__future__", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "_typeshed", "abc", "sqlalchemy.engine.base", "sqlalchemy.engine.util", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.state_changes", "sqlalchemy.sql._selectable_constructors", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "c820320d9f6eb199b14dcaae7cdc71d1c67780bd", "id": "sqlalchemy.orm.bulk_persistence", "ignore_all": true, "interface_hash": "69dcba57fbb40bf729e23f00f9384043b09671fc", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/orm/bulk_persistence.py", "plugin_data": null, "size": 72663, "suppressed": [], "version_id": "1.16.1"}