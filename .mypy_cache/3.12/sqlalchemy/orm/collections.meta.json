{"data_mtime": 1751559210, "dep_lines": [130, 133, 134, 135, 138, 140, 144, 131, 132, 108, 110, 111, 112, 128, 131, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 25, 25, 25, 10, 10, 5, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.base", "sqlalchemy.sql.base", "sqlalchemy.util.compat", "sqlalchemy.util.typing", "sqlalchemy.orm.attributes", "sqlalchemy.orm.mapped_collection", "sqlalchemy.orm.state", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "operator", "threading", "typing", "weakref", "sqlalchemy", "builtins", "_frozen_importlib", "_thread", "_typeshed", "abc", "enum", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "types"], "hash": "e5b09a93244dcde7c41abca28f36aa6d932f621f", "id": "sqlalchemy.orm.collections", "ignore_all": true, "interface_hash": "f113ad84cd4c82963053cd27b7ffece3a50d4eb1", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/orm/collections.py", "plugin_data": null, "size": 52252, "suppressed": [], "version_id": "1.16.1"}