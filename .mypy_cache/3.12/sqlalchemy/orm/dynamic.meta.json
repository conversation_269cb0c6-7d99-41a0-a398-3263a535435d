{"data_mtime": 1751559211, "dep_lines": [33, 34, 35, 36, 37, 38, 39, 40, 45, 50, 53, 56, 33, 44, 45, 55, 20, 22, 44, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 5, 5, 5, 10, 25, 25, 25, 20, 10, 20, 25, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.exc", "sqlalchemy.orm.relationships", "sqlalchemy.orm.util", "sqlalchemy.orm.base", "sqlalchemy.orm.query", "sqlalchemy.orm.session", "sqlalchemy.orm.writeonly", "sqlalchemy.engine.result", "sqlalchemy.orm.mapper", "sqlalchemy.orm.state", "sqlalchemy.sql.elements", "sqlalchemy.orm", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.event", "__future__", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "enum", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm._typing", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.strategies", "sqlalchemy.sql", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "726327b0776cf0a274573507a36ccd9a0f762b94", "id": "sqlalchemy.orm.dynamic", "ignore_all": true, "interface_hash": "60e11c661336a5c9b5231f6ef72a17a87b21e422", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/orm/dynamic.py", "plugin_data": null, "size": 9816, "suppressed": [], "version_id": "1.16.1"}