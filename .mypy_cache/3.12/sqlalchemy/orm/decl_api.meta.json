{"data_mtime": 1751559211, "dep_lines": [34, 35, 36, 37, 39, 45, 49, 56, 59, 60, 61, 62, 66, 67, 68, 69, 70, 73, 87, 93, 94, 34, 63, 64, 65, 66, 10, 12, 13, 14, 32, 63, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 25, 25, 25, 5, 10, 10, 5, 20, 5, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.clsregistry", "sqlalchemy.orm.instrumentation", "sqlalchemy.orm.interfaces", "sqlalchemy.orm._orm_constructors", "sqlalchemy.orm.base", "sqlalchemy.orm.decl_base", "sqlalchemy.orm.descriptor_props", "sqlalchemy.orm.mapper", "sqlalchemy.orm.properties", "sqlalchemy.orm.relationships", "sqlalchemy.orm.state", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.base", "sqlalchemy.sql.elements", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.util.typing", "sqlalchemy.orm._typing", "sqlalchemy.sql._typing", "sqlalchemy.sql.type_api", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.util", "sqlalchemy.sql", "__future__", "itertools", "re", "typing", "weakref", "sqlalchemy", "builtins", "_collections_abc", "_frozen_importlib", "_thread", "_typeshed", "abc", "enum", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.log", "sqlalchemy.orm.query", "sqlalchemy.orm.util", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util.langhelpers", "types", "typing_extensions"], "hash": "9436f9bd46068c3f3d5b97883657a6e7db594297", "id": "sqlalchemy.orm.decl_api", "ignore_all": true, "interface_hash": "e537cbf8bb9de9a473844fcff525fd0ade9316c5", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/orm/decl_api.py", "plugin_data": null, "size": 64930, "suppressed": [], "version_id": "1.16.1"}