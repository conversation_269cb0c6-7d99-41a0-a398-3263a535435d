{"data_mtime": 1751559211, "dep_lines": [16, 17, 18, 19, 20, 21, 16, 24, 25, 26, 14, 24, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 5, 20, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.exc", "sqlalchemy.orm.sync", "sqlalchemy.orm.unitofwork", "sqlalchemy.orm.util", "sqlalchemy.orm.interfaces", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.sql", "sqlalchemy.util", "__future__", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "enum", "sqlalchemy.orm.base", "typing"], "hash": "318fe027dbc47da0d3600daa33b181918abfd918", "id": "sqlalchemy.orm.dependency", "ignore_all": true, "interface_hash": "8f32fa3a8cd914032fc6459cc48e000394543ad4", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/orm/dependency.py", "plugin_data": null, "size": 47631, "suppressed": [], "version_id": "1.16.1"}