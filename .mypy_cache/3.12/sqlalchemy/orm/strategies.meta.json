{"data_mtime": 1751559210, "dep_lines": [24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 41, 47, 48, 49, 58, 59, 60, 62, 65, 67, 24, 52, 53, 55, 56, 57, 13, 15, 16, 17, 52, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 25, 25, 20, 10, 10, 10, 10, 10, 5, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.exc", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.loading", "sqlalchemy.orm.path_registry", "sqlalchemy.orm.properties", "sqlalchemy.orm.query", "sqlalchemy.orm.relationships", "sqlalchemy.orm.unitofwork", "sqlalchemy.orm.util", "sqlalchemy.orm.base", "sqlalchemy.orm.context", "sqlalchemy.orm.session", "sqlalchemy.orm.state", "sqlalchemy.orm.strategy_options", "sqlalchemy.sql.util", "sqlalchemy.sql.visitors", "sqlalchemy.sql.selectable", "sqlalchemy.util.typing", "sqlalchemy.orm.mapper", "sqlalchemy.sql.elements", "sqlalchemy.orm", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.log", "sqlalchemy.sql", "sqlalchemy.util", "__future__", "collections", "itertools", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "logging", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql._elements_constructors", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.coercions", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types"], "hash": "7d499d5fcf79432c8ae3f5cc9a824a8b9465fd31", "id": "sqlalchemy.orm.strategies", "ignore_all": true, "interface_hash": "8e40bd19b69ddb1338a3e908ff3bd818d090f377", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/orm/strategies.py", "plugin_data": null, "size": 119866, "suppressed": [], "version_id": "1.16.1"}