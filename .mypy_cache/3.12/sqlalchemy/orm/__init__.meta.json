{"data_mtime": 1751559211, "dep_lines": [20, 21, 22, 23, 42, 45, 55, 57, 69, 70, 74, 75, 81, 82, 83, 97, 99, 115, 118, 120, 124, 126, 135, 153, 154, 161, 162, 16, 18, 162, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 20, 5, 30, 30], "dependencies": ["sqlalchemy.orm.exc", "sqlalchemy.orm.mapper", "sqlalchemy.orm.strategy_options", "sqlalchemy.orm._orm_constructors", "sqlalchemy.orm.attributes", "sqlalchemy.orm.base", "sqlalchemy.orm.context", "sqlalchemy.orm.decl_api", "sqlalchemy.orm.decl_base", "sqlalchemy.orm.descriptor_props", "sqlalchemy.orm.dynamic", "sqlalchemy.orm.events", "sqlalchemy.orm.identity", "sqlalchemy.orm.instrumentation", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.loading", "sqlalchemy.orm.mapped_collection", "sqlalchemy.orm.properties", "sqlalchemy.orm.query", "sqlalchemy.orm.relationships", "sqlalchemy.orm.scoping", "sqlalchemy.orm.session", "sqlalchemy.orm.state", "sqlalchemy.orm.unitofwork", "sqlalchemy.orm.util", "sqlalchemy.orm.writeonly", "sqlalchemy.util", "__future__", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "abc"], "hash": "a5b3937b9f65e0eb17bca2cb4def479af664353d", "id": "sqlalchemy.orm", "ignore_all": true, "interface_hash": "83ab8494c23a512901e048e61d7ef2b1e059cdde", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/orm/__init__.py", "plugin_data": null, "size": 8463, "suppressed": [], "version_id": "1.16.1"}