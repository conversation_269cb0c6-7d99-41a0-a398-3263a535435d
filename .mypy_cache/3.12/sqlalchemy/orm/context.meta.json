{"data_mtime": 1751559210, "dep_lines": [25, 26, 27, 28, 31, 32, 44, 45, 46, 47, 48, 49, 53, 59, 60, 62, 73, 76, 77, 78, 81, 83, 91, 25, 39, 40, 42, 43, 80, 9, 11, 12, 39, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 25, 25, 25, 25, 25, 25, 25, 20, 10, 10, 10, 10, 25, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.loading", "sqlalchemy.orm.base", "sqlalchemy.orm.path_registry", "sqlalchemy.orm.util", "sqlalchemy.sql.coercions", "sqlalchemy.sql.expression", "sqlalchemy.sql.roles", "sqlalchemy.sql.util", "sqlalchemy.sql.visitors", "sqlalchemy.sql._typing", "sqlalchemy.sql.base", "sqlalchemy.sql.dml", "sqlalchemy.sql.elements", "sqlalchemy.sql.selectable", "sqlalchemy.orm._typing", "sqlalchemy.orm.mapper", "sqlalchemy.orm.query", "sqlalchemy.orm.session", "sqlalchemy.engine.interfaces", "sqlalchemy.sql.compiler", "sqlalchemy.sql.type_api", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.future", "sqlalchemy.sql", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "itertools", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "sqlalchemy.engine.result", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.sql._selectable_constructors", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.schema", "sqlalchemy.sql.traversals", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "c2f87dd64686284b95e8b4217011a7a4fea623b2", "id": "sqlalchemy.orm.context", "ignore_all": true, "interface_hash": "4f91cf36b17fb19d7cc383db2f10491d482f46b9", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/orm/context.py", "plugin_data": null, "size": 115113, "suppressed": [], "version_id": "1.16.1"}