{".class": "MypyFile", "_fullname": "sqlalchemy.orm", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AliasOption": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.query.AliasOption", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AppenderQuery": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.dynamic.AppenderQuery", "kind": "Gdef"}, "AttributeEventToken": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes.AttributeEventToken", "kind": "Gdef"}, "AttributeEvents": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.events.AttributeEvents", "kind": "Gdef"}, "AttributeState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.state.AttributeState", "kind": "Gdef"}, "Bundle": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util.B<PERSON>le", "kind": "Gdef"}, "CascadeOptions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util.CascadeOptions", "kind": "Gdef"}, "ClassManager": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.instrumentation.ClassManager", "kind": "Gdef"}, "ColumnProperty": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.properties.ColumnProperty", "kind": "Gdef"}, "Composite": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.descriptor_props.Composite", "kind": "Gdef"}, "CompositeProperty": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.descriptor_props.CompositeProperty", "kind": "Gdef"}, "DeclarativeBase": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_api.DeclarativeBase", "kind": "Gdef"}, "DeclarativeBaseNoMeta": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_api.DeclarativeBaseNoMeta", "kind": "Gdef"}, "DeclarativeMeta": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_api.DeclarativeMeta", "kind": "Gdef"}, "DynamicMapped": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.DynamicMapped", "kind": "Gdef"}, "EXT_CONTINUE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.EXT_CONTINUE", "kind": "Gdef"}, "EXT_SKIP": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.EXT_SKIP", "kind": "Gdef"}, "EXT_STOP": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.EXT_STOP", "kind": "Gdef"}, "FromStatement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context.FromStatement", "kind": "Gdef"}, "IdentityMap": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.identity.IdentityMap", "kind": "Gdef"}, "InspectionAttr": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.InspectionAttr", "kind": "Gdef"}, "InspectionAttrExtensionType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.InspectionAttrExtensionType", "kind": "Gdef"}, "InspectionAttrInfo": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.InspectionAttrInfo", "kind": "Gdef"}, "InstanceEvents": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.events.InstanceEvents", "kind": "Gdef"}, "InstanceState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.state.InstanceState", "kind": "Gdef"}, "InstrumentationEvents": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.events.InstrumentationEvents", "kind": "Gdef"}, "InstrumentedAttribute": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes.InstrumentedAttribute", "kind": "Gdef"}, "KeyFuncDict": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapped_collection.KeyFuncDict", "kind": "Gdef"}, "Load": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategy_options.Load", "kind": "Gdef"}, "LoaderCallableStatus": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.LoaderCallableStatus", "kind": "Gdef"}, "LoaderCriteriaOption": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util.LoaderCriteriaOption", "kind": "Gdef"}, "MANYTOMANY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.MANYTOMANY", "kind": "Gdef"}, "MANYTOONE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.MANYTOONE", "kind": "Gdef"}, "Mapped": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.Mapped", "kind": "Gdef"}, "MappedAsDataclass": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_api.MappedAsDataclass", "kind": "Gdef"}, "MappedClassProtocol": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_base.MappedClassProtocol", "kind": "Gdef"}, "MappedCollection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapped_collection.MappedCollection", "kind": "Gdef"}, "MappedColumn": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.properties.MappedColumn", "kind": "Gdef"}, "MappedSQLExpression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.properties.MappedSQLExpression", "kind": "Gdef"}, "Mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapper.Mapper", "kind": "Gdef"}, "MapperEvents": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.events.MapperEvents", "kind": "Gdef"}, "MapperProperty": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.MapperProperty", "kind": "Gdef"}, "NO_KEY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.NO_KEY", "kind": "Gdef"}, "NO_VALUE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.NO_VALUE", "kind": "Gdef"}, "NotExtension": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.NotExtension", "kind": "Gdef"}, "ONETOMANY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.ONETOMANY", "kind": "Gdef"}, "ORMDescriptor": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.ORMDescriptor", "kind": "Gdef"}, "ORMExecuteState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.ORMExecuteState", "kind": "Gdef"}, "PassiveFlag": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.PassiveFlag", "kind": "Gdef"}, "PropComparator": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.PropComparator", "kind": "Gdef"}, "Query": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.query.Query", "kind": "Gdef"}, "QueryContext": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context.QueryContext", "kind": "Gdef"}, "QueryEvents": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.events.QueryEvents", "kind": "Gdef"}, "QueryPropertyDescriptor": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.scoping.QueryPropertyDescriptor", "kind": "Gdef"}, "QueryableAttribute": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes.QueryableAttribute", "kind": "Gdef"}, "Relationship": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.relationships.Relationship", "kind": "Gdef"}, "RelationshipDirection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.RelationshipDirection", "kind": "Gdef"}, "RelationshipProperty": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.relationships.RelationshipProperty", "kind": "Gdef"}, "SQLORMExpression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.SQLORMExpression", "kind": "Gdef"}, "Session": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.Session", "kind": "Gdef"}, "SessionEvents": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.events.SessionEvents", "kind": "Gdef"}, "SessionTransaction": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.SessionTransaction", "kind": "Gdef"}, "SessionTransactionOrigin": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.SessionTransactionOrigin", "kind": "Gdef"}, "Synonym": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.descriptor_props.Synonym", "kind": "Gdef"}, "SynonymProperty": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.descriptor_props.SynonymProperty", "kind": "Gdef"}, "UOWTransaction": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.unitofwork.UOWTransaction", "kind": "Gdef"}, "UserDefinedOption": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.UserDefinedOption", "kind": "Gdef"}, "WriteOnlyCollection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.writeonly.WriteOnlyCollection", "kind": "Gdef"}, "WriteOnlyMapped": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.WriteOnlyMapped", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__go": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["lcls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.__go", "name": "__go", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["lcls"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__go", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_sa_util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}, "add_mapped_attribute": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_api.add_mapped_attribute", "kind": "Gdef"}, "aliased": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._orm_constructors.aliased", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "as_declarative": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_api.as_declarative", "kind": "Gdef"}, "attribute_keyed_dict": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapped_collection.attribute_keyed_dict", "kind": "Gdef"}, "attribute_mapped_collection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapped_collection.attribute_mapped_collection", "kind": "Gdef"}, "backref": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._orm_constructors.backref", "kind": "Gdef"}, "class_mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.class_mapper", "kind": "Gdef"}, "clear_mappers": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._orm_constructors.clear_mappers", "kind": "Gdef"}, "close_all_sessions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.close_all_sessions", "kind": "Gdef"}, "column_keyed_dict": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapped_collection.column_keyed_dict", "kind": "Gdef"}, "column_mapped_collection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapped_collection.column_mapped_collection", "kind": "Gdef"}, "column_property": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._orm_constructors.column_property", "kind": "Gdef"}, "composite": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._orm_constructors.composite", "kind": "Gdef"}, "configure_mappers": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapper.configure_mappers", "kind": "Gdef"}, "contains_alias": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._orm_constructors.contains_alias", "kind": "Gdef"}, "contains_eager": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategy_options.contains_eager", "kind": "Gdef"}, "create_session": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._orm_constructors.create_session", "kind": "Gdef"}, "declarative_base": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_api.declarative_base", "kind": "Gdef"}, "declarative_mixin": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_api.declarative_mixin", "kind": "Gdef"}, "declared_attr": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_api.declared_attr", "kind": "Gdef"}, "defaultload": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategy_options.defaultload", "kind": "Gdef"}, "defer": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategy_options.defer", "kind": "Gdef"}, "deferred": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._orm_constructors.deferred", "kind": "Gdef"}, "dynamic_loader": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._orm_constructors.dynamic_loader", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.exc", "kind": "Gdef"}, "foreign": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.relationships.foreign", "kind": "Gdef"}, "has_inherited_table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_api.has_inherited_table", "kind": "Gdef"}, "immediateload": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategy_options.immediateload", "kind": "Gdef"}, "join": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._orm_constructors.join", "kind": "Gdef"}, "joinedload": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategy_options.joinedload", "kind": "Gdef"}, "keyfunc_mapping": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapped_collection.keyfunc_mapping", "kind": "Gdef"}, "lazyload": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategy_options.lazyload", "kind": "Gdef"}, "load_only": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategy_options.load_only", "kind": "Gdef"}, "make_transient": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.make_transient", "kind": "Gdef"}, "make_transient_to_detached": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.make_transient_to_detached", "kind": "Gdef"}, "mapped_collection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapped_collection.mapped_collection", "kind": "Gdef"}, "mapped_column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._orm_constructors.mapped_column", "kind": "Gdef"}, "mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._orm_constructors._mapper_fn", "kind": "Gdef"}, "mapperlib": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapper", "kind": "Gdef"}, "merge_frozen_result": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.loading.merge_frozen_result", "kind": "Gdef"}, "merge_result": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.loading.merge_result", "kind": "Gdef"}, "noload": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategy_options.noload", "kind": "Gdef"}, "object_mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.object_mapper", "kind": "Gdef"}, "object_session": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.object_session", "kind": "Gdef"}, "orm_insert_sentinel": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._orm_constructors.orm_insert_sentinel", "kind": "Gdef"}, "outerjoin": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._orm_constructors.outerjoin", "kind": "Gdef"}, "polymorphic_union": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util.polymorphic_union", "kind": "Gdef"}, "query_expression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._orm_constructors.query_expression", "kind": "Gdef"}, "raiseload": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategy_options.raiseload", "kind": "Gdef"}, "reconstructor": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapper.reconstructor", "kind": "Gdef"}, "registry": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_api.registry", "kind": "Gdef"}, "relationship": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._orm_constructors.relationship", "kind": "Gdef"}, "remote": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.relationships.remote", "kind": "Gdef"}, "scoped_session": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.scoping.scoped_session", "kind": "Gdef"}, "selectin_polymorphic": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategy_options.selectin_polymorphic", "kind": "Gdef"}, "selectinload": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategy_options.selectinload", "kind": "Gdef"}, "sessionmaker": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.sessionmaker", "kind": "Gdef"}, "strategy_options": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategy_options", "kind": "Gdef"}, "subqueryload": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategy_options.subqueryload", "kind": "Gdef"}, "synonym": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._orm_constructors.synonym", "kind": "Gdef"}, "synonym_for": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_api.synonym_for", "kind": "Gdef"}, "undefer": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategy_options.undefer", "kind": "Gdef"}, "undefer_group": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategy_options.undefer_group", "kind": "Gdef"}, "validates": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapper.validates", "kind": "Gdef"}, "was_deleted": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util.was_deleted", "kind": "Gdef"}, "with_expression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategy_options.with_expression", "kind": "Gdef"}, "with_loader_criteria": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._orm_constructors.with_loader_criteria", "kind": "Gdef"}, "with_parent": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util.with_parent", "kind": "Gdef"}, "with_polymorphic": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._orm_constructors.with_polymorphic", "kind": "Gdef"}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/orm/__init__.py"}