{"data_mtime": 1751559210, "dep_lines": [53, 54, 55, 56, 57, 58, 59, 63, 72, 73, 74, 53, 60, 61, 34, 36, 51, 60, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 5, 5, 5, 25, 25, 25, 20, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.base", "sqlalchemy.orm.collections", "sqlalchemy.orm.exc", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.state", "sqlalchemy.orm._typing", "sqlalchemy.orm.attributes", "sqlalchemy.util.typing", "sqlalchemy.orm.decl_base", "sqlalchemy.orm.events", "sqlalchemy.orm.mapper", "sqlalchemy.orm", "sqlalchemy.util", "sqlalchemy.event", "__future__", "typing", "weakref", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "enum", "sqlalchemy.event.attr", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm.decl_api", "sqlalchemy.orm.util", "sqlalchemy.sql", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "types"], "hash": "fef630b7d3aa1718bc8f11212d5b731c207ced2f", "id": "sqlalchemy.orm.instrumentation", "ignore_all": true, "interface_hash": "244ad1db6c089143354bd07d21a158ec3b06e4b8", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/orm/instrumentation.py", "plugin_data": null, "size": 24321, "suppressed": [], "version_id": "1.16.1"}