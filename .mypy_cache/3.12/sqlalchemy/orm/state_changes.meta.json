{"data_mtime": 1751559210, "dep_lines": [28, 26, 27, 12, 14, 15, 16, 26, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 10, 5, 5, 20, 5, 30, 30], "dependencies": ["sqlalchemy.util.typing", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "contextlib", "enum", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "abc"], "hash": "238730d9065a50cf1130f7a971a01aa4c42e1f3c", "id": "sqlalchemy.orm.state_changes", "ignore_all": true, "interface_hash": "fca112ffb6eea2cf410ed2e7e4d452cdb7c9f6af", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/orm/state_changes.py", "plugin_data": null, "size": 6815, "suppressed": [], "version_id": "1.16.1"}