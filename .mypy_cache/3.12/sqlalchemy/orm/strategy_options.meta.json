{"data_mtime": 1751559210, "dep_lines": [29, 30, 34, 35, 36, 37, 50, 51, 52, 53, 54, 55, 56, 68, 74, 76, 29, 46, 48, 49, 13, 15, 46, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 5, 25, 25, 25, 20, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.util", "sqlalchemy.orm._typing", "sqlalchemy.orm.attributes", "sqlalchemy.orm.base", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.path_registry", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.coercions", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.sql.base", "sqlalchemy.util.typing", "sqlalchemy.orm.context", "sqlalchemy.orm.mapper", "sqlalchemy.sql._typing", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.sql", "__future__", "typing", "sqlalchemy", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.sql.annotation", "sqlalchemy.sql.dml", "sqlalchemy.sql.elements", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.selectable", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "2509160fd807a0a9d2c9c11be113259f3588ae89", "id": "sqlalchemy.orm.strategy_options", "ignore_all": true, "interface_hash": "0074409da3bab3de3bc8453a6a8badec2563deb1", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/orm/strategy_options.py", "plugin_data": null, "size": 85043, "suppressed": [], "version_id": "1.16.1"}