{"data_mtime": 1751559209, "dep_lines": [39, 40, 35, 36, 17, 19, 20, 21, 33, 35, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 10, 10, 5, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30], "dependencies": ["sqlalchemy.event.attr", "sqlalchemy.event.base", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "collections", "types", "typing", "weakref", "sqlalchemy", "builtins", "_frozen_importlib", "_typeshed", "abc", "sqlalchemy.util.langhelpers"], "hash": "47e3d3b71001d625eb8d0eb51942a33bd08e80fd", "id": "sqlalchemy.event.registry", "ignore_all": true, "interface_hash": "372d3caf3198b90b97db9a5ef35a5b0f6c6aad27", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/event/registry.py", "plugin_data": null, "size": 11144, "suppressed": [], "version_id": "1.16.1"}