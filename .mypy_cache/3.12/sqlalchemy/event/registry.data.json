{".class": "MypyFile", "_fullname": "sqlalchemy.event.registry", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Deque": {".class": "SymbolTableNode", "cross_ref": "typing.Deque", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EventTarget": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.event.registry.EventTarget", "name": "EventTarget", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.event.registry.EventTarget", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.event.registry", "mro": ["sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.event.registry.EventTarget.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "dispatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.registry.EventTarget.dispatch", "name": "dispatch", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.event.base.dispatcher"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry.EventTarget.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "RefCollection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.attr.RefCollection", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_ET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "name": "_ET", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}}, "_EventKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.event.registry._EventKey", "name": "_EventKey", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.registry._EventKey", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.event.registry._EventKey", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.event.registry", "mro": ["sqlalchemy.event.registry._EventKey", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "target", "identifier", "fn", "dispatch_target", "_fn_wrap"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.event.registry._EventKey.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "target", "identifier", "fn", "dispatch_target", "_fn_wrap"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.registry._EventKey", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.registry._EventKey", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of _Event<PERSON>ey", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.event.registry._EventKey.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_fn_wrap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.registry._EventKey._fn_wrap", "name": "_fn_wrap", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "sqlalchemy.event.registry._EventKey._key", "name": "_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.registry._EventKey", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_key of _EventKey", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._EventKeyTupleType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.event.registry._EventKey._key", "name": "_key", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.registry._EventKey", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_key of _EventKey", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._EventKeyTupleType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_listen_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "sqlalchemy.event.registry._EventKey._listen_fn", "name": "_listen_fn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.registry._EventKey", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_listen_fn of _EventKey", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.event.registry._EventKey._listen_fn", "name": "_listen_fn", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.registry._EventKey", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_listen_fn of _EventKey", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "append_to_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "owner", "list_"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.event.registry._EventKey.append_to_list", "name": "append_to_list", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "owner", "list_"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.registry._EventKey", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.registry._EventKey", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.attr.RefCollection"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "extra_attrs": null, "type_ref": "collections.deque"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "append_to_list of _EventKey", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "base_listen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "propagate", "insert", "named", "retval", "asyncio"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.event.registry._EventKey.base_listen", "name": "base_listen", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "propagate", "insert", "named", "retval", "asyncio"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.registry._EventKey", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "base_listen of _Event<PERSON>ey", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "contains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.event.registry._EventKey.contains", "name": "contains", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.registry._EventKey", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "contains of _EventKey", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dispatch_target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.registry._EventKey.dispatch_target", "name": "dispatch_target", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.registry._EventKey.fn", "name": "fn", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}}}, "fn_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.registry._EventKey.fn_key", "name": "fn_key", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnKeyType"}}}, "fn_wrap": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.event.registry._EventKey.fn_wrap", "name": "fn_wrap", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "identifier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.registry._EventKey.identifier", "name": "identifier", "setter_type": null, "type": "builtins.str"}}, "listen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.event.registry._EventKey.listen", "name": "listen", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.registry._EventKey", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "listen of _Event<PERSON>ey", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prepend_to_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "owner", "list_"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.event.registry._EventKey.prepend_to_list", "name": "prepend_to_list", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "owner", "list_"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.registry._EventKey", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.registry._EventKey", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.attr.RefCollection"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "extra_attrs": null, "type_ref": "collections.deque"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "prepend_to_list of _EventKey", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.event.registry._EventKey.remove", "name": "remove", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.registry._EventKey", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "remove of _EventKey", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_from_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "owner", "list_"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.event.registry._EventKey.remove_from_list", "name": "remove_from_list", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "owner", "list_"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.registry._EventKey", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.registry._EventKey", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.attr.RefCollection"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "extra_attrs": null, "type_ref": "collections.deque"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "remove_from_list of _EventKey", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.event.registry._EventKey.target", "name": "target", "setter_type": null, "type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.registry._EventKey", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}}}, "with_dispatch_target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dispatch_target"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.event.registry._EventKey.with_dispatch_target", "name": "with_dispatch_target", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dispatch_target"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.registry._EventKey", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_dispatch_target of _EventKey", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.registry._EventKey", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "with_wrapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fn_wrap"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlalchemy.event.registry._EventKey.with_wrapper", "name": "with_wrapper", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fn_wrap"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.registry._EventKey", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_wrapper of _EventKey", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.registry._EventKey", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._EventKey.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": 1, "name": "_ET", "namespace": "sqlalchemy.event.registry._EventKey", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}, "values": [], "variance": 0}, "slots": ["dispatch_target", "fn", "fn_key", "fn_wrap", "identifier", "target"], "tuple_type": null, "type_vars": ["_ET"], "typeddict_type": null}}, "_EventKeyTupleType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.event.registry._EventKeyTupleType", "line": 44, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnKeyType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_ListenerFnKeyType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.event.registry._ListenerFnKeyType", "line": 43, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}}}, "_ListenerFnType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.event.registry._ListenerFnType", "line": 42, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ListenerToEventKeyType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.event.registry._ListenerToEventKeyType", "line": 83, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "extra_attrs": null, "type_ref": "weakref.ReferenceType"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._EventKeyTupleType"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_RefCollectionToListenerType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.event.registry._RefCollectionToListenerType", "line": 64, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "sqlalchemy.event.attr.RefCollection"}], "extra_attrs": null, "type_ref": "weakref.ReferenceType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "extra_attrs": null, "type_ref": "weakref.ReferenceType"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.registry.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.registry.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.registry.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.registry.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.registry.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.event.registry.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_clear": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["owner", "elements"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.event.registry._clear", "name": "_clear", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["owner", "elements"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.event.registry._clear", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.attr.RefCollection"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_clear", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.event.registry._clear", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}}}, "_collection_gced": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["ref"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.event.registry._collection_gced", "name": "_collection_gced", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["ref"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "weakref.ReferenceType"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_collection_gced", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_collection_to_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "sqlalchemy.event.registry._collection_to_key", "name": "_collection_to_key", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.event.attr.RefCollection"}], "extra_attrs": null, "type_ref": "weakref.ReferenceType"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerToEventKeyType"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_key_to_collection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "sqlalchemy.event.registry._key_to_collection", "name": "_key_to_collection", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._EventKeyTupleType"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._RefCollectionToListenerType"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_removed_from_collection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["event_key", "owner"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.event.registry._removed_from_collection", "name": "_removed_from_collection", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["event_key", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.event.registry._removed_from_collection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.event.registry._removed_from_collection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.attr.RefCollection"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_removed_from_collection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.event.registry._removed_from_collection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}}}, "_stored_in_collection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["event_key", "owner"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.event.registry._stored_in_collection", "name": "_stored_in_collection", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["event_key", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.event.registry._stored_in_collection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.registry._EventKey"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.event.registry._stored_in_collection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.attr.RefCollection"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_stored_in_collection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.event.registry._stored_in_collection", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}}}, "_stored_in_collection_multi": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["newowner", "oldowner", "elements"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.event.registry._stored_in_collection_multi", "name": "_stored_in_collection_multi", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["newowner", "oldowner", "elements"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.event.registry._stored_in_collection_multi", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.attr.RefCollection"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.event.registry._stored_in_collection_multi", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.event.attr.RefCollection"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_stored_in_collection_multi", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.event.registry._ET", "id": -1, "name": "_ET", "namespace": "sqlalchemy.event.registry._stored_in_collection_multi", "upper_bound": "sqlalchemy.event.registry.EventTarget", "values": [], "variance": 0}]}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "dispatcher": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.base.dispatcher", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}, "weakref": {".class": "SymbolTableNode", "cross_ref": "weakref", "kind": "Gdef"}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/event/registry.py"}