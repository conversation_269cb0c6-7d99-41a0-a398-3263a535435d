{"data_mtime": 1751559209, "dep_lines": [57, 58, 64, 65, 70, 57, 62, 63, 31, 33, 34, 35, 36, 37, 55, 62, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 25, 20, 10, 10, 5, 10, 5, 10, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.event.legacy", "sqlalchemy.event.registry", "sqlalchemy.util.concurrency", "sqlalchemy.util.typing", "sqlalchemy.event.base", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "collections", "itertools", "threading", "types", "typing", "weakref", "sqlalchemy", "builtins", "_frozen_importlib", "_typeshed", "abc", "sqlalchemy.util.compat", "sqlalchemy.util.langhelpers"], "hash": "57e375ef5925dadb0106635b6727a87485990dbb", "id": "sqlalchemy.event.attr", "ignore_all": true, "interface_hash": "581265bd0726d268bf3b981951f74844a3230fd2", "mtime": 1747044678, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlalchemy/event/attr.py", "plugin_data": null, "size": 20751, "suppressed": [], "version_id": "1.16.1"}