{"data_mtime": 1751559217, "dep_lines": [58, 38, 39, 55, 56, 57, 27, 29, 30, 31, 34, 36, 150, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 189], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 25, 25, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["httpx._transports.base", "httpx._config", "httpx._exceptions", "httpx._models", "httpx._types", "httpx._urls", "__future__", "contextlib", "typing", "types", "ssl", "httpx", "httpcore", "builtins", "_collections_abc", "_frozen_importlib", "_ssl", "abc", "httpcore._async", "httpcore._async.connection_pool", "httpcore._async.http_proxy", "httpcore._async.interfaces", "httpcore._async.socks_proxy", "httpcore._backends", "httpcore._backends.base", "httpcore._models", "httpcore._sync", "httpcore._sync.connection_pool", "httpcore._sync.http_proxy", "httpcore._sync.interfaces", "httpcore._sync.socks_proxy"], "hash": "79fd270cb3f27adcc29817efed9b57bd125b3b1d", "id": "httpx._transports.default", "ignore_all": true, "interface_hash": "3bb446a8f2b063feb973b3af7d82c86937018c8f", "mtime": 1747044682, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/httpx/_transports/default.py", "plugin_data": null, "size": 13983, "suppressed": ["<PERSON><PERSON>"], "version_id": "1.16.1"}