{"data_mtime": 1751559218, "dep_lines": [11, 12, 13, 14, 15, 17, 18, 19, 20, 1, 3, 4, 5, 6, 8, 11, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 9, 10, 9], "dep_prios": [10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 20, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10, 20], "dependencies": ["rich.console", "rich.markup", "rich.progress", "rich.syntax", "rich.table", "httpx._client", "httpx._exceptions", "httpx._models", "httpx._status_codes", "__future__", "functools", "json", "sys", "typing", "click", "rich", "httpcore", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "click.core", "click.decorators", "click.types", "httpcore._models"], "hash": "f1e376e6e69a9013fe57cced3181ff8b9bc1a95a", "id": "httpx._main", "ignore_all": true, "interface_hash": "05cc99b927a5172b803da828ed69ac103ac4f0da", "mtime": 1747044682, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/httpx/_main.py", "plugin_data": null, "size": 15626, "suppressed": ["pygments.lexers", "pygments.util", "pygments"], "version_id": "1.16.1"}