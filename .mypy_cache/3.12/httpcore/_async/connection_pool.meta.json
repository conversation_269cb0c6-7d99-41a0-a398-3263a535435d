{"data_mtime": 1751559216, "dep_lines": [8, 9, 13, 14, 131, 144, 10, 11, 12, 1, 3, 4, 5, 6, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 20, 20, 5, 5, 5, 5, 10, 10, 10, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["httpcore._backends.auto", "httpcore._backends.base", "httpcore._async.connection", "httpcore._async.interfaces", "httpcore._async.socks_proxy", "httpcore._async.http_proxy", "httpcore._exceptions", "httpcore._models", "httpcore._synchronization", "__future__", "ssl", "sys", "types", "typing", "builtins", "_frozen_importlib", "_ssl", "_typeshed", "abc", "httpcore._backends"], "hash": "5e37f86d5b27f549897fef88a82a146ad8ddff0c", "id": "httpcore._async.connection_pool", "ignore_all": true, "interface_hash": "03a18db0017a28acbe92f283e28affb0c2292c61", "mtime": 1747044681, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/httpcore/_async/connection_pool.py", "plugin_data": null, "size": 17307, "suppressed": [], "version_id": "1.16.1"}