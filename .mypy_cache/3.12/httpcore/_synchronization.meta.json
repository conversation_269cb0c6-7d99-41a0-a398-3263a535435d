{"data_mtime": 1751559180, "dep_lines": [6, 1, 3, 4, 17, 1, 1, 1, 1, 1, 1, 1, 1, 12], "dep_prios": [5, 5, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["httpcore._exceptions", "__future__", "threading", "types", "anyio", "builtins", "_frozen_importlib", "_thread", "abc", "anyio._core", "anyio._core._synchronization", "anyio._core._tasks", "typing"], "hash": "e1dfb6fdf9e473eeab369edf6b383e3676d6c1e8", "id": "httpcore._synchronization", "ignore_all": true, "interface_hash": "b7e984e3701ea2b68f12f3466ddfe9d9dc6ea9cb", "mtime": 1747044681, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/httpcore/_synchronization.py", "plugin_data": null, "size": 9434, "suppressed": ["trio"], "version_id": "1.16.1"}