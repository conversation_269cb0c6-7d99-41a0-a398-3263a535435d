{".class": "MypyFile", "_fullname": "httpcore", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AnyIOBackend": {".class": "SymbolTableNode", "cross_ref": "httpcore._backends.anyio.AnyIOBackend", "kind": "Gdef"}, "AsyncConnectionInterface": {".class": "SymbolTableNode", "cross_ref": "httpcore._async.interfaces.AsyncConnectionInterface", "kind": "Gdef"}, "AsyncConnectionPool": {".class": "SymbolTableNode", "cross_ref": "httpcore._async.connection_pool.AsyncConnectionPool", "kind": "Gdef"}, "AsyncHTTP11Connection": {".class": "SymbolTableNode", "cross_ref": "httpcore._async.http11.AsyncHTTP11Connection", "kind": "Gdef"}, "AsyncHTTP2Connection": {".class": "SymbolTableNode", "cross_ref": "httpcore._async.http2.AsyncHTTP2Connection", "kind": "Gdef"}, "AsyncHTTPConnection": {".class": "SymbolTableNode", "cross_ref": "httpcore._async.connection.AsyncHTTPConnection", "kind": "Gdef"}, "AsyncHTTPProxy": {".class": "SymbolTableNode", "cross_ref": "httpcore._async.http_proxy.AsyncHTTPProxy", "kind": "Gdef"}, "AsyncMockBackend": {".class": "SymbolTableNode", "cross_ref": "httpcore._backends.mock.AsyncMockBackend", "kind": "Gdef"}, "AsyncMockStream": {".class": "SymbolTableNode", "cross_ref": "httpcore._backends.mock.AsyncMockStream", "kind": "Gdef"}, "AsyncNetworkBackend": {".class": "SymbolTableNode", "cross_ref": "httpcore._backends.base.AsyncNetworkBackend", "kind": "Gdef"}, "AsyncNetworkStream": {".class": "SymbolTableNode", "cross_ref": "httpcore._backends.base.AsyncNetworkStream", "kind": "Gdef"}, "AsyncSOCKSProxy": {".class": "SymbolTableNode", "cross_ref": "httpcore._async.socks_proxy.AsyncSOCKSProxy", "kind": "Gdef"}, "ConnectError": {".class": "SymbolTableNode", "cross_ref": "httpcore._exceptions.ConnectError", "kind": "Gdef"}, "ConnectTimeout": {".class": "SymbolTableNode", "cross_ref": "httpcore._exceptions.ConnectTimeout", "kind": "Gdef"}, "ConnectionInterface": {".class": "SymbolTableNode", "cross_ref": "httpcore._sync.interfaces.ConnectionInterface", "kind": "Gdef"}, "ConnectionNotAvailable": {".class": "SymbolTableNode", "cross_ref": "httpcore._exceptions.ConnectionNotAvailable", "kind": "Gdef"}, "ConnectionPool": {".class": "SymbolTableNode", "cross_ref": "httpcore._sync.connection_pool.ConnectionPool", "kind": "Gdef"}, "HTTP11Connection": {".class": "SymbolTableNode", "cross_ref": "httpcore._sync.http11.HTTP11Connection", "kind": "Gdef"}, "HTTP2Connection": {".class": "SymbolTableNode", "cross_ref": "httpcore._sync.http2.HTTP2Connection", "kind": "Gdef"}, "HTTPConnection": {".class": "SymbolTableNode", "cross_ref": "httpcore._sync.connection.HTTPConnection", "kind": "Gdef"}, "HTTPProxy": {".class": "SymbolTableNode", "cross_ref": "httpcore._sync.http_proxy.HTTPProxy", "kind": "Gdef"}, "LocalProtocolError": {".class": "SymbolTableNode", "cross_ref": "httpcore._exceptions.LocalProtocolError", "kind": "Gdef"}, "MockBackend": {".class": "SymbolTableNode", "cross_ref": "httpcore._backends.mock.MockBackend", "kind": "Gdef"}, "MockStream": {".class": "SymbolTableNode", "cross_ref": "httpcore._backends.mock.MockStream", "kind": "Gdef"}, "NetworkBackend": {".class": "SymbolTableNode", "cross_ref": "httpcore._backends.base.NetworkBackend", "kind": "Gdef"}, "NetworkError": {".class": "SymbolTableNode", "cross_ref": "httpcore._exceptions.NetworkError", "kind": "Gdef"}, "NetworkStream": {".class": "SymbolTableNode", "cross_ref": "httpcore._backends.base.NetworkStream", "kind": "Gdef"}, "Origin": {".class": "SymbolTableNode", "cross_ref": "httpcore._models.Origin", "kind": "Gdef"}, "PoolTimeout": {".class": "SymbolTableNode", "cross_ref": "httpcore._exceptions.PoolTimeout", "kind": "Gdef"}, "ProtocolError": {".class": "SymbolTableNode", "cross_ref": "httpcore._exceptions.ProtocolError", "kind": "Gdef"}, "Proxy": {".class": "SymbolTableNode", "cross_ref": "httpcore._models.Proxy", "kind": "Gdef"}, "ProxyError": {".class": "SymbolTableNode", "cross_ref": "httpcore._exceptions.ProxyError", "kind": "Gdef"}, "ReadError": {".class": "SymbolTableNode", "cross_ref": "httpcore._exceptions.ReadError", "kind": "Gdef"}, "ReadTimeout": {".class": "SymbolTableNode", "cross_ref": "httpcore._exceptions.ReadTimeout", "kind": "Gdef"}, "RemoteProtocolError": {".class": "SymbolTableNode", "cross_ref": "httpcore._exceptions.RemoteProtocolError", "kind": "Gdef"}, "Request": {".class": "SymbolTableNode", "cross_ref": "httpcore._models.Request", "kind": "Gdef"}, "Response": {".class": "SymbolTableNode", "cross_ref": "httpcore._models.Response", "kind": "Gdef"}, "SOCKET_OPTION": {".class": "SymbolTableNode", "cross_ref": "httpcore._backends.base.SOCKET_OPTION", "kind": "Gdef"}, "SOCKSProxy": {".class": "SymbolTableNode", "cross_ref": "httpcore._sync.socks_proxy.SOCKSProxy", "kind": "Gdef"}, "SyncBackend": {".class": "SymbolTableNode", "cross_ref": "httpcore._backends.sync.SyncBackend", "kind": "Gdef"}, "TimeoutException": {".class": "SymbolTableNode", "cross_ref": "httpcore._exceptions.TimeoutException", "kind": "Gdef"}, "TrioBackend": {".class": "SymbolTableNode", "cross_ref": "httpcore._backends.trio.TrioBackend", "kind": "Gdef"}, "URL": {".class": "SymbolTableNode", "cross_ref": "httpcore._models.URL", "kind": "Gdef"}, "UnsupportedProtocol": {".class": "SymbolTableNode", "cross_ref": "httpcore._exceptions.UnsupportedProtocol", "kind": "Gdef"}, "WriteError": {".class": "SymbolTableNode", "cross_ref": "httpcore._exceptions.WriteError", "kind": "Gdef"}, "WriteTimeout": {".class": "SymbolTableNode", "cross_ref": "httpcore._exceptions.WriteTimeout", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "httpcore.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__locals": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "httpcore.__locals", "name": "__locals", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__name": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_index_var", "is_inferred"], "fullname": "httpcore.__name", "name": "__name", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "httpcore.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "httpcore.__version__", "name": "__version__", "setter_type": null, "type": "builtins.str"}}, "default_ssl_context": {".class": "SymbolTableNode", "cross_ref": "httpcore._ssl.default_ssl_context", "kind": "Gdef"}, "request": {".class": "SymbolTableNode", "cross_ref": "httpcore._api.request", "kind": "Gdef"}, "stream": {".class": "SymbolTableNode", "cross_ref": "httpcore._api.stream", "kind": "Gdef"}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/httpcore/__init__.py"}