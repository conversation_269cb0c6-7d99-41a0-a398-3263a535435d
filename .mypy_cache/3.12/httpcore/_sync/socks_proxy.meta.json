{"data_mtime": 1751559216, "dep_lines": [8, 9, 15, 16, 17, 280, 10, 11, 12, 13, 14, 1, 3, 4, 1, 1, 1, 1, 1, 1, 6], "dep_prios": [5, 5, 5, 5, 5, 20, 5, 5, 5, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 10], "dependencies": ["httpcore._backends.sync", "httpcore._backends.base", "httpcore._sync.connection_pool", "httpcore._sync.http11", "httpcore._sync.interfaces", "httpcore._sync.http2", "httpcore._exceptions", "httpcore._models", "httpcore._ssl", "httpcore._synchronization", "httpcore._trace", "__future__", "logging", "ssl", "builtins", "_frozen_importlib", "_ssl", "abc", "httpcore._backends", "typing"], "hash": "1c317f91e1bf0ee5e6456142583b6d427bb2536b", "id": "httpcore._sync.socks_proxy", "ignore_all": true, "interface_hash": "6259924a90445b7256996d0ddf3f644cad4febb0", "mtime": 1747044681, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/httpcore/_sync/socks_proxy.py", "plugin_data": null, "size": 13614, "suppressed": ["<PERSON><PERSON>"], "version_id": "1.16.1"}