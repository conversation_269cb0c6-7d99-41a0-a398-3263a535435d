{".class": "MypyFile", "_fullname": "app.models.base", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncSession": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.session.AsyncSession", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlmodel.main.SQLModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.models.base.BaseModel", "name": "BaseModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "app.models.base.BaseModel", "has_param_spec_type": false, "metaclass_type": "sqlmodel.main.SQLModelMetaclass", "metadata": {}, "module_name": "app.models.base", "mro": ["app.models.base.BaseModel", "sqlmodel.main.SQLModel", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__tablename__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "app.models.base.BaseModel.__tablename__", "name": "__tablename__", "setter_type": null, "type": "builtins.str"}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "db", "obj_in"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_coroutine", "is_decorated"], "fullname": "app.models.base.BaseModel.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "db", "obj_in"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.base.T", "id": -1, "name": "T", "namespace": "app.models.base.BaseModel.create", "upper_bound": "app.models.base.BaseModel", "values": [], "variance": 0}}, "sqlalchemy.ext.asyncio.session.AsyncSession", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of BaseModel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.base.T", "id": -1, "name": "T", "namespace": "app.models.base.BaseModel.create", "upper_bound": "app.models.base.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.base.T", "id": -1, "name": "T", "namespace": "app.models.base.BaseModel.create", "upper_bound": "app.models.base.BaseModel", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "app.models.base.BaseModel.create", "name": "create", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "db", "obj_in"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.base.T", "id": -1, "name": "T", "namespace": "app.models.base.BaseModel.create", "upper_bound": "app.models.base.BaseModel", "values": [], "variance": 0}}, "sqlalchemy.ext.asyncio.session.AsyncSession", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of BaseModel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.base.T", "id": -1, "name": "T", "namespace": "app.models.base.BaseModel.create", "upper_bound": "app.models.base.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.base.T", "id": -1, "name": "T", "namespace": "app.models.base.BaseModel.create", "upper_bound": "app.models.base.BaseModel", "values": [], "variance": 0}]}}}}, "created_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "app.models.base.BaseModel.created_at", "name": "created_at", "setter_type": null, "type": "datetime.datetime"}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "db"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "app.models.base.BaseModel.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "db"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.base.T", "id": -1, "name": "T", "namespace": "app.models.base.BaseModel.delete", "upper_bound": "app.models.base.BaseModel", "values": [], "variance": 0}, "sqlalchemy.ext.asyncio.session.AsyncSession"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete of BaseModel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.base.T", "id": -1, "name": "T", "namespace": "app.models.base.BaseModel.delete", "upper_bound": "app.models.base.BaseModel", "values": [], "variance": 0}]}}}, "get_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["cls", "db", "skip", "limit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_coroutine", "is_decorated"], "fullname": "app.models.base.BaseModel.get_all", "name": "get_all", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["cls", "db", "skip", "limit"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.base.T", "id": -1, "name": "T", "namespace": "app.models.base.BaseModel.get_all", "upper_bound": "app.models.base.BaseModel", "values": [], "variance": 0}}, "sqlalchemy.ext.asyncio.session.AsyncSession", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_all of BaseModel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.base.T", "id": -1, "name": "T", "namespace": "app.models.base.BaseModel.get_all", "upper_bound": "app.models.base.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.base.T", "id": -1, "name": "T", "namespace": "app.models.base.BaseModel.get_all", "upper_bound": "app.models.base.BaseModel", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "app.models.base.BaseModel.get_all", "name": "get_all", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["cls", "db", "skip", "limit"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.base.T", "id": -1, "name": "T", "namespace": "app.models.base.BaseModel.get_all", "upper_bound": "app.models.base.BaseModel", "values": [], "variance": 0}}, "sqlalchemy.ext.asyncio.session.AsyncSession", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_all of BaseModel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.base.T", "id": -1, "name": "T", "namespace": "app.models.base.BaseModel.get_all", "upper_bound": "app.models.base.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.base.T", "id": -1, "name": "T", "namespace": "app.models.base.BaseModel.get_all", "upper_bound": "app.models.base.BaseModel", "values": [], "variance": 0}]}}}}, "get_by_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "db", "id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_coroutine", "is_decorated"], "fullname": "app.models.base.BaseModel.get_by_id", "name": "get_by_id", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "db", "id"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.base.T", "id": -1, "name": "T", "namespace": "app.models.base.BaseModel.get_by_id", "upper_bound": "app.models.base.BaseModel", "values": [], "variance": 0}}, "sqlalchemy.ext.asyncio.session.AsyncSession", "builtins.int"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_by_id of BaseModel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.base.T", "id": -1, "name": "T", "namespace": "app.models.base.BaseModel.get_by_id", "upper_bound": "app.models.base.BaseModel", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.base.T", "id": -1, "name": "T", "namespace": "app.models.base.BaseModel.get_by_id", "upper_bound": "app.models.base.BaseModel", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "app.models.base.BaseModel.get_by_id", "name": "get_by_id", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "db", "id"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.base.T", "id": -1, "name": "T", "namespace": "app.models.base.BaseModel.get_by_id", "upper_bound": "app.models.base.BaseModel", "values": [], "variance": 0}}, "sqlalchemy.ext.asyncio.session.AsyncSession", "builtins.int"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_by_id of BaseModel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.base.T", "id": -1, "name": "T", "namespace": "app.models.base.BaseModel.get_by_id", "upper_bound": "app.models.base.BaseModel", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.base.T", "id": -1, "name": "T", "namespace": "app.models.base.BaseModel.get_by_id", "upper_bound": "app.models.base.BaseModel", "values": [], "variance": 0}]}}}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "app.models.base.BaseModel.id", "name": "id", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "db", "obj_in"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "app.models.base.BaseModel.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "db", "obj_in"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.base.T", "id": -1, "name": "T", "namespace": "app.models.base.BaseModel.update", "upper_bound": "app.models.base.BaseModel", "values": [], "variance": 0}, "sqlalchemy.ext.asyncio.session.AsyncSession", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update of BaseModel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.base.T", "id": -1, "name": "T", "namespace": "app.models.base.BaseModel.update", "upper_bound": "app.models.base.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.base.T", "id": -1, "name": "T", "namespace": "app.models.base.BaseModel.update", "upper_bound": "app.models.base.BaseModel", "values": [], "variance": 0}]}}}, "updated_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "app.models.base.BaseModel.updated_at", "name": "updated_at", "setter_type": null, "type": "datetime.datetime"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.base.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.models.base.BaseModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Field": {".class": "SymbolTableNode", "cross_ref": "sqlmodel.main.Field", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "SQLModel": {".class": "SymbolTableNode", "cross_ref": "sqlmodel.main.SQLModel", "kind": "Gdef"}, "T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.base.T", "name": "T", "upper_bound": "app.models.base.BaseModel", "values": [], "variance": 0}}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.base.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.base.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.base.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.base.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.base.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.base.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "select": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.select", "kind": "Gdef"}, "timezone": {".class": "SymbolTableNode", "cross_ref": "datetime.timezone", "kind": "Gdef"}}, "path": "app/models/base.py"}