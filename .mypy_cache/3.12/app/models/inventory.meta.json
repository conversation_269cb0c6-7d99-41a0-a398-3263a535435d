{"data_mtime": 1751559225, "dep_lines": [6, 9, 1, 2, 3, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.ext.asyncio", "app.models.base", "datetime", "enum", "typing", "sqlalchemy", "sqlmodel", "builtins", "_collections_abc", "_frozen_importlib", "abc", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic_core", "pydantic_core._pydantic_core", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.ext", "sqlalchemy.ext.asyncio.base", "sqlalchemy.ext.asyncio.session", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm", "sqlalchemy.orm.base", "sqlalchemy.orm.decl_api", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.relationships", "sqlalchemy.sql", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers", "sqlmodel.main"], "hash": "ba3f13f0959735bfcfb2d1812fbf167a50b8d053", "id": "app.models.inventory", "ignore_all": false, "interface_hash": "57288786bdc378e418890aedd9f3cbe0fdb33cce", "mtime": 1751557461, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "app/models/inventory.py", "plugin_data": null, "size": 4067, "suppressed": [], "version_id": "1.16.1"}