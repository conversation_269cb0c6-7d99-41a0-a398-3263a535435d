{"data_mtime": 1751559180, "dep_lines": [15, 5, 1, 3, 4, 6, 7, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 10, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["anyio._core._tasks", "collections.abc", "__future__", "sys", "abc", "types", "typing", "builtins", "_frozen_importlib", "_typeshed", "anyio._core"], "hash": "21b27c449aa694c47bcdc78b2439ff58c10d8692", "id": "anyio.abc._tasks", "ignore_all": true, "interface_hash": "aa6197b842ec5feb0eadf30510452d414ba561a2", "mtime": 1747044681, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/anyio/abc/_tasks.py", "plugin_data": null, "size": 3080, "suppressed": [], "version_id": "1.16.1"}