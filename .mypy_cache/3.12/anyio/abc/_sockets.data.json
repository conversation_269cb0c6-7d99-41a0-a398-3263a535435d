{".class": "MypyFile", "_fullname": "anyio.abc._sockets", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AddressFamily": {".class": "SymbolTableNode", "cross_ref": "socket.AddressFamily", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncExitStack": {".class": "SymbolTableNode", "cross_ref": "contextlib.AsyncExitStack", "kind": "Gdef"}, "ByteStream": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._streams.ByteStream", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef"}, "ConnectedUDPSocket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_raw_socket", 1], ["aclose", 1], ["receive", 1], ["send", 1]], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "anyio.abc._streams.UnreliableObjectStream"}, "anyio.abc._sockets._SocketProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.abc._sockets.ConnectedUDPSocket", "name": "ConnectedUDPSocket", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "anyio.abc._sockets.ConnectedUDPSocket", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "anyio.abc._sockets", "mro": ["anyio.abc._sockets.ConnectedUDPSocket", "anyio.abc._streams.UnreliableObjectStream", "anyio.abc._streams.UnreliableObjectReceiveStream", "anyio.abc._streams.UnreliableObjectSendStream", "anyio.abc._resources.AsyncResource", "anyio.abc._sockets._SocketProvider", "anyio._core._typedattr.TypedAttributeProvider", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._sockets.ConnectedUDPSocket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio.abc._sockets.ConnectedUDPSocket", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectedUNIXDatagramSocket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_raw_socket", 1], ["aclose", 1], ["receive", 1], ["send", 1]], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "anyio.abc._streams.UnreliableObjectStream"}, "anyio.abc._sockets._SocketProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.abc._sockets.ConnectedUNIXDatagramSocket", "name": "ConnectedUNIXDatagramSocket", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "anyio.abc._sockets.ConnectedUNIXDatagramSocket", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "anyio.abc._sockets", "mro": ["anyio.abc._sockets.ConnectedUNIXDatagramSocket", "anyio.abc._streams.UnreliableObjectStream", "anyio.abc._streams.UnreliableObjectReceiveStream", "anyio.abc._streams.UnreliableObjectSendStream", "anyio.abc._resources.AsyncResource", "anyio.abc._sockets._SocketProvider", "anyio._core._typedattr.TypedAttributeProvider", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._sockets.ConnectedUNIXDatagramSocket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio.abc._sockets.ConnectedUNIXDatagramSocket", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IOBase": {".class": "SymbolTableNode", "cross_ref": "io.IOBase", "kind": "Gdef"}, "IPAddressType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "anyio.abc._sockets.IPAddressType", "line": 21, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", "ipaddress.IPv4Address", "ipaddress.IPv6Address"], "uses_pep604_syntax": false}}}, "IPSockAddrType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "anyio.abc._sockets.IPSockAddrType", "line": 22, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "IPv4Address": {".class": "SymbolTableNode", "cross_ref": "ipaddress.IPv4Address", "kind": "Gdef"}, "IPv6Address": {".class": "SymbolTableNode", "cross_ref": "ipaddress.IPv6Address", "kind": "Gdef"}, "Listener": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._streams.Listener", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "SockAddrType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "anyio.abc._sockets.SockAddrType", "line": 23, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPSockAddrType"}, "builtins.str"], "uses_pep604_syntax": false}}}, "SocketAttribute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["anyio._core._typedattr.TypedAttributeSet"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.abc._sockets.SocketAttribute", "name": "SocketAttribute", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "anyio.abc._sockets.SocketAttribute", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "anyio.abc._sockets", "mro": ["anyio.abc._sockets.SocketAttribute", "anyio._core._typedattr.TypedAttributeSet", "builtins.object"], "names": {".class": "SymbolTable", "family": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.abc._sockets.SocketAttribute.family", "name": "family", "setter_type": null, "type": "socket.AddressFamily"}}, "local_address": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.abc._sockets.SocketAttribute.local_address", "name": "local_address", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.SockAddrType"}}}, "local_port": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.abc._sockets.SocketAttribute.local_port", "name": "local_port", "setter_type": null, "type": "builtins.int"}}, "raw_socket": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.abc._sockets.SocketAttribute.raw_socket", "name": "raw_socket", "setter_type": null, "type": "socket.socket"}}, "remote_address": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.abc._sockets.SocketAttribute.remote_address", "name": "remote_address", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.SockAddrType"}}}, "remote_port": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "anyio.abc._sockets.SocketAttribute.remote_port", "name": "remote_port", "setter_type": null, "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._sockets.SocketAttribute.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio.abc._sockets.SocketAttribute", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SocketListener": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_raw_socket", 1], ["accept", 1], ["aclose", 1]], "alt_promote": null, "bases": [{".class": "Instance", "args": ["anyio.abc._sockets.SocketStream"], "extra_attrs": null, "type_ref": "anyio.abc._streams.Listener"}, "anyio.abc._sockets._SocketProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.abc._sockets.SocketListener", "name": "SocketListener", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "anyio.abc._sockets.SocketListener", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "anyio.abc._sockets", "mro": ["anyio.abc._sockets.SocketListener", "anyio.abc._streams.Listener", "anyio.abc._resources.AsyncResource", "anyio.abc._sockets._SocketProvider", "anyio._core._typedattr.TypedAttributeProvider", "builtins.object"], "names": {".class": "SymbolTable", "accept": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "anyio.abc._sockets.SocketListener.accept", "name": "accept", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["anyio.abc._sockets.SocketListener"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "accept of SocketListener", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._sockets.SocketStream"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "anyio.abc._sockets.SocketListener.accept", "name": "accept", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["anyio.abc._sockets.SocketListener"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "accept of SocketListener", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "anyio.abc._sockets.SocketStream"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "serve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "handler", "task_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "anyio.abc._sockets.SocketListener.serve", "name": "serve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "handler", "task_group"], "arg_types": ["anyio.abc._sockets.SocketListener", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["anyio.abc._sockets.SocketStream"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": ["anyio.abc._tasks.TaskGroup", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "serve of SocketListener", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._sockets.SocketListener.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio.abc._sockets.SocketListener", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SocketStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_raw_socket", 1], ["aclose", 1], ["receive", 1], ["send", 1], ["send_eof", 1]], "alt_promote": null, "bases": ["anyio.abc._streams.ByteStream", "anyio.abc._sockets._SocketProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.abc._sockets.SocketStream", "name": "SocketStream", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "anyio.abc._sockets.SocketStream", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "anyio.abc._sockets", "mro": ["anyio.abc._sockets.SocketStream", "anyio.abc._streams.ByteStream", "anyio.abc._streams.ByteReceiveStream", "anyio.abc._streams.ByteSendStream", "anyio.abc._resources.AsyncResource", "anyio.abc._sockets._SocketProvider", "anyio._core._typedattr.TypedAttributeProvider", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._sockets.SocketStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio.abc._sockets.SocketStream", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "T_Retval": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._sockets.T_Retval", "name": "T_<PERSON><PERSON><PERSON>", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "TaskGroup": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._tasks.TaskGroup", "kind": "Gdef"}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "TypedAttributeProvider": {".class": "SymbolTableNode", "cross_ref": "anyio._core._typedattr.TypedAttributeProvider", "kind": "Gdef"}, "TypedAttributeSet": {".class": "SymbolTableNode", "cross_ref": "anyio._core._typedattr.TypedAttributeSet", "kind": "Gdef"}, "UDPPacketType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "anyio.abc._sockets.UDPPacketType", "line": 24, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", {".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.IPSockAddrType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "UDPSocket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_raw_socket", 1], ["aclose", 1], ["receive", 1], ["send", 1]], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.UDPPacketType"}], "extra_attrs": null, "type_ref": "anyio.abc._streams.UnreliableObjectStream"}, "anyio.abc._sockets._SocketProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.abc._sockets.UDPSocket", "name": "UDPSocket", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "anyio.abc._sockets.UDPSocket", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "anyio.abc._sockets", "mro": ["anyio.abc._sockets.UDPSocket", "anyio.abc._streams.UnreliableObjectStream", "anyio.abc._streams.UnreliableObjectReceiveStream", "anyio.abc._streams.UnreliableObjectSendStream", "anyio.abc._resources.AsyncResource", "anyio.abc._sockets._SocketProvider", "anyio._core._typedattr.TypedAttributeProvider", "builtins.object"], "names": {".class": "SymbolTable", "sendto": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "data", "host", "port"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "anyio.abc._sockets.UDPSocket.sendto", "name": "sendto", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "data", "host", "port"], "arg_types": ["anyio.abc._sockets.UDPSocket", "builtins.bytes", "builtins.str", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sendto of UDPSocket", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._sockets.UDPSocket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio.abc._sockets.UDPSocket", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UNIXDatagramPacketType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "anyio.abc._sockets.UNIXDatagramPacketType", "line": 25, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "UNIXDatagramSocket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_raw_socket", 1], ["aclose", 1], ["receive", 1], ["send", 1]], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "anyio.abc._sockets.UNIXDatagramPacketType"}], "extra_attrs": null, "type_ref": "anyio.abc._streams.UnreliableObjectStream"}, "anyio.abc._sockets._SocketProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.abc._sockets.UNIXDatagramSocket", "name": "UNIXDatagramSocket", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "anyio.abc._sockets.UNIXDatagramSocket", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "anyio.abc._sockets", "mro": ["anyio.abc._sockets.UNIXDatagramSocket", "anyio.abc._streams.UnreliableObjectStream", "anyio.abc._streams.UnreliableObjectReceiveStream", "anyio.abc._streams.UnreliableObjectSendStream", "anyio.abc._resources.AsyncResource", "anyio.abc._sockets._SocketProvider", "anyio._core._typedattr.TypedAttributeProvider", "builtins.object"], "names": {".class": "SymbolTable", "sendto": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "anyio.abc._sockets.UNIXDatagramSocket.sendto", "name": "sendto", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "path"], "arg_types": ["anyio.abc._sockets.UNIXDatagramSocket", "builtins.bytes", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sendto of UNIXDatagramSocket", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._sockets.UNIXDatagramSocket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio.abc._sockets.UNIXDatagramSocket", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UNIXSocketStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_raw_socket", 1], ["aclose", 1], ["receive", 1], ["receive_fds", 1], ["send", 1], ["send_eof", 1], ["send_fds", 1]], "alt_promote": null, "bases": ["anyio.abc._sockets.SocketStream"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.abc._sockets.UNIXSocketStream", "name": "UNIXSocketStream", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "anyio.abc._sockets.UNIXSocketStream", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "anyio.abc._sockets", "mro": ["anyio.abc._sockets.UNIXSocketStream", "anyio.abc._sockets.SocketStream", "anyio.abc._streams.ByteStream", "anyio.abc._streams.ByteReceiveStream", "anyio.abc._streams.ByteSendStream", "anyio.abc._resources.AsyncResource", "anyio.abc._sockets._SocketProvider", "anyio._core._typedattr.TypedAttributeProvider", "builtins.object"], "names": {".class": "SymbolTable", "receive_fds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "msglen", "maxfds"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "anyio.abc._sockets.UNIXSocketStream.receive_fds", "name": "receive_fds", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "msglen", "maxfds"], "arg_types": ["anyio.abc._sockets.UNIXSocketStream", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "receive_fds of UNIXSocketStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "anyio.abc._sockets.UNIXSocketStream.receive_fds", "name": "receive_fds", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "msglen", "maxfds"], "arg_types": ["anyio.abc._sockets.UNIXSocketStream", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "receive_fds of UNIXSocketStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "send_fds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "message", "fds"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "anyio.abc._sockets.UNIXSocketStream.send_fds", "name": "send_fds", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "message", "fds"], "arg_types": ["anyio.abc._sockets.UNIXSocketStream", "builtins.bytes", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "io.IOBase"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Collection"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send_fds of UNIXSocketStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "anyio.abc._sockets.UNIXSocketStream.send_fds", "name": "send_fds", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "message", "fds"], "arg_types": ["anyio.abc._sockets.UNIXSocketStream", "builtins.bytes", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "io.IOBase"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Collection"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send_fds of UNIXSocketStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._sockets.UNIXSocketStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio.abc._sockets.UNIXSocketStream", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UnreliableObjectStream": {".class": "SymbolTableNode", "cross_ref": "anyio.abc._streams.UnreliableObjectStream", "kind": "Gdef"}, "_NullAsyncContextManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.abc._sockets._NullAsyncContextManager", "name": "_NullAsyncContextManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "anyio.abc._sockets._NullAsyncContextManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "anyio.abc._sockets", "mro": ["anyio.abc._sockets._NullAsyncContextManager", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "anyio.abc._sockets._NullAsyncContextManager.__aenter__", "name": "__aenter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["anyio.abc._sockets._NullAsyncContextManager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aenter__ of _NullAsyncContextManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc_val", "exc_tb"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "anyio.abc._sockets._NullAsyncContextManager.__aexit__", "name": "__aexit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc_val", "exc_tb"], "arg_types": ["anyio.abc._sockets._NullAsyncContextManager", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aexit__ of _NullAsyncContextManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._sockets._NullAsyncContextManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio.abc._sockets._NullAsyncContextManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SocketProvider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_raw_socket", 1]], "alt_promote": null, "bases": ["anyio._core._typedattr.TypedAttributeProvider"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio.abc._sockets._SocketProvider", "name": "_SocketProvider", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "anyio.abc._sockets._SocketProvider", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "anyio.abc._sockets", "mro": ["anyio.abc._sockets._SocketProvider", "anyio._core._typedattr.TypedAttributeProvider", "builtins.object"], "names": {".class": "SymbolTable", "_raw_socket": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "anyio.abc._sockets._SocketProvider._raw_socket", "name": "_raw_socket", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["anyio.abc._sockets._SocketProvider"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_raw_socket of _SocketProvider", "ret_type": "socket.socket", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "anyio.abc._sockets._SocketProvider._raw_socket", "name": "_raw_socket", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["anyio.abc._sockets._SocketProvider"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_raw_socket of _SocketProvider", "ret_type": "socket.socket", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "extra_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "anyio.abc._sockets._SocketProvider.extra_attributes", "name": "extra_attributes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["anyio.abc._sockets._SocketProvider"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "extra_attributes of _SocketProvider", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "anyio.abc._sockets._SocketProvider.extra_attributes", "name": "extra_attributes", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["anyio.abc._sockets._SocketProvider"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "extra_attributes of _SocketProvider", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio.abc._sockets._SocketProvider.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio.abc._sockets._SocketProvider", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.abc._sockets.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.abc._sockets.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.abc._sockets.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.abc._sockets.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.abc._sockets.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.abc._sockets.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef"}, "typed_attribute": {".class": "SymbolTableNode", "cross_ref": "anyio._core._typedattr.typed_attribute", "kind": "Gdef"}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/anyio/abc/_sockets.py"}