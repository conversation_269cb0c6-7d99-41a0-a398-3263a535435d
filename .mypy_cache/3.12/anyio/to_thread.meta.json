{"data_mtime": 1751559180, "dep_lines": [8, 4, 9, 1, 3, 5, 6, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["anyio._core._eventloop", "collections.abc", "anyio.abc", "__future__", "sys", "typing", "warnings", "builtins", "_frozen_importlib", "_typeshed", "abc", "anyio._core", "anyio._core._synchronization"], "hash": "20655b28e90587eacb80518bd11b22c90abbca71", "id": "anyio.to_thread", "ignore_all": true, "interface_hash": "5af7bc609282ffad2f7c1732a29fb267d0beda4c", "mtime": 1747044681, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/anyio/to_thread.py", "plugin_data": null, "size": 2396, "suppressed": [], "version_id": "1.16.1"}