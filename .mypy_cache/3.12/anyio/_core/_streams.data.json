{".class": "MypyFile", "_fullname": "anyio._core._streams", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "MemoryObjectReceiveStream": {".class": "SymbolTableNode", "cross_ref": "anyio.streams.memory.MemoryObjectReceiveStream", "kind": "Gdef"}, "MemoryObjectSendStream": {".class": "SymbolTableNode", "cross_ref": "anyio.streams.memory.MemoryObjectSendStream", "kind": "Gdef"}, "MemoryObjectStreamState": {".class": "SymbolTableNode", "cross_ref": "anyio.streams.memory.MemoryObjectStreamState", "kind": "Gdef"}, "T_Item": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._streams.T_Item", "name": "T_Item", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._streams.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._streams.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._streams.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._streams.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._streams.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._streams.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "create_memory_object_stream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio._core._streams.create_memory_object_stream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "anyio.streams.memory.MemoryObjectSendStream"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio._core._streams.create_memory_object_stream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "anyio.streams.memory.MemoryObjectReceiveStream"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio._core._streams.create_memory_object_stream", "name": "create_memory_object_stream", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio._core._streams.create_memory_object_stream", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "anyio._core._streams.create_memory_object_stream", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "anyio._core._streams", "mro": ["anyio._core._streams.create_memory_object_stream", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["cls", "max_buffer_size", "item_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_trivial_self"], "fullname": "anyio._core._streams.create_memory_object_stream.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["cls", "max_buffer_size", "item_type"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio._core._streams.create_memory_object_stream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "anyio.streams.memory.MemoryObjectSendStream"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio._core._streams.create_memory_object_stream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "anyio.streams.memory.MemoryObjectReceiveStream"}], "partial_fallback": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio._core._streams.create_memory_object_stream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "anyio._core._streams.create_memory_object_stream"}}}, "builtins.float", {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__new__ of create_memory_object_stream", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio._core._streams.create_memory_object_stream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "anyio.streams.memory.MemoryObjectSendStream"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio._core._streams.create_memory_object_stream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "anyio.streams.memory.MemoryObjectReceiveStream"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._streams.create_memory_object_stream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio._core._streams.create_memory_object_stream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "anyio.streams.memory.MemoryObjectSendStream"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio._core._streams.create_memory_object_stream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "anyio.streams.memory.MemoryObjectReceiveStream"}], "partial_fallback": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio._core._streams.create_memory_object_stream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "anyio._core._streams.create_memory_object_stream"}}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio._core._streams.create_memory_object_stream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "anyio.streams.memory.MemoryObjectSendStream"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio._core._streams.create_memory_object_stream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "anyio.streams.memory.MemoryObjectReceiveStream"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio._core._streams.create_memory_object_stream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "anyio.streams.memory.MemoryObjectSendStream"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._streams.T_Item", "id": 1, "name": "T_Item", "namespace": "anyio._core._streams.create_memory_object_stream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "anyio.streams.memory.MemoryObjectReceiveStream"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": ["T_Item"], "typeddict_type": null}}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef"}, "warn": {".class": "SymbolTableNode", "cross_ref": "_warnings.warn", "kind": "Gdef"}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/anyio/_core/_streams.py"}