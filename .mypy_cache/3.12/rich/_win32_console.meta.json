{"data_mtime": 1751559177, "dep_lines": [17, 20, 21, 578, 6, 7, 8, 16, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 10, 5, 10, 5, 30, 30, 30], "dependencies": ["ctypes.wintypes", "rich.color", "rich.style", "rich.console", "ctypes", "sys", "typing", "time", "builtins", "_ctypes", "_frozen_importlib", "abc"], "hash": "4f20d1a3e31a9d8ce949fb30096b341f42c54769", "id": "rich._win32_console", "ignore_all": true, "interface_hash": "77778ac7fc6d16ddf09c59aa8053a775b53ccc06", "mtime": 1747044682, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/rich/_win32_console.py", "plugin_data": null, "size": 22719, "suppressed": [], "version_id": "1.16.1"}