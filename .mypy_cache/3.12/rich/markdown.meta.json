{"data_mtime": 1751559177, "dep_lines": [7, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 1, 3, 4, 6, 16, 690, 770, 771, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 20, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["markdown_it.token", "rich.table", "rich.box", "rich._loop", "rich._stack", "rich.console", "rich.containers", "rich.jupyter", "rich.panel", "rich.rule", "rich.segment", "rich.style", "rich.syntax", "rich.text", "__future__", "sys", "typing", "markdown_it", "rich", "<PERSON><PERSON><PERSON><PERSON>", "io", "pydoc", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "datetime", "enum", "markdown_it.main", "markdown_it.renderer", "markdown_it.utils", "os", "rich.theme", "types"], "hash": "04b2c68ab17569cae96918fc36906d37a548ff0a", "id": "rich.markdown", "ignore_all": true, "interface_hash": "ef85063b8e273945e8b3a00df06763c72aedfc37", "mtime": 1747044682, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/rich/markdown.py", "plugin_data": null, "size": 25969, "suppressed": [], "version_id": "1.16.1"}