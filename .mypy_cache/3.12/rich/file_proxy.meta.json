{"data_mtime": 1751559177, "dep_lines": [4, 5, 8, 1, 2, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 10, 5, 5, 30, 30, 30], "dependencies": ["rich.ansi", "rich.text", "rich.console", "io", "typing", "builtins", "_frozen_importlib", "_io", "abc"], "hash": "5f7dee3ccc5b50b923adaec01508dfb25984acd6", "id": "rich.file_proxy", "ignore_all": true, "interface_hash": "0fecd5388638e584f0485c2a9f145f0fa93626cd", "mtime": 1747044682, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/rich/file_proxy.py", "plugin_data": null, "size": 1683, "suppressed": [], "version_id": "1.16.1"}