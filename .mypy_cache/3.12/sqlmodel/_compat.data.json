{".class": "MypyFile", "_fullname": "sqlmodel._compat", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbstractSet": {".class": "SymbolTableNode", "cross_ref": "typing.AbstractSet", "kind": "Gdef"}, "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Annotated", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseConfig": {".class": "SymbolTableNode", "cross_ref": "pydantic.config.ConfigDict", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ConfigError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "from_module_getattr"], "fullname": "pydantic.errors.ConfigError", "name": "ConfigError", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "ContextVar": {".class": "SymbolTableNode", "cross_ref": "_contextvars.ContextVar", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FakeMetadata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlmodel._compat.FakeMetadata", "name": "FakeMetadata", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlmodel._compat.FakeMetadata", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlmodel._compat", "mro": ["sqlmodel._compat.FakeMetadata", "builtins.object"], "names": {".class": "SymbolTable", "decimal_places": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlmodel._compat.FakeMetadata.decimal_places", "name": "decimal_places", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "max_digits": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlmodel._compat.FakeMetadata.max_digits", "name": "max_digits", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "max_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlmodel._compat.FakeMetadata.max_length", "name": "max_length", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlmodel._compat.FakeMetadata.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlmodel._compat.FakeMetadata", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FieldInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.FieldInfo", "kind": "Gdef"}, "ForwardRef": {".class": "SymbolTableNode", "cross_ref": "typing.ForwardRef", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "IS_PYDANTIC_V2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlmodel._compat.IS_PYDANTIC_V2", "name": "IS_PYDANTIC_V2", "setter_type": null, "type": "builtins.bool"}}, "InstanceOrType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlmodel._compat.T", "id": 1, "name": "T", "namespace": "sqlmodel._compat.InstanceOrType", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "sqlmodel._compat.InstanceOrType", "line": 38, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlmodel._compat.T", "id": 1, "name": "T", "namespace": "sqlmodel._compat.InstanceOrType", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlmodel._compat.T", "id": 1, "name": "T", "namespace": "sqlmodel._compat.InstanceOrType", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "uses_pep604_syntax": false}}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "MaxLen": {".class": "SymbolTableNode", "cross_ref": "annotated_types.MaxLen", "kind": "Gdef"}, "ModelField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlmodel._compat.ModelField", "name": "ModelField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlmodel._compat.ModelField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlmodel._compat", "mro": ["sqlmodel._compat.ModelField", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlmodel._compat.ModelField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlmodel._compat.ModelField", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ModelMetaclass": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._model_construction.ModelMetaclass", "kind": "Gdef"}, "NoneType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlmodel._compat.NoneType", "line": 36, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "NoneType"}}}, "ObjectWithUpdateWrapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlmodel._compat.ObjectWithUpdateWrapper", "name": "ObjectWithUpdateWrapper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlmodel._compat.ObjectWithUpdateWrapper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 50, "name": "obj", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 51, "name": "update", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "sqlmodel._compat", "mro": ["sqlmodel._compat.ObjectWithUpdateWrapper", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "sqlmodel._compat.ObjectWithUpdateWrapper.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__getattribute__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sqlmodel._compat.ObjectWithUpdateWrapper.__getattribute__", "name": "__getattribute__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["sqlmodel._compat.ObjectWithUpdateWrapper", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__getattribute__ of ObjectWithUpdateWrapper", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "update"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlmodel._compat.ObjectWithUpdateWrapper.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "update"], "arg_types": ["sqlmodel._compat.ObjectWithUpdateWrapper", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ObjectWithUpdateWrapper", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "sqlmodel._compat.ObjectWithUpdateWrapper.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "obj"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "update"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["obj", "update"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "sqlmodel._compat.ObjectWithUpdateWrapper.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["obj", "update"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ObjectWithUpdateWrapper", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "sqlmodel._compat.ObjectWithUpdateWrapper.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["obj", "update"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ObjectWithUpdateWrapper", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "obj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlmodel._compat.ObjectWithUpdateWrapper.obj", "name": "obj", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "update": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlmodel._compat.ObjectWithUpdateWrapper.update", "name": "update", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlmodel._compat.ObjectWithUpdateWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlmodel._compat.ObjectWithUpdateWrapper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PYDANTIC_MINOR_VERSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlmodel._compat.PYDANTIC_MINOR_VERSION", "name": "PYDANTIC_MINOR_VERSION", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "PYDANTIC_VERSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlmodel._compat.PYDANTIC_VERSION", "name": "PYDANTIC_VERSION", "setter_type": null, "type": "builtins.str"}}, "P_VERSION": {".class": "SymbolTableNode", "cross_ref": "pydantic.version.VERSION", "kind": "Gdef"}, "PydanticMetadata": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._fields.PydanticMetadata", "kind": "Gdef"}, "ROOT_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "from_module_getattr"], "fullname": "pydantic.utils.ROOT_KEY", "name": "ROOT_KEY", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "RelationshipInfo": {".class": "SymbolTableNode", "cross_ref": "sqlmodel.main.RelationshipInfo", "kind": "Gdef"}, "Representation": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._repr.Representation", "kind": "Gdef"}, "SHAPE_SINGLETON": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sqlmodel._compat.SHAPE_SINGLETON", "name": "SHAPE_SINGLETON", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sqlmodel._compat.SHAPE_SINGLETON", "source_any": null, "type_of_any": 3}}}, "SQLModel": {".class": "SymbolTableNode", "cross_ref": "sqlmodel.main.SQLModel", "kind": "Gdef"}, "SQLModelConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlmodel._compat.SQLModelConfig", "name": "SQLModelConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlmodel._compat.SQLModelConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlmodel._compat", "mro": ["sqlmodel._compat.SQLModelConfig", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["title", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["model_title_generator", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.type"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["field_title_generator", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["pydantic.fields.FieldInfo", "pydantic.fields.ComputedFieldInfo"], "uses_pep604_syntax": true}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["str_to_lower", "builtins.bool"], ["str_to_upper", "builtins.bool"], ["str_strip_whitespace", "builtins.bool"], ["str_min_length", "builtins.int"], ["str_max_length", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["extra", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.ExtraValues"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["frozen", "builtins.bool"], ["populate_by_name", "builtins.bool"], ["use_enum_values", "builtins.bool"], ["validate_assignment", "builtins.bool"], ["arbitrary_types_allowed", "builtins.bool"], ["from_attributes", "builtins.bool"], ["loc_by_alias", "builtins.bool"], ["alias_generator", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "pydantic.aliases.AliasGenerator", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["ignored_types", {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.tuple"}], ["allow_inf_nan", "builtins.bool"], ["json_schema_extra", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonDict"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonSchemaExtraCallable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["json_encoders", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.object"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonEncoder"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["strict", "builtins.bool"], ["revalidate_instances", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "always"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "never"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "subclass-instances"}], "uses_pep604_syntax": false}], ["ser_j<PERSON>_<PERSON><PERSON><PERSON>", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "iso8601"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "float"}], "uses_pep604_syntax": false}], ["ser_json_bytes", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "utf8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "base64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hex"}], "uses_pep604_syntax": false}], ["val_json_bytes", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "utf8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "base64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hex"}], "uses_pep604_syntax": false}], ["ser_json_inf_nan", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "null"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "constants"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "strings"}], "uses_pep604_syntax": false}], ["validate_default", "builtins.bool"], ["validate_return", "builtins.bool"], ["protected_namespaces", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}], ["hide_input_in_errors", "builtins.bool"], ["defer_build", "builtins.bool"], ["plugin_settings", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["schema_generator", {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic._internal._generate_schema.GenerateSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["json_schema_serialization_defaults_required", "builtins.bool"], ["json_schema_mode_override", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["coerce_numbers_to_str", "builtins.bool"], ["regex_engine", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rust-regex"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "python-re"}], "uses_pep604_syntax": false}], ["validation_error_cause", "builtins.bool"], ["use_attribute_docstrings", "builtins.bool"], ["cache_strings", {".class": "UnionType", "items": ["builtins.bool", {".class": "LiteralType", "fallback": "builtins.str", "value": "all"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "keys"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}], "uses_pep604_syntax": true}], ["validate_by_alias", "builtins.bool"], ["validate_by_name", "builtins.bool"], ["serialize_by_alias", "builtins.bool"], ["table", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["registry", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}}}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlmodel._compat.T", "name": "T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Undefined": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticUndefined", "kind": "Gdef"}, "UndefinedType": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticUndefinedType", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UnionType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlmodel._compat.UnionType", "name": "UnionType", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "typing._SpecialForm"], "uses_pep604_syntax": false}}}, "ValueItems": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "from_module_getattr"], "fullname": "pydantic.utils.ValueItems", "name": "ValueItems", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "_TSQLModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlmodel._compat._TSQLModel", "name": "_TSQLModel", "upper_bound": "sqlmodel.main.SQLModel", "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlmodel._compat.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlmodel._compat.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlmodel._compat.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlmodel._compat.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlmodel._compat.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlmodel._compat.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_calculate_keys": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "include", "exclude", "exclude_unset", "update"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlmodel._compat._calculate_keys", "name": "_calculate_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "include", "exclude", "exclude_unset", "update"], "arg_types": ["sqlmodel.main.SQLModel", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_calculate_keys", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.AbstractSet"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_union_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["t"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlmodel._compat._is_union_type", "name": "_is_union_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["t"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_is_union_type", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "finish_init": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "sqlmodel._compat.finish_init", "name": "finish_init", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "_contextvars.ContextVar"}}}, "get_annotations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["class_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlmodel._compat.get_annotations", "name": "get_annotations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["class_dict"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_annotations", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_args": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.get_args", "kind": "Gdef"}, "get_config_value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3, 5], "arg_names": ["model", "parameter", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlmodel._compat.get_config_value", "name": "get_config_value", "type": {".class": "CallableType", "arg_kinds": [3, 3, 5], "arg_names": ["model", "parameter", "default"], "arg_types": [{".class": "TypeAliasType", "args": ["sqlmodel.main.SQLModel"], "type_ref": "sqlmodel._compat.InstanceOrType"}, "builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_config_value", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_field_metadata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["field"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlmodel._compat.get_field_metadata", "name": "get_field_metadata", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["field"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_field_metadata", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_fields_set": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["object"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlmodel._compat.get_fields_set", "name": "get_fields_set", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["object"], "arg_types": [{".class": "TypeAliasType", "args": ["sqlmodel.main.SQLModel"], "type_ref": "sqlmodel._compat.InstanceOrType"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_fields_set", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pydantic.main.BaseModel"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_model_fields": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlmodel._compat.get_model_fields", "name": "get_model_fields", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["model"], "arg_types": [{".class": "TypeAliasType", "args": ["pydantic.main.BaseModel"], "type_ref": "sqlmodel._compat.InstanceOrType"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_model_fields", "ret_type": {".class": "Instance", "args": ["builtins.str", "pydantic.fields.FieldInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_origin": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.get_origin", "kind": "Gdef"}, "get_relationship_to": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["name", "rel_info", "annotation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlmodel._compat.get_relationship_to", "name": "get_relationship_to", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["name", "rel_info", "annotation"], "arg_types": ["builtins.str", "sqlmodel.main.RelationshipInfo", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_relationship_to", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_sa_type_from_field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["field"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlmodel._compat.get_sa_type_from_field", "name": "get_sa_type_from_field", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["field"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_sa_type_from_field", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_sa_type_from_type_annotation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["annotation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlmodel._compat.get_sa_type_from_type_annotation", "name": "get_sa_type_from_type_annotation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["annotation"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_sa_type_from_type_annotation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "init_pydantic_private_attrs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["new_object"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlmodel._compat.init_pydantic_private_attrs", "name": "init_pydantic_private_attrs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["new_object"], "arg_types": [{".class": "TypeAliasType", "args": ["sqlmodel.main.SQLModel"], "type_ref": "sqlmodel._compat.InstanceOrType"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "init_pydantic_private_attrs", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_field_noneable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["field"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlmodel._compat.is_field_noneable", "name": "is_field_noneable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["field"], "arg_types": ["pydantic.fields.FieldInfo"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_field_noneable", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_table_model_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlmodel._compat.is_table_model_class", "name": "is_table_model_class", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_table_model_class", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "partial_init": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlmodel._compat.partial_init", "name": "partial_init", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "partial_init", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlmodel._compat.partial_init", "name": "partial_init", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "partial_init", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "post_init_field_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["field_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlmodel._compat.post_init_field_info", "name": "post_init_field_info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["field_info"], "arg_types": ["pydantic.fields.FieldInfo"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_init_field_info", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resolve_annotations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "from_module_getattr"], "fullname": "pydantic.typing.resolve_annotations", "name": "resolve_annotations", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "set_config_value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3, 3], "arg_names": ["model", "parameter", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlmodel._compat.set_config_value", "name": "set_config_value", "type": {".class": "CallableType", "arg_kinds": [3, 3, 3], "arg_names": ["model", "parameter", "value"], "arg_types": [{".class": "TypeAliasType", "args": ["sqlmodel.main.SQLModel"], "type_ref": "sqlmodel._compat.InstanceOrType"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_config_value", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sqlmodel_init": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlmodel._compat.sqlmodel_init", "name": "sqlmodel_init", "type": {".class": "CallableType", "arg_kinds": [3, 3], "arg_names": ["self", "data"], "arg_types": ["sqlmodel.main.SQLModel", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sqlmodel_init", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sqlmodel_table_construct": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3, 5], "arg_names": ["self_instance", "values", "_fields_set"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlmodel._compat.sqlmodel_table_construct", "name": "sqlmodel_table_construct", "type": {".class": "CallableType", "arg_kinds": [3, 3, 5], "arg_names": ["self_instance", "values", "_fields_set"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlmodel._compat._TSQLModel", "id": -1, "name": "_TSQLModel", "namespace": "sqlmodel._compat.sqlmodel_table_construct", "upper_bound": "sqlmodel.main.SQLModel", "values": [], "variance": 0}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sqlmodel_table_construct", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlmodel._compat._TSQLModel", "id": -1, "name": "_TSQLModel", "namespace": "sqlmodel._compat.sqlmodel_table_construct", "upper_bound": "sqlmodel.main.SQLModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlmodel._compat._TSQLModel", "id": -1, "name": "_TSQLModel", "namespace": "sqlmodel._compat.sqlmodel_table_construct", "upper_bound": "sqlmodel.main.SQLModel", "values": [], "variance": 0}]}}}, "sqlmodel_validate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "obj", "strict", "from_attributes", "context", "update"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlmodel._compat.sqlmodel_validate", "name": "sqlmodel_validate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "obj", "strict", "from_attributes", "context", "update"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlmodel._compat._TSQLModel", "id": -1, "name": "_TSQLModel", "namespace": "sqlmodel._compat.sqlmodel_validate", "upper_bound": "sqlmodel.main.SQLModel", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sqlmodel_validate", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlmodel._compat._TSQLModel", "id": -1, "name": "_TSQLModel", "namespace": "sqlmodel._compat.sqlmodel_validate", "upper_bound": "sqlmodel.main.SQLModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlmodel._compat._TSQLModel", "id": -1, "name": "_TSQLModel", "namespace": "sqlmodel._compat.sqlmodel_validate", "upper_bound": "sqlmodel.main.SQLModel", "values": [], "variance": 0}]}}}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}, "validate_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "from_module_getattr"], "fullname": "pydantic.main.validate_model", "name": "validate_model", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlmodel/_compat.py"}