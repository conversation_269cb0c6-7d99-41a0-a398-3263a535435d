{"data_mtime": 1751559223, "dep_lines": [50, 51, 52, 53, 54, 83, 86, 87, 29, 43, 57, 1, 2, 3, 4, 5, 6, 7, 8, 28, 30, 55, 88, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 25, 25, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.decl_api", "sqlalchemy.orm.instrumentation", "sqlalchemy.sql.schema", "sqlalchemy.sql.sqltypes", "sqlmodel.sql.sqltypes", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.fields", "sqlalchemy.orm", "sqlmodel._compat", "ipaddress", "uuid", "weakref", "datetime", "decimal", "enum", "pathlib", "typing", "pydantic", "sqlalchemy", "typing_extensions", "pydantic_core", "builtins", "_collections_abc", "_frozen_importlib", "abc", "annotated_types", "pydantic._internal", "pydantic._internal._generate_schema", "pydantic._internal._generics", "pydantic.aliases", "pydantic.config", "pydantic.main", "pydantic.types", "pydantic_core._pydantic_core", "re", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm.base", "sqlalchemy.orm.clsregistry", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.relationships", "sqlalchemy.sql", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers", "types"], "hash": "83fff049e4b7f0c9cb271e672af0b9c9ffc14bea", "id": "sqlmodel.main", "ignore_all": true, "interface_hash": "68c8fce9239da3609273a91fe06727347e6c6d77", "mtime": 1747044683, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlmodel/main.py", "plugin_data": null, "size": 37223, "suppressed": [], "version_id": "1.16.1"}