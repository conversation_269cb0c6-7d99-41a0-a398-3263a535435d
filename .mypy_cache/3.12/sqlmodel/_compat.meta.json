{"data_mtime": 1751559223, "dep_lines": [78, 79, 80, 23, 33, 368, 379, 383, 384, 1, 2, 3, 4, 5, 21, 24, 76, 81, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 25, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._fields", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.fields", "sqlmodel.main", "pydantic.errors", "pydantic.main", "pydantic.typing", "pydantic.utils", "types", "contextlib", "<PERSON><PERSON><PERSON>", "dataclasses", "typing", "pydantic", "typing_extensions", "annotated_types", "pydantic_core", "builtins", "_collections_abc", "_contextvars", "_frozen_importlib", "_typeshed", "abc", "pydantic._internal", "pydantic._internal._generate_schema", "pydantic.aliases", "pydantic.config", "pydantic.deprecated", "pydantic.deprecated.config", "pydantic.version", "re"], "hash": "ceab7620cc78abcb40167d69a00d56beb7daed4d", "id": "sqlmodel._compat", "ignore_all": true, "interface_hash": "d4f52085b3c03c69e804aca1534b7856f298f460", "mtime": 1747044683, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/sqlmodel/_compat.py", "plugin_data": null, "size": 22012, "suppressed": [], "version_id": "1.16.1"}