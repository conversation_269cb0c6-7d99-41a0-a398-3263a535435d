{"data_mtime": 1751559178, "dep_lines": [2, 3, 1, 4, 5, 6, 7, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["asyncio.events", "collections.abc", "sys", "<PERSON><PERSON><PERSON>", "types", "typing", "typing_extensions", "builtins", "_contextvars", "_frozen_importlib", "_typeshed", "abc", "asyncio"], "hash": "db943b3d0d864b083fb437db0577e213278e33d6", "id": "_asyncio", "ignore_all": true, "interface_hash": "97694d9b664763155a4f937fe809b44b9255a41f", "mtime": 1751558983, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/mypy/typeshed/stdlib/_asyncio.pyi", "plugin_data": null, "size": 5127, "suppressed": [], "version_id": "1.16.1"}