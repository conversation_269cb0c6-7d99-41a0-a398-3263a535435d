{".class": "MypyFile", "_fullname": "asyncio.unix_events", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ABCMeta": {".class": "SymbolTableNode", "cross_ref": "abc.ABCMeta", "kind": "Gdef", "module_hidden": true, "module_public": false}, "AbstractChildWatcher": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__enter__", 1], ["__exit__", 1], ["add_child_handler", 1], ["attach_loop", 1], ["close", 1], ["is_active", 1], ["remove_child_handler", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asyncio.unix_events.AbstractChildWatcher", "name": "AbstractChildWatcher", "type_vars": []}, "deletable_attributes": [], "deprecated": "class asyncio.unix_events.AbstractChildWatcher is deprecated: Deprecated as of Python 3.12; will be removed in Python 3.14", "flags": ["is_abstract"], "fullname": "asyncio.unix_events.AbstractChildWatcher", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "asyncio.unix_events", "mro": ["asyncio.unix_events.AbstractChildWatcher", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "asyncio.unix_events.AbstractChildWatcher.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.AbstractChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.AbstractChildWatcher", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__enter__ of AbstractChildWatcher", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.AbstractChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.AbstractChildWatcher", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.AbstractChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.AbstractChildWatcher", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "asyncio.unix_events.AbstractChildWatcher.__enter__", "name": "__enter__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.AbstractChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.AbstractChildWatcher", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__enter__ of AbstractChildWatcher", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.AbstractChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.AbstractChildWatcher", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.AbstractChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.AbstractChildWatcher", "values": [], "variance": 0}]}}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "asyncio.unix_events.AbstractChildWatcher.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["asyncio.unix_events.AbstractChildWatcher", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__exit__ of AbstractChildWatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "asyncio.unix_events.AbstractChildWatcher.__exit__", "name": "__exit__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["asyncio.unix_events.AbstractChildWatcher", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__exit__ of AbstractChildWatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "add_child_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 2], "arg_names": ["self", "pid", "callback", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "asyncio.unix_events.AbstractChildWatcher.add_child_handler", "name": "add_child_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2], "arg_names": ["self", "pid", "callback", "args"], "arg_types": ["asyncio.unix_events.AbstractChildWatcher", "builtins.int", {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": [null, null, null], "arg_types": ["builtins.int", "builtins.int", {".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events._Ts", "id": -1, "min_len": 0, "name": "_Ts", "namespace": "asyncio.unix_events.AbstractChildWatcher.add_child_handler", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events._Ts", "id": -1, "min_len": 0, "name": "_Ts", "namespace": "asyncio.unix_events.AbstractChildWatcher.add_child_handler", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_child_handler of AbstractChildWatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events._Ts", "id": -1, "min_len": 0, "name": "_Ts", "namespace": "asyncio.unix_events.AbstractChildWatcher.add_child_handler", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "asyncio.unix_events.AbstractChildWatcher.add_child_handler", "name": "add_child_handler", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2], "arg_names": ["self", "pid", "callback", "args"], "arg_types": ["asyncio.unix_events.AbstractChildWatcher", "builtins.int", {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": [null, null, null], "arg_types": ["builtins.int", "builtins.int", {".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events._Ts", "id": -1, "min_len": 0, "name": "_Ts", "namespace": "asyncio.unix_events.AbstractChildWatcher.add_child_handler", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events._Ts", "id": -1, "min_len": 0, "name": "_Ts", "namespace": "asyncio.unix_events.AbstractChildWatcher.add_child_handler", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_child_handler of AbstractChildWatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events._Ts", "id": -1, "min_len": 0, "name": "_Ts", "namespace": "asyncio.unix_events.AbstractChildWatcher.add_child_handler", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}]}}}}, "attach_loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "loop"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "asyncio.unix_events.AbstractChildWatcher.attach_loop", "name": "attach_loop", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "loop"], "arg_types": ["asyncio.unix_events.AbstractChildWatcher", {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "attach_loop of AbstractChildWatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "asyncio.unix_events.AbstractChildWatcher.attach_loop", "name": "attach_loop", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "loop"], "arg_types": ["asyncio.unix_events.AbstractChildWatcher", {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "attach_loop of AbstractChildWatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "asyncio.unix_events.AbstractChildWatcher.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["asyncio.unix_events.AbstractChildWatcher"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of Abstract<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "asyncio.unix_events.AbstractChildWatcher.close", "name": "close", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["asyncio.unix_events.AbstractChildWatcher"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of Abstract<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_active": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "asyncio.unix_events.AbstractChildWatcher.is_active", "name": "is_active", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["asyncio.unix_events.AbstractChildWatcher"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_active of AbstractChildWatcher", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "asyncio.unix_events.AbstractChildWatcher.is_active", "name": "is_active", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["asyncio.unix_events.AbstractChildWatcher"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_active of AbstractChildWatcher", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "remove_child_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "pid"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "asyncio.unix_events.AbstractChildWatcher.remove_child_handler", "name": "remove_child_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pid"], "arg_types": ["asyncio.unix_events.AbstractChildWatcher", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "remove_child_handler of AbstractChildWatcher", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "asyncio.unix_events.AbstractChildWatcher.remove_child_handler", "name": "remove_child_handler", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pid"], "arg_types": ["asyncio.unix_events.AbstractChildWatcher", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "remove_child_handler of AbstractChildWatcher", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.AbstractChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.AbstractChildWatcher", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AbstractEventLoop": {".class": "SymbolTableNode", "cross_ref": "asyncio.events.AbstractEventLoop", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseChildWatcher": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__enter__", 1], ["__exit__", 1], ["add_child_handler", 1], ["remove_child_handler", 1]], "alt_promote": null, "bases": ["asyncio.unix_events.AbstractChildWatcher"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "asyncio.unix_events.BaseChildWatcher", "name": "BaseChildWatcher", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "asyncio.unix_events.BaseChildWatcher", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "asyncio.unix_events", "mro": ["asyncio.unix_events.BaseChildWatcher", "asyncio.unix_events.AbstractChildWatcher", "builtins.object"], "names": {".class": "SymbolTable", "attach_loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "loop"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.BaseChildWatcher.attach_loop", "name": "attach_loop", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "loop"], "arg_types": ["asyncio.unix_events.BaseChildWatcher", {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "attach_loop of BaseChildWatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.BaseChildWatcher.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["asyncio.unix_events.BaseChildWatcher"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of BaseChildWatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_active": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.BaseChildWatcher.is_active", "name": "is_active", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["asyncio.unix_events.BaseChildWatcher"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_active of BaseChildWatcher", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.BaseChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.BaseChildWatcher", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseDefaultEventLoopPolicy": {".class": "SymbolTableNode", "cross_ref": "asyncio.events.BaseDefaultEventLoopPolicy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseSelectorEventLoop": {".class": "SymbolTableNode", "cross_ref": "asyncio.selector_events.BaseSelectorEventLoop", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DefaultEventLoopPolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "asyncio.unix_events.DefaultEventLoopPolicy", "line": 192, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "asyncio.unix_events._UnixDefaultEventLoopPolicy"}}, "FastChildWatcher": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["asyncio.unix_events.BaseChildWatcher"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asyncio.unix_events.FastChildWatcher", "name": "FastChildWatcher", "type_vars": []}, "deletable_attributes": [], "deprecated": "class asyncio.unix_events.FastChildWatcher is deprecated: Deprecated as of Python 3.12; will be removed in Python 3.14", "flags": [], "fullname": "asyncio.unix_events.FastChildWatcher", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "asyncio.unix_events", "mro": ["asyncio.unix_events.FastChildWatcher", "asyncio.unix_events.BaseChildWatcher", "asyncio.unix_events.AbstractChildWatcher", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.unix_events.FastChildWatcher.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.FastChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.FastChildWatcher", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__enter__ of FastChildWatcher", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.FastChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.FastChildWatcher", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.FastChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.FastChildWatcher", "values": [], "variance": 0}]}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.FastChildWatcher.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["asyncio.unix_events.FastChildWatcher", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__exit__ of FastChildWatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_child_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2], "arg_names": ["self", "pid", "callback", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.FastChildWatcher.add_child_handler", "name": "add_child_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2], "arg_names": ["self", "pid", "callback", "args"], "arg_types": ["asyncio.unix_events.FastChildWatcher", "builtins.int", {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": [null, null, null], "arg_types": ["builtins.int", "builtins.int", {".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events._Ts", "id": -1, "min_len": 0, "name": "_Ts", "namespace": "asyncio.unix_events.FastChildWatcher.add_child_handler", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events._Ts", "id": -1, "min_len": 0, "name": "_Ts", "namespace": "asyncio.unix_events.FastChildWatcher.add_child_handler", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_child_handler of FastChildWatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events._Ts", "id": -1, "min_len": 0, "name": "_Ts", "namespace": "asyncio.unix_events.FastChildWatcher.add_child_handler", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}]}}}, "remove_child_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pid"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.FastChildWatcher.remove_child_handler", "name": "remove_child_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pid"], "arg_types": ["asyncio.unix_events.FastChildWatcher", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "remove_child_handler of FastChildWatcher", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.FastChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.FastChildWatcher", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MultiLoopChildWatcher": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["asyncio.unix_events.AbstractChildWatcher"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asyncio.unix_events.MultiLoopChildWatcher", "name": "MultiLoopChildWatcher", "type_vars": []}, "deletable_attributes": [], "deprecated": "class asyncio.unix_events.MultiLoopChildWatcher is deprecated: Deprecated as of Python 3.12; will be removed in Python 3.14", "flags": [], "fullname": "asyncio.unix_events.MultiLoopChildWatcher", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "asyncio.unix_events", "mro": ["asyncio.unix_events.MultiLoopChildWatcher", "asyncio.unix_events.AbstractChildWatcher", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.unix_events.MultiLoopChildWatcher.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.MultiLoopChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.MultiLoopChildWatcher", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__enter__ of MultiLoopChildWatcher", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.MultiLoopChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.MultiLoopChildWatcher", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.MultiLoopChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.MultiLoopChildWatcher", "values": [], "variance": 0}]}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.MultiLoopChildWatcher.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["asyncio.unix_events.MultiLoopChildWatcher", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__exit__ of MultiLoopChildWatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_child_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2], "arg_names": ["self", "pid", "callback", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.MultiLoopChildWatcher.add_child_handler", "name": "add_child_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2], "arg_names": ["self", "pid", "callback", "args"], "arg_types": ["asyncio.unix_events.MultiLoopChildWatcher", "builtins.int", {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": [null, null, null], "arg_types": ["builtins.int", "builtins.int", {".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events._Ts", "id": -1, "min_len": 0, "name": "_Ts", "namespace": "asyncio.unix_events.MultiLoopChildWatcher.add_child_handler", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events._Ts", "id": -1, "min_len": 0, "name": "_Ts", "namespace": "asyncio.unix_events.MultiLoopChildWatcher.add_child_handler", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_child_handler of MultiLoopChildWatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events._Ts", "id": -1, "min_len": 0, "name": "_Ts", "namespace": "asyncio.unix_events.MultiLoopChildWatcher.add_child_handler", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}]}}}, "attach_loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "loop"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.MultiLoopChildWatcher.attach_loop", "name": "attach_loop", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "loop"], "arg_types": ["asyncio.unix_events.MultiLoopChildWatcher", {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "attach_loop of MultiLoopChildWatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.MultiLoopChildWatcher.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["asyncio.unix_events.MultiLoopChildWatcher"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of MultiLoopChildWatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_active": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.MultiLoopChildWatcher.is_active", "name": "is_active", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["asyncio.unix_events.MultiLoopChildWatcher"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_active of MultiLoopChildWatcher", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_child_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pid"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.MultiLoopChildWatcher.remove_child_handler", "name": "remove_child_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pid"], "arg_types": ["asyncio.unix_events.MultiLoopChildWatcher", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "remove_child_handler of MultiLoopChildWatcher", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.MultiLoopChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.MultiLoopChildWatcher", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PidfdChildWatcher": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["asyncio.unix_events.AbstractChildWatcher"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asyncio.unix_events.PidfdChildWatcher", "name": "PidfdChildWatcher", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "asyncio.unix_events.PidfdChildWatcher", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "asyncio.unix_events", "mro": ["asyncio.unix_events.PidfdChildWatcher", "asyncio.unix_events.AbstractChildWatcher", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.unix_events.PidfdChildWatcher.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.PidfdChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.PidfdChildWatcher", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__enter__ of PidfdChildWatcher", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.PidfdChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.PidfdChildWatcher", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.PidfdChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.PidfdChildWatcher", "values": [], "variance": 0}]}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.PidfdChildWatcher.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["asyncio.unix_events.PidfdChildWatcher", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__exit__ of PidfdChildWatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_child_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2], "arg_names": ["self", "pid", "callback", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.PidfdChildWatcher.add_child_handler", "name": "add_child_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2], "arg_names": ["self", "pid", "callback", "args"], "arg_types": ["asyncio.unix_events.PidfdChildWatcher", "builtins.int", {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": [null, null, null], "arg_types": ["builtins.int", "builtins.int", {".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events._Ts", "id": -1, "min_len": 0, "name": "_Ts", "namespace": "asyncio.unix_events.PidfdChildWatcher.add_child_handler", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events._Ts", "id": -1, "min_len": 0, "name": "_Ts", "namespace": "asyncio.unix_events.PidfdChildWatcher.add_child_handler", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_child_handler of PidfdChildWatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events._Ts", "id": -1, "min_len": 0, "name": "_Ts", "namespace": "asyncio.unix_events.PidfdChildWatcher.add_child_handler", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}]}}}, "attach_loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "loop"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.PidfdChildWatcher.attach_loop", "name": "attach_loop", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "loop"], "arg_types": ["asyncio.unix_events.PidfdChildWatcher", {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "attach_loop of PidfdChildWatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.PidfdChildWatcher.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["asyncio.unix_events.PidfdChildWatcher"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of PidfdChildWatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_active": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.PidfdChildWatcher.is_active", "name": "is_active", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["asyncio.unix_events.PidfdChildWatcher"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_active of PidfdChildWatcher", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_child_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pid"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.PidfdChildWatcher.remove_child_handler", "name": "remove_child_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pid"], "arg_types": ["asyncio.unix_events.PidfdChildWatcher", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "remove_child_handler of PidfdChildWatcher", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.PidfdChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.PidfdChildWatcher", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SafeChildWatcher": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["asyncio.unix_events.BaseChildWatcher"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asyncio.unix_events.SafeChildWatcher", "name": "SafeChildWatcher", "type_vars": []}, "deletable_attributes": [], "deprecated": "class asyncio.unix_events.SafeChildWatcher is deprecated: Deprecated as of Python 3.12; will be removed in Python 3.14", "flags": [], "fullname": "asyncio.unix_events.SafeChildWatcher", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "asyncio.unix_events", "mro": ["asyncio.unix_events.SafeChildWatcher", "asyncio.unix_events.BaseChildWatcher", "asyncio.unix_events.AbstractChildWatcher", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.unix_events.SafeChildWatcher.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.SafeChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.SafeChildWatcher", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__enter__ of SafeChildWatcher", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.SafeChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.SafeChildWatcher", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.SafeChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.SafeChildWatcher", "values": [], "variance": 0}]}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.SafeChildWatcher.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["asyncio.unix_events.SafeChildWatcher", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__exit__ of SafeChildWatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_child_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2], "arg_names": ["self", "pid", "callback", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.SafeChildWatcher.add_child_handler", "name": "add_child_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2], "arg_names": ["self", "pid", "callback", "args"], "arg_types": ["asyncio.unix_events.SafeChildWatcher", "builtins.int", {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": [null, null, null], "arg_types": ["builtins.int", "builtins.int", {".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events._Ts", "id": -1, "min_len": 0, "name": "_Ts", "namespace": "asyncio.unix_events.SafeChildWatcher.add_child_handler", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events._Ts", "id": -1, "min_len": 0, "name": "_Ts", "namespace": "asyncio.unix_events.SafeChildWatcher.add_child_handler", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_child_handler of SafeChildWatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events._Ts", "id": -1, "min_len": 0, "name": "_Ts", "namespace": "asyncio.unix_events.SafeChildWatcher.add_child_handler", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}]}}}, "remove_child_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pid"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.SafeChildWatcher.remove_child_handler", "name": "remove_child_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pid"], "arg_types": ["asyncio.unix_events.SafeChildWatcher", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "remove_child_handler of SafeChildWatcher", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.SafeChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.SafeChildWatcher", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SelectorEventLoop": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "asyncio.unix_events.SelectorEventLoop", "line": 190, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "asyncio.unix_events._UnixSelectorEventLoop"}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Server": {".class": "SymbolTableNode", "cross_ref": "asyncio.base_events.Server", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StrPath": {".class": "SymbolTableNode", "cross_ref": "_typeshed.StrPath", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ThreadedChildWatcher": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["asyncio.unix_events.AbstractChildWatcher"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asyncio.unix_events.ThreadedChildWatcher", "name": "ThreadedChildWatcher", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "asyncio.unix_events.ThreadedChildWatcher", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "asyncio.unix_events", "mro": ["asyncio.unix_events.ThreadedChildWatcher", "asyncio.unix_events.AbstractChildWatcher", "builtins.object"], "names": {".class": "SymbolTable", "__del__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.ThreadedChildWatcher.__del__", "name": "__del__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["asyncio.unix_events.ThreadedChildWatcher"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__del__ of ThreadedChildWatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asyncio.unix_events.ThreadedChildWatcher.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.ThreadedChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.ThreadedChildWatcher", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__enter__ of ThreadedChildWatcher", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.ThreadedChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.ThreadedChildWatcher", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.ThreadedChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.ThreadedChildWatcher", "values": [], "variance": 0}]}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.ThreadedChildWatcher.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["asyncio.unix_events.ThreadedChildWatcher", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__exit__ of ThreadedChildWatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_child_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2], "arg_names": ["self", "pid", "callback", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.ThreadedChildWatcher.add_child_handler", "name": "add_child_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2], "arg_names": ["self", "pid", "callback", "args"], "arg_types": ["asyncio.unix_events.ThreadedChildWatcher", "builtins.int", {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": [null, null, null], "arg_types": ["builtins.int", "builtins.int", {".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events._Ts", "id": -1, "min_len": 0, "name": "_Ts", "namespace": "asyncio.unix_events.ThreadedChildWatcher.add_child_handler", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnpackType", "type": {".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events._Ts", "id": -1, "min_len": 0, "name": "_Ts", "namespace": "asyncio.unix_events.ThreadedChildWatcher.add_child_handler", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_child_handler of ThreadedChildWatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarTupleType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events._Ts", "id": -1, "min_len": 0, "name": "_Ts", "namespace": "asyncio.unix_events.ThreadedChildWatcher.add_child_handler", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}]}}}, "attach_loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "loop"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.ThreadedChildWatcher.attach_loop", "name": "attach_loop", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "loop"], "arg_types": ["asyncio.unix_events.ThreadedChildWatcher", {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "attach_loop of ThreadedChildWatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.ThreadedChildWatcher.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["asyncio.unix_events.ThreadedChildWatcher"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of Threaded<PERSON><PERSON>dWatcher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_active": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.ThreadedChildWatcher.is_active", "name": "is_active", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["asyncio.unix_events.ThreadedChildWatcher"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_active of ThreadedChildWatcher", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_child_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pid"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asyncio.unix_events.ThreadedChildWatcher.remove_child_handler", "name": "remove_child_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pid"], "arg_types": ["asyncio.unix_events.ThreadedChildWatcher", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "remove_child_handler of ThreadedChildWatcher", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events.ThreadedChildWatcher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events.ThreadedChildWatcher", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeVarTuple": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeVarTuple", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Unpack": {".class": "SymbolTableNode", "cross_ref": "typing.Unpack", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ProtocolFactory": {".class": "SymbolTableNode", "cross_ref": "asyncio.base_events._ProtocolFactory", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_SSLContext": {".class": "SymbolTableNode", "cross_ref": "asyncio.base_events._SSLContext", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Ts": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarTupleExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events._Ts", "name": "_Ts", "tuple_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "variance": 0}}, "_UnixDefaultEventLoopPolicy": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["asyncio.events.BaseDefaultEventLoopPolicy"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asyncio.unix_events._UnixDefaultEventLoopPolicy", "name": "_UnixDefaultEventLoopPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "asyncio.unix_events._UnixDefaultEventLoopPolicy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "asyncio.unix_events", "mro": ["asyncio.unix_events._UnixDefaultEventLoopPolicy", "asyncio.events.BaseDefaultEventLoopPolicy", "asyncio.events.AbstractEventLoopPolicy", "builtins.object"], "names": {".class": "SymbolTable", "get_child_watcher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": "function asyncio.unix_events._UnixDefaultEventLoopPolicy.get_child_watcher is deprecated: Deprecated as of Python 3.12; will be removed in Python 3.14", "flags": ["is_decorated", "is_trivial_self"], "fullname": "asyncio.unix_events._UnixDefaultEventLoopPolicy.get_child_watcher", "name": "get_child_watcher", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["asyncio.unix_events._UnixDefaultEventLoopPolicy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_child_watcher of _UnixDefaultEventLoopPolicy", "ret_type": "asyncio.unix_events.AbstractChildWatcher", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "asyncio.unix_events._UnixDefaultEventLoopPolicy.get_child_watcher", "name": "get_child_watcher", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["asyncio.unix_events._UnixDefaultEventLoopPolicy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_child_watcher of _UnixDefaultEventLoopPolicy", "ret_type": "asyncio.unix_events.AbstractChildWatcher", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "set_child_watcher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "watcher"], "dataclass_transform_spec": null, "deprecated": "function asyncio.unix_events._UnixDefaultEventLoopPolicy.set_child_watcher is deprecated: Deprecated as of Python 3.12; will be removed in Python 3.14", "flags": ["is_decorated", "is_trivial_self"], "fullname": "asyncio.unix_events._UnixDefaultEventLoopPolicy.set_child_watcher", "name": "set_child_watcher", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "watcher"], "arg_types": ["asyncio.unix_events._UnixDefaultEventLoopPolicy", {".class": "UnionType", "items": ["asyncio.unix_events.AbstractChildWatcher", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_child_watcher of _UnixDefaultEventLoopPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "asyncio.unix_events._UnixDefaultEventLoopPolicy.set_child_watcher", "name": "set_child_watcher", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "watcher"], "arg_types": ["asyncio.unix_events._UnixDefaultEventLoopPolicy", {".class": "UnionType", "items": ["asyncio.unix_events.AbstractChildWatcher", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_child_watcher of _UnixDefaultEventLoopPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events._UnixDefaultEventLoopPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events._UnixDefaultEventLoopPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_UnixSelectorEventLoop": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["asyncio.selector_events.BaseSelectorEventLoop"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asyncio.unix_events._UnixSelectorEventLoop", "name": "_UnixSelectorEventLoop", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "asyncio.unix_events._UnixSelectorEventLoop", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "asyncio.unix_events", "mro": ["asyncio.unix_events._UnixSelectorEventLoop", "asyncio.selector_events.BaseSelectorEventLoop", "asyncio.base_events.BaseEventLoop", "asyncio.events.AbstractEventLoop", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asyncio.unix_events._UnixSelectorEventLoop.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asyncio.unix_events._UnixSelectorEventLoop", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "asyncio.unix_events.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.unix_events.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.unix_events.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.unix_events.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.unix_events.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.unix_events.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asyncio.unix_events.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef", "module_hidden": true, "module_public": false}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.deprecated", "kind": "Gdef", "module_hidden": true, "module_public": false}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket.socket", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/mypy/typeshed/stdlib/asyncio/unix_events.pyi"}