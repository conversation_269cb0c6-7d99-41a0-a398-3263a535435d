{"data_mtime": 1751559222, "dep_lines": [66, 66, 66, 66, 66, 67, 68, 69, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 104, 451, 542, 5, 44, 55, 56, 58, 59, 60, 61, 62, 63, 64, 65, 66, 101, 102, 103, 895, 1896, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 23, 24, 25, 26, 39, 41, 43, 44, 55, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 20, 20, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 25, 25, 25, 20, 20, 5, 20, 10, 10, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._decorators", "pydantic._internal._discriminated_union", "pydantic._internal._known_annotated_metadata", "pydantic._internal._repr", "pydantic._internal._typing_extra", "pydantic._internal._config", "pydantic._internal._core_metadata", "pydantic._internal._core_utils", "pydantic._internal._docs_extraction", "pydantic._internal._fields", "pydantic._internal._forward_ref", "pydantic._internal._generics", "pydantic._internal._import_utils", "pydantic._internal._mock_val_ser", "pydantic._internal._namespace_utils", "pydantic._internal._schema_gather", "pydantic._internal._schema_generation_shared", "pydantic._internal._utils", "pydantic._internal._dataclasses", "pydantic._internal._validators", "pydantic._internal._serializers", "collections.abc", "pydantic_core.core_schema", "typing_inspection.typing_objects", "typing_inspection.introspection", "pydantic.aliases", "pydantic.annotated_handlers", "pydantic.config", "pydantic.errors", "pydantic.functional_validators", "pydantic.json_schema", "pydantic.version", "pydantic.warnings", "pydantic._internal", "pydantic.fields", "pydantic.main", "pydantic.types", "pydantic.v1", "pydantic.dataclasses", "__future__", "collections", "dataclasses", "datetime", "inspect", "os", "pathlib", "re", "sys", "typing", "warnings", "contextlib", "copy", "decimal", "enum", "fractions", "functools", "ipaddress", "itertools", "operator", "types", "uuid", "zoneinfo", "typing_extensions", "pydantic_core", "typing_inspection", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "_warnings", "abc", "pydantic._internal._model_construction", "pydantic_core._pydantic_core"], "hash": "0dab017362cddc4c4acbc1b437928f24fd8f9b59", "id": "pydantic._internal._generate_schema", "ignore_all": true, "interface_hash": "3e4046b09080e84c997b2800b6572c7724ff135d", "mtime": 1747044682, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/pydantic/_internal/_generate_schema.py", "plugin_data": null, "size": 132136, "suppressed": [], "version_id": "1.16.1"}