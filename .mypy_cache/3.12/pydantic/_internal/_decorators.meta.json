{"data_mtime": 1751559222, "dep_lines": [18, 19, 20, 21, 22, 7, 14, 17, 25, 26, 3, 5, 6, 8, 9, 10, 11, 12, 14, 15, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 20, 25, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._core_utils", "pydantic._internal._internal_dataclass", "pydantic._internal._namespace_utils", "pydantic._internal._typing_extra", "pydantic._internal._utils", "collections.abc", "pydantic_core.core_schema", "pydantic.errors", "pydantic.fields", "pydantic.functional_validators", "__future__", "types", "collections", "dataclasses", "functools", "inspect", "itertools", "typing", "pydantic_core", "typing_extensions", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "pydantic_core._pydantic_core"], "hash": "0a9221d4acddedf2da8705ab542074dc3f5d07b6", "id": "pydantic._internal._decorators", "ignore_all": true, "interface_hash": "ac68cfb67f20648c6b6e64739c6896d24bd53c96", "mtime": 1747044682, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/pydantic/_internal/_decorators.py", "plugin_data": null, "size": 32638, "suppressed": [], "version_id": "1.16.1"}