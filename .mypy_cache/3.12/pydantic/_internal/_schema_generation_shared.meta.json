{"data_mtime": 1751559222, "dep_lines": [13, 14, 15, 7, 9, 12, 3, 5, 7, 1, 1, 1], "dep_prios": [25, 25, 25, 10, 5, 25, 5, 5, 20, 5, 30, 30], "dependencies": ["pydantic._internal._core_utils", "pydantic._internal._generate_schema", "pydantic._internal._namespace_utils", "pydantic_core.core_schema", "pydantic.annotated_handlers", "pydantic.json_schema", "__future__", "typing", "pydantic_core", "builtins", "_frozen_importlib", "abc"], "hash": "8c4d813e2a8cef18b1bf2f398db489a9a6593890", "id": "pydantic._internal._schema_generation_shared", "ignore_all": true, "interface_hash": "51bdecd2559fbc4f47ef39375fb8b8cee0fa4a5a", "mtime": 1747044682, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/pydantic/_internal/_schema_generation_shared.py", "plugin_data": null, "size": 4842, "suppressed": [], "version_id": "1.16.1"}