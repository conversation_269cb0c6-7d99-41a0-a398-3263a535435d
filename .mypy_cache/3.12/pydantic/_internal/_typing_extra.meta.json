{"data_mtime": 1751559222, "dep_lines": [20, 5, 15, 16, 18, 3, 5, 6, 7, 8, 9, 10, 13, 15, 30, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 20, 10, 10, 5, 5, 5, 5, 20, 25, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._namespace_utils", "collections.abc", "typing_inspection.typing_objects", "typing_inspection.introspection", "pydantic.version", "__future__", "collections", "re", "sys", "types", "typing", "functools", "typing_extensions", "typing_inspection", "pydantic", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "pydantic._internal._model_construction", "pydantic.main"], "hash": "ea16351f0d6745ce77cfdd97a4dbc8f93f6486ee", "id": "pydantic._internal._typing_extra", "ignore_all": true, "interface_hash": "dc4f8f1e45487c9e4de4f70b6b67ac256d29aacf", "mtime": 1747044682, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/pydantic/_internal/_typing_extra.py", "plugin_data": null, "size": 28216, "suppressed": [], "version_id": "1.16.1"}