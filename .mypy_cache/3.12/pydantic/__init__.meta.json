{"data_mtime": 1751559222, "dep_lines": [62, 63, 64, 5, 6, 12, 20, 21, 22, 23, 24, 25, 26, 33, 44, 45, 46, 47, 48, 49, 50, 65, 1, 2, 3, 11, 1, 1, 1, 1], "dep_prios": [25, 25, 25, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 10, 5, 5, 25, 5, 30, 30, 30], "dependencies": ["pydantic.deprecated.class_validators", "pydantic.deprecated.config", "pydantic.deprecated.tools", "pydantic._migration", "pydantic.version", "pydantic_core.core_schema", "pydantic.dataclasses", "pydantic.aliases", "pydantic.annotated_handlers", "pydantic.config", "pydantic.errors", "pydantic.fields", "pydantic.functional_serializers", "pydantic.functional_validators", "pydantic.json_schema", "pydantic.main", "pydantic.networks", "pydantic.type_adapter", "pydantic.types", "pydantic.validate_call_decorator", "pydantic.warnings", "pydantic.root_model", "typing", "importlib", "warnings", "pydantic_core", "builtins", "_frozen_importlib", "abc", "pydantic_core._pydantic_core"], "hash": "bca5b205a20e0a963351f04bf8f3a69c2f9a3d47", "id": "pydantic", "ignore_all": true, "interface_hash": "d26cf6a0927f33874f227c33ea67fcaf41c26c7b", "mtime": 1747044682, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/pydantic/__init__.py", "plugin_data": null, "size": 15395, "suppressed": [], "version_id": "1.16.1"}