{"data_mtime": 1751559222, "dep_lines": [26, 26, 9, 13, 24, 26, 27, 28, 29, 30, 3, 5, 6, 8, 10, 11, 13, 22, 33, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 10, 5, 20, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._repr", "pydantic._internal._schema_generation_shared", "importlib.metadata", "pydantic_core.core_schema", "pydantic.errors", "pydantic._internal", "pydantic._migration", "pydantic.annotated_handlers", "pydantic.json_schema", "pydantic.type_adapter", "__future__", "dataclasses", "re", "functools", "ipaddress", "typing", "pydantic_core", "typing_extensions", "email_validator", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "pydantic_core._pydantic_core", "types"], "hash": "0144005a67236799a563c4b002b4a413df5dedf2", "id": "pydantic.networks", "ignore_all": true, "interface_hash": "977cc3cb23695284c46c2c90186672d32021b3b2", "mtime": 1747044682, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": false}, "path": "/home/<USER>/Desktop/ISMS-Full stack/isms-backend/venv/lib/python3.13/site-packages/pydantic/networks.py", "plugin_data": null, "size": 41446, "suppressed": [], "version_id": "1.16.1"}