# ISMS AWS Deployment Guide

This guide provides step-by-step instructions for deploying the ISMS FastAPI application on Amazon Web Services (AWS).

## 🏗️ Architecture Overview

### Recommended AWS Architecture

```
Internet Gateway
    ↓
Application Load Balancer (ALB)
    ↓
ECS Fargate Service (Auto Scaling)
    ↓
RDS PostgreSQL (Multi-AZ)
```

### AWS Services Used

- **ECS Fargate**: Container orchestration
- **RDS PostgreSQL**: Managed database
- **Application Load Balancer**: Load balancing and SSL termination
- **ECR**: Container registry
- **CloudWatch**: Logging and monitoring
- **Route 53**: DNS management
- **Certificate Manager**: SSL certificates
- **VPC**: Network isolation
- **IAM**: Access management

## 🚀 Deployment Steps

### 1. Prerequisites

- AWS CLI configured with appropriate permissions
- Docker installed locally
- Domain name configured in Route 53

### 2. Create VPC and Networking

```bash
# Create VPC
aws ec2 create-vpc --cidr-block 10.0.0.0/16 --tag-specifications 'ResourceType=vpc,Tags=[{Key=Name,Value=isms-vpc}]'

# Create subnets (public and private)
aws ec2 create-subnet --vpc-id vpc-xxxxxxxx --cidr-block 10.0.1.0/24 --availability-zone us-east-1a
aws ec2 create-subnet --vpc-id vpc-xxxxxxxx --cidr-block 10.0.2.0/24 --availability-zone us-east-1b
aws ec2 create-subnet --vpc-id vpc-xxxxxxxx --cidr-block 10.0.3.0/24 --availability-zone us-east-1a
aws ec2 create-subnet --vpc-id vpc-xxxxxxxx --cidr-block ********/24 --availability-zone us-east-1b

# Create Internet Gateway
aws ec2 create-internet-gateway --tag-specifications 'ResourceType=internet-gateway,Tags=[{Key=Name,Value=isms-igw}]'
aws ec2 attach-internet-gateway --vpc-id vpc-xxxxxxxx --internet-gateway-id igw-xxxxxxxx
```

### 3. Set Up RDS PostgreSQL

```bash
# Create DB subnet group
aws rds create-db-subnet-group \
    --db-subnet-group-name isms-db-subnet-group \
    --db-subnet-group-description "ISMS Database Subnet Group" \
    --subnet-ids subnet-xxxxxxxx subnet-yyyyyyyy

# Create security group for RDS
aws ec2 create-security-group \
    --group-name isms-db-sg \
    --description "ISMS Database Security Group" \
    --vpc-id vpc-xxxxxxxx

# Allow PostgreSQL access from ECS
aws ec2 authorize-security-group-ingress \
    --group-id sg-xxxxxxxx \
    --protocol tcp \
    --port 5432 \
    --source-group sg-yyyyyyyy

# Create RDS instance
aws rds create-db-instance \
    --db-instance-identifier isms-production-db \
    --db-instance-class db.t3.micro \
    --engine postgres \
    --engine-version 15.4 \
    --master-username isms_admin \
    --master-user-password "YourSecurePassword123!" \
    --allocated-storage 20 \
    --storage-type gp2 \
    --vpc-security-group-ids sg-xxxxxxxx \
    --db-subnet-group-name isms-db-subnet-group \
    --backup-retention-period 7 \
    --multi-az \
    --storage-encrypted \
    --deletion-protection
```

### 4. Create ECR Repository

```bash
# Create ECR repository
aws ecr create-repository --repository-name isms-backend

# Get login token
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 123456789012.dkr.ecr.us-east-1.amazonaws.com

# Build and push image
docker build -t isms-backend .
docker tag isms-backend:latest 123456789012.dkr.ecr.us-east-1.amazonaws.com/isms-backend:latest
docker push 123456789012.dkr.ecr.us-east-1.amazonaws.com/isms-backend:latest
```

### 5. Create ECS Cluster and Service

#### Task Definition (task-definition.json)

```json
{
  "family": "isms-backend",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "executionRoleArn": "arn:aws:iam::123456789012:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::123456789012:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "isms-backend",
      "image": "123456789012.dkr.ecr.us-east-1.amazonaws.com/isms-backend:latest",
      "portMappings": [
        {
          "containerPort": 8000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "ENV_MODE",
          "value": "production"
        },
        {
          "name": "POSTGRES_SERVER",
          "value": "isms-production-db.xxxxxxxx.us-east-1.rds.amazonaws.com"
        },
        {
          "name": "POSTGRES_DB",
          "value": "isms_production"
        }
      ],
      "secrets": [
        {
          "name": "POSTGRES_USER",
          "valueFrom": "arn:aws:ssm:us-east-1:123456789012:parameter/isms/postgres/user"
        },
        {
          "name": "POSTGRES_PASSWORD",
          "valueFrom": "arn:aws:ssm:us-east-1:123456789012:parameter/isms/postgres/password"
        },
        {
          "name": "SECRET_KEY",
          "valueFrom": "arn:aws:ssm:us-east-1:123456789012:parameter/isms/secret-key"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/isms-backend",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "healthCheck": {
        "command": ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"],
        "interval": 30,
        "timeout": 5,
        "retries": 3,
        "startPeriod": 60
      }
    }
  ]
}
```

#### Create ECS Resources

```bash
# Create CloudWatch log group
aws logs create-log-group --log-group-name /ecs/isms-backend

# Create ECS cluster
aws ecs create-cluster --cluster-name isms-production

# Register task definition
aws ecs register-task-definition --cli-input-json file://task-definition.json

# Create security group for ECS
aws ec2 create-security-group \
    --group-name isms-ecs-sg \
    --description "ISMS ECS Security Group" \
    --vpc-id vpc-xxxxxxxx

# Allow HTTP/HTTPS from ALB
aws ec2 authorize-security-group-ingress \
    --group-id sg-xxxxxxxx \
    --protocol tcp \
    --port 8000 \
    --source-group sg-yyyyyyyy
```

### 6. Create Application Load Balancer

```bash
# Create security group for ALB
aws ec2 create-security-group \
    --group-name isms-alb-sg \
    --description "ISMS ALB Security Group" \
    --vpc-id vpc-xxxxxxxx

# Allow HTTP and HTTPS
aws ec2 authorize-security-group-ingress \
    --group-id sg-xxxxxxxx \
    --protocol tcp \
    --port 80 \
    --cidr 0.0.0.0/0

aws ec2 authorize-security-group-ingress \
    --group-id sg-xxxxxxxx \
    --protocol tcp \
    --port 443 \
    --cidr 0.0.0.0/0

# Create ALB
aws elbv2 create-load-balancer \
    --name isms-alb \
    --subnets subnet-xxxxxxxx subnet-yyyyyyyy \
    --security-groups sg-xxxxxxxx

# Create target group
aws elbv2 create-target-group \
    --name isms-targets \
    --protocol HTTP \
    --port 8000 \
    --vpc-id vpc-xxxxxxxx \
    --target-type ip \
    --health-check-path /health \
    --health-check-interval-seconds 30 \
    --health-check-timeout-seconds 5 \
    --healthy-threshold-count 2 \
    --unhealthy-threshold-count 3

# Create listener (HTTP -> HTTPS redirect)
aws elbv2 create-listener \
    --load-balancer-arn arn:aws:elasticloadbalancing:us-east-1:123456789012:loadbalancer/app/isms-alb/xxxxxxxx \
    --protocol HTTP \
    --port 80 \
    --default-actions Type=redirect,RedirectConfig='{Protocol=HTTPS,Port=443,StatusCode=HTTP_301}'

# Create HTTPS listener (requires SSL certificate)
aws elbv2 create-listener \
    --load-balancer-arn arn:aws:elasticloadbalancing:us-east-1:123456789012:loadbalancer/app/isms-alb/xxxxxxxx \
    --protocol HTTPS \
    --port 443 \
    --certificates CertificateArn=arn:aws:acm:us-east-1:123456789012:certificate/xxxxxxxx \
    --default-actions Type=forward,TargetGroupArn=arn:aws:elasticloadbalancing:us-east-1:123456789012:targetgroup/isms-targets/xxxxxxxx
```

### 7. Create ECS Service

```bash
# Create ECS service
aws ecs create-service \
    --cluster isms-production \
    --service-name isms-backend-service \
    --task-definition isms-backend:1 \
    --desired-count 2 \
    --launch-type FARGATE \
    --network-configuration "awsvpcConfiguration={subnets=[subnet-xxxxxxxx,subnet-yyyyyyyy],securityGroups=[sg-xxxxxxxx],assignPublicIp=ENABLED}" \
    --load-balancers targetGroupArn=arn:aws:elasticloadbalancing:us-east-1:123456789012:targetgroup/isms-targets/xxxxxxxx,containerName=isms-backend,containerPort=8000
```

## 🔧 Configuration Management

### AWS Systems Manager Parameter Store

Store sensitive configuration in Parameter Store:

```bash
# Database credentials
aws ssm put-parameter \
    --name "/isms/postgres/user" \
    --value "isms_admin" \
    --type "SecureString"

aws ssm put-parameter \
    --name "/isms/postgres/password" \
    --value "YourSecurePassword123!" \
    --type "SecureString"

# Application secrets
aws ssm put-parameter \
    --name "/isms/secret-key" \
    --value "your-super-secure-secret-key-min-32-chars" \
    --type "SecureString"
```

## 📊 Monitoring and Logging

### CloudWatch Configuration

1. **Application Logs**: Automatically sent to CloudWatch Logs
2. **Metrics**: ECS and RDS metrics available in CloudWatch
3. **Alarms**: Set up alarms for critical metrics

### Recommended Alarms

```bash
# High CPU utilization
aws cloudwatch put-metric-alarm \
    --alarm-name "ISMS-HighCPU" \
    --alarm-description "ISMS high CPU utilization" \
    --metric-name CPUUtilization \
    --namespace AWS/ECS \
    --statistic Average \
    --period 300 \
    --threshold 80 \
    --comparison-operator GreaterThanThreshold \
    --evaluation-periods 2

# Database connections
aws cloudwatch put-metric-alarm \
    --alarm-name "ISMS-DBConnections" \
    --alarm-description "ISMS high database connections" \
    --metric-name DatabaseConnections \
    --namespace AWS/RDS \
    --statistic Average \
    --period 300 \
    --threshold 80 \
    --comparison-operator GreaterThanThreshold \
    --evaluation-periods 2
```

## 🔐 Security Best Practices

### IAM Roles and Policies

1. **ECS Task Execution Role**: Minimal permissions for ECS
2. **ECS Task Role**: Application-specific permissions
3. **RDS Security**: VPC isolation and encryption
4. **Secrets Management**: Use Parameter Store or Secrets Manager

### Network Security

1. **VPC**: Isolated network environment
2. **Security Groups**: Restrictive ingress rules
3. **Private Subnets**: Database in private subnets
4. **WAF**: Optional Web Application Firewall

## 💰 Cost Optimization

### Recommendations

1. **Right-sizing**: Start with smaller instances and scale up
2. **Reserved Instances**: For predictable workloads
3. **Spot Instances**: For development environments
4. **Auto Scaling**: Scale based on demand
5. **CloudWatch**: Monitor costs and usage

### Estimated Monthly Costs

- **ECS Fargate**: $30-50 (2 tasks, 0.5 vCPU, 1GB RAM)
- **RDS PostgreSQL**: $15-25 (db.t3.micro)
- **ALB**: $20-25
- **Data Transfer**: $5-10
- **Total**: ~$70-110/month

## 🔄 CI/CD Integration

### GitHub Actions for AWS

Update `.github/workflows/ci-cd.yml` to include AWS deployment:

```yaml
deploy-aws-production:
  name: Deploy to AWS Production
  runs-on: ubuntu-latest
  needs: [test, docker-build, security-scan]
  if: github.ref == 'refs/heads/main' && github.event_name == 'push'
  environment: aws-production

  steps:
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: Login to Amazon ECR
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build and push to ECR
      run: |
        docker build -t isms-backend .
        docker tag isms-backend:latest $ECR_REGISTRY/isms-backend:$GITHUB_SHA
        docker push $ECR_REGISTRY/isms-backend:$GITHUB_SHA

    - name: Update ECS service
      run: |
        aws ecs update-service \
          --cluster isms-production \
          --service isms-backend-service \
          --force-new-deployment
```

## 🛠️ Maintenance

### Regular Tasks

1. **Database Backups**: Automated via RDS
2. **Security Updates**: Update container images regularly
3. **Monitoring**: Review CloudWatch metrics and logs
4. **Cost Review**: Monthly cost analysis
5. **Performance Tuning**: Optimize based on metrics

### Scaling

```bash
# Scale ECS service
aws ecs update-service \
    --cluster isms-production \
    --service isms-backend-service \
    --desired-count 4

# Scale RDS (requires downtime)
aws rds modify-db-instance \
    --db-instance-identifier isms-production-db \
    --db-instance-class db.t3.small \
    --apply-immediately
```

---

**Related Guides:**
- [Main Deployment Guide](DEPLOYMENT.md)
- [Azure Deployment Guide](AZURE_DEPLOYMENT.md)
- [Docker Guide](DOCKER.md)
