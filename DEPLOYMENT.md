# ISMS Production Deployment Guide

This guide provides comprehensive instructions for deploying the Integrated Supermarket Management System (ISMS) to production environments.

## 🚀 Quick Start (Dokploy)

### Prerequisites

1. **External PostgreSQL Database** (required)
   - PostgreSQL 13+ with async support
   - Database created: `isms_production`
   - User with full permissions on the database

2. **Dokploy Environment**
   - Docker and Docker Compose support
   - Traefik reverse proxy configured
   - SSL certificates (Let's Encrypt recommended)

### Deployment Steps

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd isms-backend
   ```

2. **Configure environment variables**
   ```bash
   cp .env.production .env
   # Edit .env with your actual values
   ```

3. **Set required environment variables in Dokploy:**
   ```bash
   # Database Configuration
   POSTGRES_SERVER=your-postgres-server.com
   POSTGRES_USER=your_postgres_user
   POSTGRES_PASSWORD=your_secure_postgres_password
   POSTGRES_DB=isms_production
   
   # Security
   SECRET_KEY=your-super-secure-secret-key-min-32-chars
   
   # Domain
   BACKEND_CORS_ORIGINS=["https://api.isms.helevon.org","https://isms.helevon.org"]
   ```

4. **Deploy with Dokploy**
   - Use the provided `docker-compose.yml`
   - Configure webhook: `https://helevon-vps.helevon.org/api/deploy/compose/DmBlOGTL2eE6i2LxjDP5u`
   - Deploy to `api.isms.helevon.org`

5. **Initialize the database**
   ```bash
   # After deployment, run inside the container:
   python manage.py init_db
   python manage.py create_superuser
   ```

## 📋 Environment Configuration

### Production Environment Variables

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `ENV_MODE` | Yes | Environment mode | `production` |
| `POSTGRES_SERVER` | Yes | PostgreSQL server host | `db.example.com` |
| `POSTGRES_USER` | Yes | Database username | `isms_user` |
| `POSTGRES_PASSWORD` | Yes | Database password | `secure_password` |
| `POSTGRES_DB` | Yes | Database name | `isms_production` |
| `SECRET_KEY` | Yes | JWT secret key (32+ chars) | `your-secret-key` |
| `BACKEND_CORS_ORIGINS` | Yes | Allowed CORS origins | `["https://api.isms.helevon.org"]` |
| `LOG_LEVEL` | No | Logging level | `INFO` |
| `WORKERS` | No | Gunicorn workers | `4` |
| `RATE_LIMIT_REQUESTS` | No | Rate limit per minute | `100` |

### Security Configuration

```bash
# Security Headers
SECURE_HEADERS=true
HTTPS_ONLY=true

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Monitoring
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true
```

## 🔧 Database Setup

### PostgreSQL Configuration

1. **Create database and user:**
   ```sql
   CREATE DATABASE isms_production;
   CREATE USER isms_user WITH PASSWORD 'secure_password';
   GRANT ALL PRIVILEGES ON DATABASE isms_production TO isms_user;
   GRANT ALL ON SCHEMA public TO isms_user;
   ```

2. **Configure connection pooling:**
   ```bash
   POOL_SIZE=20
   MAX_OVERFLOW=30
   MAX_CONNECTIONS=100
   ```

3. **Initialize tables:**
   ```bash
   python manage.py init_db
   ```

## 🐳 Docker Configuration

### Dockerfile Features

- **Multi-stage build** for optimized image size
- **Non-root user** for security
- **Health checks** for container monitoring
- **Production-optimized** Python environment

### Docker Compose Features

- **External PostgreSQL** connection
- **Traefik labels** for reverse proxy
- **Health checks** and restart policies
- **Volume mounting** for logs
- **Environment variable** injection

## 🔍 Health Checks

The application provides multiple health check endpoints:

- `GET /` - Basic health check
- `GET /health` - Simple health status
- `GET /health/detailed` - Comprehensive health information
- `GET /health/readiness` - Kubernetes-style readiness probe
- `GET /health/liveness` - Kubernetes-style liveness probe
- `GET /metrics` - Basic metrics for monitoring

## 🚨 Monitoring and Logging

### Application Logs

Logs are written to `/app/logs/isms.log` inside the container and mounted to `./logs/` on the host.

### Log Levels

- **DEBUG**: Development debugging
- **INFO**: General information (default)
- **WARNING**: Warning messages
- **ERROR**: Error conditions
- **CRITICAL**: Critical errors

### Metrics

Basic metrics available at `/metrics` endpoint:
- User count
- Memory usage
- CPU usage
- Disk usage

## 🔐 Security Features

### Production Security

1. **Security Headers**
   - X-Content-Type-Options
   - X-Frame-Options
   - X-XSS-Protection
   - Strict-Transport-Security
   - Content-Security-Policy

2. **Rate Limiting**
   - Configurable requests per minute
   - Sliding window algorithm
   - IP-based limiting

3. **HTTPS Enforcement**
   - Redirect HTTP to HTTPS
   - Secure cookie settings
   - HSTS headers

## 🔄 CI/CD Pipeline

### GitHub Actions Workflow

The CI/CD pipeline includes:

1. **Code Quality Checks**
   - Black formatting
   - isort import sorting
   - flake8 linting
   - mypy type checking

2. **Security Scanning**
   - bandit security analysis
   - safety dependency check
   - Trivy container scanning

3. **Comprehensive Testing**
   - Unit tests
   - Integration tests
   - API tests
   - Coverage reporting

4. **Automated Deployment**
   - Staging deployment on `staging` branch
   - Production deployment on `main` branch
   - Webhook integration with Dokploy

### Deployment Triggers

- **Staging**: Push to `staging` branch
- **Production**: Push to `main` branch
- **Manual**: Webhook URL trigger

## 🛠️ Management Commands

### Database Management

```bash
# Initialize database
python manage.py init_db

# Check database status
python manage.py check_db

# Reset database (⚠️ DESTRUCTIVE)
python manage.py reset_db

# Backup database (PostgreSQL only)
python manage.py backup_db --output-file backup.sql

# Health check
python manage.py health_check
```

### User Management

```bash
# Create superuser
python manage.py create_superuser

# Create regular user
python manage.py create_user
```

### Server Management

```bash
# Run development server
python manage.py runserver

# Run production server (use Docker instead)
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker
```

## 🚀 Performance Optimization

### Production Settings

1. **Worker Configuration**
   - Gunicorn with Uvicorn workers
   - 4 workers (adjust based on CPU cores)
   - Async request handling

2. **Database Optimization**
   - Connection pooling
   - Async PostgreSQL driver (asyncpg)
   - Query optimization

3. **Caching**
   - Response compression
   - Static file caching
   - Database query optimization

## 🔧 Troubleshooting

### Common Issues

1. **Database Connection Errors**
   ```bash
   # Check database connectivity
   python manage.py check_db
   ```

2. **Permission Errors**
   ```bash
   # Ensure proper file permissions
   chown -R isms:isms /app
   ```

3. **Health Check Failures**
   ```bash
   # Check application health
   curl -f https://api.isms.helevon.org/health/detailed
   ```

### Log Analysis

```bash
# View application logs
docker logs <container-name>

# View specific log file
tail -f logs/isms.log
```

## 📞 Support

For deployment issues:
1. Check the logs first
2. Verify environment variables
3. Test database connectivity
4. Review security settings
5. Contact support if needed

---

**Next Steps:**
- [AWS Deployment Guide](AWS_DEPLOYMENT.md)
- [Azure Deployment Guide](AZURE_DEPLOYMENT.md)
- [Docker Guide](DOCKER.md)
