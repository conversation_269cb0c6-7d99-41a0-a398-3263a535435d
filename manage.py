#!/usr/bin/env python
import asyncio
import logging
import os
import sys
from typing import Optional

import typer
import uvicorn
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import async_session_factory, create_db_and_tables
from app.core.logging import setup_logging
from app.core.password import get_password_hash
from app.models.user import User, UserRole

# Set up logging
setup_logging()
logger = logging.getLogger("app.cli")

# Create Typer app
app = typer.Typer(help="ISMS management CLI")


@app.command()
def runserver(
    host: str = "127.0.0.1",
    port: int = 8000,
    reload: bool = True,
):
    """Run the development server."""
    logger.info(f"Starting development server at {host}:{port}")

    # Configure reload excludes to prevent watching log files and database files
    reload_excludes = [
        "*.log",
        "*.db",
        "*.sqlite*",
        "logs/*",
        "__pycache__/*",
        ".git/*",
        "venv/*",
        ".pytest_cache/*",
        "*.pyc",
        "debug_settings.py",  # Exclude our debug script
    ] if reload else None

    # Also specify reload dirs to only watch specific directories
    reload_dirs = ["app/"] if reload else None

    uvicorn.run(
        "app.main:app",
        host=host,
        port=port,
        reload=reload,
        reload_excludes=reload_excludes,
        reload_dirs=reload_dirs,
    )


@app.command()
def create_user(
    email: str = typer.Option(..., prompt=True),
    password: str = typer.Option(..., prompt=True, hide_input=True),
    full_name: str = typer.Option(..., prompt=True),
    role: UserRole = typer.Option(UserRole.CASHIER, case_sensitive=False),
    superuser: bool = typer.Option(False),
):
    """Create a new user."""
    async def _create_user():
        async with async_session_factory() as session:
            # Check if user exists
            user = await User.get_by_email(session, email=email)
            if user:
                logger.error(f"User with email {email} already exists")
                return
            
            # Create user
            user_data = {
                "email": email,
                "password": password,
                "full_name": full_name,
                "role": role,
                "is_superuser": superuser,
            }
            user = await User.create(session, obj_in=user_data)
            logger.info(f"User created: {user.email} (ID: {user.id})")
    
    asyncio.run(_create_user())


@app.command()
def init_db():
    """Initialize the database."""
    async def _init_db():
        await create_db_and_tables()
        logger.info("Database initialized")
    
    asyncio.run(_init_db())


@app.command()
def create_superuser():
    """Create a superuser (admin) account."""
    create_user(
        email=typer.prompt("Email"),
        password=typer.prompt("Password", hide_input=True),
        full_name=typer.prompt("Full Name"),
        role=UserRole.ADMIN,
        superuser=True,
    )


if __name__ == "__main__":
    app()
