version: '3.8'

services:
  isms-api:
    build:
      context: .
      target: production
    expose:
      - '8000'
    environment:
      # Application settings
      - ENV_MODE=production
      - PROJECT_NAME=${PROJECT_NAME:-Integrated Supermarket Management System (ISMS)}
      - VERSION=${VERSION:-0.1.0}
      - DESCRIPTION=${DESCRIPTION:-Backend API for the Integrated Supermarket Management System}
      
      # API configuration
      - API_V1_STR=${API_V1_STR:-/api/v1}
      
      # Database configuration (External PostgreSQL)
      - POSTGRES_SERVER=${POSTGRES_SERVER}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      
      # Security settings
      - SECRET_KEY=${SECRET_KEY}
      - ACCESS_TOKEN_EXPIRE_MINUTES=${ACCESS_TOKEN_EXPIRE_MINUTES:-11520}
      
      # CORS settings
      - BACKEND_CORS_ORIGINS=${BACKEND_CORS_ORIGINS:-["https://api.isms.helevon.org","https://isms.helevon.org"]}
      
      # Logging
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - PYTHONUNBUFFERED=1
    
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.isms-api.rule=Host(`api.isms.helevon.org`)"
      - "traefik.http.routers.isms-api.priority=1"
      - "traefik.http.services.isms-api.loadbalancer.server.port=8000"
      - "traefik.http.routers.isms-api.tls=true"
      - "traefik.http.routers.isms-api.tls.certresolver=letsencrypt"
    
    volumes:
      - ./logs:/app/logs
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s
    
    restart: unless-stopped
    
    networks:
      - dokploy-network

networks:
  dokploy-network:
    external: true
