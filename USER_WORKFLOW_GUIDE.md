# Integrated Supermarket Management System (ISMS) User Guide

This guide provides detailed workflows for different user roles in the Integrated Supermarket Management System (ISMS).

## Table of Contents

1. [Getting Started](#getting-started)
2. [Admin/Superuser Workflows](#adminsuperuser-workflows)
3. [Manager Workflows](#manager-workflows)
4. [Cashier Workflows](#cashier-workflows)
5. [Inventory Clerk Workflows](#inventory-clerk-workflows)
6. [Common Tasks](#common-tasks)

## Getting Started

### Installation and Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/isms-backend.git
   cd isms-backend
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Initialize the database:
   ```bash
   python manage.py init-db
   ```

4. Create a superuser account:
   ```bash
   python manage.py create-superuser
   ```

5. Start the development server:
   ```bash
   python manage.py runserver
   ```

### Authentication

All users must authenticate to access the system:

1. <PERSON>gin using your email and password:
   ```
   POST /api/v1/auth/login
   {
     "username": "<EMAIL>",
     "password": "your_password"
   }
   ```

2. Use the returned access token in the Authorization header for all subsequent requests:
   ```
   Authorization: Bearer {your_access_token}
   ```

## Admin/Superuser Workflows

Admins have full access to all system features and can manage users, roles, and permissions.

### User Management

#### Creating a New User

1. Create a user with a specific role:
   ```
   POST /api/v1/users/
   {
     "email": "<EMAIL>",
     "password": "securepassword",
     "full_name": "User Name",
     "role": "cashier"  // Options: admin, manager, cashier, inventory
   }
   ```

2. View all users:
   ```
   GET /api/v1/users/
   ```

3. View a specific user:
   ```
   GET /api/v1/users/{user_id}
   ```

4. Update a user:
   ```
   PUT /api/v1/users/{user_id}
   {
     "full_name": "Updated Name",
     "role": "manager"
   }
   ```

5. Delete a user:
   ```
   DELETE /api/v1/users/{user_id}
   ```

### Role and Permission Management

#### Viewing Roles and Permissions

1. View all roles:
   ```
   GET /api/v1/roles/
   ```

2. View all available permissions:
   ```
   GET /api/v1/roles/permissions
   ```

3. View permissions for a specific role:
   ```
   GET /api/v1/roles/{role_name}/permissions
   ```

#### Creating Custom Roles

1. Create a custom role with specific permissions:
   ```
   POST /api/v1/roles/custom
   {
     "role_name": "custom_role",
     "permissions": ["inventory:read", "inventory:create", "sales:read"]
   }
   ```

2. Update a custom role:
   ```
   PUT /api/v1/roles/custom/{role_name}
   {
     "permissions": ["inventory:read", "inventory:create", "sales:read", "sales:create"]
   }
   ```

3. Delete a custom role:
   ```
   DELETE /api/v1/roles/custom/{role_name}
   ```

### System Reports

1. Generate a sales report for a date range:
   ```
   POST /api/v1/reports/sales
   {
     "start_date": "2023-01-01T00:00:00Z",
     "end_date": "2023-01-31T23:59:59Z"
   }
   ```

2. Get daily sales for the last N days:
   ```
   GET /api/v1/reports/daily-sales?days=7
   ```

## Manager Workflows

Managers have access to most system features but cannot manage users or roles.

### Inventory Management

1. View all products:
   ```
   GET /api/v1/inventory/products/
   ```

2. View low stock products:
   ```
   GET /api/v1/inventory/products/low-stock
   ```

3. Add a new product:
   ```
   POST /api/v1/inventory/products/
   {
     "name": "Product Name",
     "description": "Product Description",
     "sku": "PROD001",
     "category": "grocery",
     "price": 9.99,
     "cost": 5.99,
     "quantity": 100,
     "reorder_level": 20,
     "supplier_id": 1
   }
   ```

4. Update a product:
   ```
   PUT /api/v1/inventory/products/{product_id}
   {
     "price": 10.99,
     "reorder_level": 25
   }
   ```

### Supplier Management

1. View all suppliers:
   ```
   GET /api/v1/inventory/suppliers/
   ```

2. Add a new supplier:
   ```
   POST /api/v1/inventory/suppliers/
   {
     "name": "Supplier Name",
     "contact_name": "Contact Person",
     "email": "<EMAIL>",
     "phone": "************",
     "address": "123 Supplier St"
   }
   ```

3. Update a supplier:
   ```
   PUT /api/v1/inventory/suppliers/{supplier_id}
   {
     "phone": "************"
   }
   ```

### Order Management

1. View all orders:
   ```
   GET /api/v1/sales/orders/
   ```

2. View a specific order:
   ```
   GET /api/v1/sales/orders/{order_id}
   ```

3. Refund an order:
   ```
   POST /api/v1/sales/orders/{order_id}/refund
   ```

## Cashier Workflows

Cashiers primarily handle sales transactions.

### Processing Sales

1. Create a new order:
   ```
   POST /api/v1/sales/orders/
   {
     "customer_name": "Customer Name",
     "payment_method": "cash",  // Options: cash, credit_card, debit_card
     "items": [
       {
         "product_id": 1,
         "quantity": 2
       },
       {
         "product_id": 3,
         "quantity": 1
       }
     ]
   }
   ```

2. Complete an order:
   ```
   POST /api/v1/sales/orders/{order_id}/complete
   ```

3. Cancel an order:
   ```
   POST /api/v1/sales/orders/{order_id}/cancel
   ```

4. View order history:
   ```
   GET /api/v1/sales/orders/
   ```

## Inventory Clerk Workflows

Inventory clerks manage stock levels and product information.

### Stock Management

1. Update product stock:
   ```
   POST /api/v1/inventory/products/{product_id}/stock
   {
     "quantity": 50,  // Positive to add, negative to remove
     "notes": "Restocking from supplier"
   }
   ```

2. View stock movement history:
   ```
   GET /api/v1/inventory/products/{product_id}/movements
   ```

3. Perform inventory count:
   ```
   PUT /api/v1/inventory/products/{product_id}
   {
     "quantity": 45,  // Actual count
     "notes": "Inventory adjustment after physical count"
   }
   ```

## Common Tasks

### Profile Management

1. View your profile:
   ```
   GET /api/v1/auth/me
   ```

2. Change your password:
   ```
   PUT /api/v1/users/me/password
   {
     "current_password": "old_password",
     "new_password": "new_password"
   }
   ```

### Product Search

1. Search for products:
   ```
   GET /api/v1/inventory/products/search?q=keyword
   ```

2. Filter products by category:
   ```
   GET /api/v1/inventory/products/?category=grocery
   ```

### Reporting Issues

If you encounter any issues while using the system, please contact the system administrator with the following information:

1. Your user role
2. The action you were trying to perform
3. Any error messages you received
4. The date and time of the issue

## Permission Reference

Here's a quick reference of the permissions available in the system:

| Permission | Description |
|------------|-------------|
| users:read | View user information |
| users:create | Create new users |
| users:update | Update user information |
| users:delete | Delete users |
| inventory:read | View inventory information |
| inventory:create | Add new products/suppliers |
| inventory:update | Update product/supplier information |
| inventory:delete | Delete products/suppliers |
| sales:read | View sales information |
| sales:create | Create new sales |
| sales:update | Update sales information |
| sales:delete | Cancel/refund sales |
| reports:read | View reports |

## Default Role Permissions

| Role | Permissions |
|------|-------------|
| admin | All permissions |
| manager | All except user management |
| cashier | sales:read, sales:create, inventory:read |
| inventory | inventory:read, inventory:create, inventory:update |
