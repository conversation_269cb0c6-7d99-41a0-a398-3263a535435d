# ISMS Azure Deployment Guide

This guide provides step-by-step instructions for deploying the ISMS FastAPI application on Microsoft Azure.

## 🏗️ Architecture Overview

### Recommended Azure Architecture

```
Azure Front Door / Application Gateway
    ↓
Container Instances / App Service
    ↓
Azure Database for PostgreSQL
```

### Azure Services Used

- **Container Instances**: Serverless container hosting
- **App Service**: Alternative PaaS option
- **Azure Database for PostgreSQL**: Managed database
- **Application Gateway**: Load balancing and SSL termination
- **Container Registry**: Container image storage
- **Key Vault**: Secrets management
- **Monitor**: Logging and monitoring
- **DNS Zone**: DNS management
- **Virtual Network**: Network isolation

## 🚀 Deployment Options

### Option 1: Azure Container Instances (Recommended)

Simple, serverless container deployment.

### Option 2: Azure App Service

Platform-as-a-Service with built-in scaling.

### Option 3: Azure Kubernetes Service (AKS)

For complex, multi-service deployments.

## 📋 Prerequisites

- Azure CLI installed and configured
- Docker installed locally
- Azure subscription with appropriate permissions
- Domain name (optional, can use Azure-provided domains)

## 🚀 Deployment Steps (Container Instances)

### 1. Create Resource Group

```bash
# Create resource group
az group create \
    --name isms-production-rg \
    --location eastus
```

### 2. Create Azure Database for PostgreSQL

```bash
# Create PostgreSQL server
az postgres flexible-server create \
    --resource-group isms-production-rg \
    --name isms-postgres-server \
    --location eastus \
    --admin-user isms_admin \
    --admin-password "YourSecurePassword123!" \
    --sku-name Standard_B1ms \
    --tier Burstable \
    --version 15 \
    --storage-size 32 \
    --backup-retention 7 \
    --geo-redundant-backup Disabled

# Create database
az postgres flexible-server db create \
    --resource-group isms-production-rg \
    --server-name isms-postgres-server \
    --database-name isms_production

# Configure firewall (allow Azure services)
az postgres flexible-server firewall-rule create \
    --resource-group isms-production-rg \
    --name isms-postgres-server \
    --rule-name AllowAzureServices \
    --start-ip-address 0.0.0.0 \
    --end-ip-address 0.0.0.0
```

### 3. Create Azure Container Registry

```bash
# Create container registry
az acr create \
    --resource-group isms-production-rg \
    --name ismsregistry \
    --sku Basic \
    --admin-enabled true

# Login to registry
az acr login --name ismsregistry

# Build and push image
docker build -t isms-backend .
docker tag isms-backend ismsregistry.azurecr.io/isms-backend:latest
docker push ismsregistry.azurecr.io/isms-backend:latest
```

### 4. Create Key Vault for Secrets

```bash
# Create Key Vault
az keyvault create \
    --resource-group isms-production-rg \
    --name isms-keyvault \
    --location eastus

# Store secrets
az keyvault secret set \
    --vault-name isms-keyvault \
    --name postgres-password \
    --value "YourSecurePassword123!"

az keyvault secret set \
    --vault-name isms-keyvault \
    --name secret-key \
    --value "your-super-secure-secret-key-min-32-chars"

# Get ACR credentials
ACR_USERNAME=$(az acr credential show --name ismsregistry --query username --output tsv)
ACR_PASSWORD=$(az acr credential show --name ismsregistry --query passwords[0].value --output tsv)

az keyvault secret set \
    --vault-name isms-keyvault \
    --name acr-username \
    --value $ACR_USERNAME

az keyvault secret set \
    --vault-name isms-keyvault \
    --name acr-password \
    --value $ACR_PASSWORD
```

### 5. Create Container Instance

```bash
# Get secrets from Key Vault
POSTGRES_PASSWORD=$(az keyvault secret show --vault-name isms-keyvault --name postgres-password --query value --output tsv)
SECRET_KEY=$(az keyvault secret show --vault-name isms-keyvault --name secret-key --query value --output tsv)
ACR_USERNAME=$(az keyvault secret show --vault-name isms-keyvault --name acr-username --query value --output tsv)
ACR_PASSWORD=$(az keyvault secret show --vault-name isms-keyvault --name acr-password --query value --output tsv)

# Create container instance
az container create \
    --resource-group isms-production-rg \
    --name isms-backend \
    --image ismsregistry.azurecr.io/isms-backend:latest \
    --registry-login-server ismsregistry.azurecr.io \
    --registry-username $ACR_USERNAME \
    --registry-password $ACR_PASSWORD \
    --dns-name-label isms-api \
    --ports 8000 \
    --cpu 1 \
    --memory 2 \
    --environment-variables \
        ENV_MODE=production \
        POSTGRES_SERVER=isms-postgres-server.postgres.database.azure.com \
        POSTGRES_USER=isms_admin \
        POSTGRES_DB=isms_production \
        BACKEND_CORS_ORIGINS='["https://isms-api.eastus.azurecontainer.io"]' \
    --secure-environment-variables \
        POSTGRES_PASSWORD=$POSTGRES_PASSWORD \
        SECRET_KEY=$SECRET_KEY \
    --restart-policy Always
```

## 🚀 Alternative: App Service Deployment

### 1. Create App Service Plan

```bash
# Create App Service plan
az appservice plan create \
    --resource-group isms-production-rg \
    --name isms-app-plan \
    --location eastus \
    --sku B1 \
    --is-linux

# Create Web App
az webapp create \
    --resource-group isms-production-rg \
    --plan isms-app-plan \
    --name isms-backend-app \
    --deployment-container-image-name ismsregistry.azurecr.io/isms-backend:latest
```

### 2. Configure App Service

```bash
# Configure container registry
az webapp config container set \
    --resource-group isms-production-rg \
    --name isms-backend-app \
    --docker-custom-image-name ismsregistry.azurecr.io/isms-backend:latest \
    --docker-registry-server-url https://ismsregistry.azurecr.io \
    --docker-registry-server-user $ACR_USERNAME \
    --docker-registry-server-password $ACR_PASSWORD

# Configure environment variables
az webapp config appsettings set \
    --resource-group isms-production-rg \
    --name isms-backend-app \
    --settings \
        ENV_MODE=production \
        POSTGRES_SERVER=isms-postgres-server.postgres.database.azure.com \
        POSTGRES_USER=isms_admin \
        POSTGRES_DB=isms_production \
        POSTGRES_PASSWORD=$POSTGRES_PASSWORD \
        SECRET_KEY=$SECRET_KEY \
        WEBSITES_PORT=8000
```

## 🔧 Configuration Management

### Environment Variables

| Variable | Value | Source |
|----------|-------|--------|
| `ENV_MODE` | `production` | Direct |
| `POSTGRES_SERVER` | `isms-postgres-server.postgres.database.azure.com` | Direct |
| `POSTGRES_USER` | `isms_admin` | Direct |
| `POSTGRES_DB` | `isms_production` | Direct |
| `POSTGRES_PASSWORD` | `***` | Key Vault |
| `SECRET_KEY` | `***` | Key Vault |

### Key Vault Integration

For App Service, enable managed identity and configure Key Vault references:

```bash
# Enable managed identity
az webapp identity assign \
    --resource-group isms-production-rg \
    --name isms-backend-app

# Get principal ID
PRINCIPAL_ID=$(az webapp identity show --resource-group isms-production-rg --name isms-backend-app --query principalId --output tsv)

# Grant Key Vault access
az keyvault set-policy \
    --name isms-keyvault \
    --object-id $PRINCIPAL_ID \
    --secret-permissions get

# Use Key Vault references in app settings
az webapp config appsettings set \
    --resource-group isms-production-rg \
    --name isms-backend-app \
    --settings \
        POSTGRES_PASSWORD="@Microsoft.KeyVault(VaultName=isms-keyvault;SecretName=postgres-password)" \
        SECRET_KEY="@Microsoft.KeyVault(VaultName=isms-keyvault;SecretName=secret-key)"
```

## 📊 Monitoring and Logging

### Application Insights

```bash
# Create Application Insights
az monitor app-insights component create \
    --resource-group isms-production-rg \
    --app isms-backend-insights \
    --location eastus \
    --kind web

# Get instrumentation key
INSTRUMENTATION_KEY=$(az monitor app-insights component show --resource-group isms-production-rg --app isms-backend-insights --query instrumentationKey --output tsv)

# Configure App Service to use Application Insights
az webapp config appsettings set \
    --resource-group isms-production-rg \
    --name isms-backend-app \
    --settings \
        APPINSIGHTS_INSTRUMENTATIONKEY=$INSTRUMENTATION_KEY \
        APPLICATIONINSIGHTS_CONNECTION_STRING="InstrumentationKey=$INSTRUMENTATION_KEY"
```

### Log Analytics

```bash
# Create Log Analytics workspace
az monitor log-analytics workspace create \
    --resource-group isms-production-rg \
    --workspace-name isms-logs \
    --location eastus

# Configure diagnostic settings for PostgreSQL
az monitor diagnostic-settings create \
    --resource isms-postgres-server \
    --resource-group isms-production-rg \
    --name postgres-diagnostics \
    --workspace isms-logs \
    --logs '[{"category":"PostgreSQLLogs","enabled":true}]' \
    --metrics '[{"category":"AllMetrics","enabled":true}]'
```

## 🔐 Security Configuration

### Network Security

```bash
# Create Virtual Network
az network vnet create \
    --resource-group isms-production-rg \
    --name isms-vnet \
    --address-prefix 10.0.0.0/16 \
    --subnet-name default \
    --subnet-prefix ********/24

# Create Network Security Group
az network nsg create \
    --resource-group isms-production-rg \
    --name isms-nsg

# Allow HTTPS traffic
az network nsg rule create \
    --resource-group isms-production-rg \
    --nsg-name isms-nsg \
    --name AllowHTTPS \
    --priority 100 \
    --source-address-prefixes '*' \
    --source-port-ranges '*' \
    --destination-address-prefixes '*' \
    --destination-port-ranges 443 \
    --access Allow \
    --protocol Tcp
```

### SSL Certificate

```bash
# For custom domain, create managed certificate
az webapp config ssl bind \
    --resource-group isms-production-rg \
    --name isms-backend-app \
    --certificate-thumbprint <thumbprint> \
    --ssl-type SNI
```

## 💰 Cost Optimization

### Pricing Tiers

1. **Container Instances**: Pay-per-second billing
2. **App Service**: Fixed monthly cost with scaling options
3. **PostgreSQL**: Burstable tier for cost optimization
4. **Storage**: Standard storage for logs and backups

### Estimated Monthly Costs

- **Container Instances**: $20-40 (1 vCPU, 2GB RAM)
- **App Service B1**: $55/month
- **PostgreSQL Flexible Server**: $15-25 (Burstable B1ms)
- **Container Registry**: $5/month (Basic)
- **Key Vault**: $3/month
- **Total**: ~$45-130/month

## 🔄 CI/CD Integration

### Azure DevOps Pipeline

Create `azure-pipelines.yml`:

```yaml
trigger:
  branches:
    include:
    - main
    - staging

pool:
  vmImage: 'ubuntu-latest'

variables:
  containerRegistry: 'ismsregistry.azurecr.io'
  imageRepository: 'isms-backend'
  dockerfilePath: '$(Build.SourcesDirectory)/Dockerfile'
  tag: '$(Build.BuildId)'

stages:
- stage: Build
  displayName: Build and push stage
  jobs:
  - job: Build
    displayName: Build
    steps:
    - task: Docker@2
      displayName: Build and push image
      inputs:
        command: buildAndPush
        repository: $(imageRepository)
        dockerfile: $(dockerfilePath)
        containerRegistry: $(dockerRegistryServiceConnection)
        tags: |
          $(tag)
          latest

- stage: Deploy
  displayName: Deploy stage
  dependsOn: Build
  condition: succeeded()
  jobs:
  - deployment: Deploy
    displayName: Deploy
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: AzureContainerInstances@0
            displayName: 'Deploy to Azure Container Instances'
            inputs:
              azureSubscription: $(azureServiceConnection)
              resourceGroupName: 'isms-production-rg'
              location: 'eastus'
              imageSource: 'Registry'
              imageRegistryLoginServer: $(containerRegistry)
              image: '$(containerRegistry)/$(imageRepository):$(tag)'
              containerName: 'isms-backend'
              dnsNameLabel: 'isms-api'
              ports: '8000'
```

### GitHub Actions for Azure

Update `.github/workflows/ci-cd.yml`:

```yaml
deploy-azure-production:
  name: Deploy to Azure Production
  runs-on: ubuntu-latest
  needs: [test, docker-build, security-scan]
  if: github.ref == 'refs/heads/main' && github.event_name == 'push'
  environment: azure-production

  steps:
    - name: Login to Azure
      uses: azure/login@v1
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}

    - name: Login to Azure Container Registry
      uses: azure/docker-login@v1
      with:
        login-server: ismsregistry.azurecr.io
        username: ${{ secrets.ACR_USERNAME }}
        password: ${{ secrets.ACR_PASSWORD }}

    - name: Build and push to ACR
      run: |
        docker build -t isms-backend .
        docker tag isms-backend:latest ismsregistry.azurecr.io/isms-backend:$GITHUB_SHA
        docker push ismsregistry.azurecr.io/isms-backend:$GITHUB_SHA

    - name: Deploy to Container Instances
      uses: azure/aci-deploy@v1
      with:
        resource-group: isms-production-rg
        dns-name-label: isms-api
        image: ismsregistry.azurecr.io/isms-backend:${{ github.sha }}
        name: isms-backend
        location: eastus
```

## 🛠️ Maintenance

### Regular Tasks

1. **Database Maintenance**: Automated backups and updates
2. **Container Updates**: Regular image updates
3. **Security Patches**: Monitor and apply security updates
4. **Cost Monitoring**: Review Azure Cost Management
5. **Performance Monitoring**: Use Application Insights

### Scaling

```bash
# Scale Container Instances (manual)
az container create \
    --resource-group isms-production-rg \
    --name isms-backend-2 \
    --image ismsregistry.azurecr.io/isms-backend:latest \
    --cpu 2 \
    --memory 4

# Scale App Service
az appservice plan update \
    --resource-group isms-production-rg \
    --name isms-app-plan \
    --sku S1

# Enable autoscaling for App Service
az monitor autoscale create \
    --resource-group isms-production-rg \
    --resource isms-backend-app \
    --resource-type Microsoft.Web/sites \
    --name isms-autoscale \
    --min-count 1 \
    --max-count 5 \
    --count 2
```

## 🔍 Troubleshooting

### Common Issues

1. **Container startup failures**: Check logs in Azure portal
2. **Database connection issues**: Verify firewall rules
3. **SSL certificate problems**: Check domain configuration
4. **Performance issues**: Monitor Application Insights

### Debugging Commands

```bash
# View container logs
az container logs \
    --resource-group isms-production-rg \
    --name isms-backend

# Check container status
az container show \
    --resource-group isms-production-rg \
    --name isms-backend \
    --query instanceView.state

# Test database connectivity
az postgres flexible-server connect \
    --name isms-postgres-server \
    --admin-user isms_admin \
    --admin-password "YourSecurePassword123!"
```

---

**Related Guides:**
- [Main Deployment Guide](DEPLOYMENT.md)
- [AWS Deployment Guide](AWS_DEPLOYMENT.md)
- [Docker Guide](DOCKER.md)
