name: ISMS Backend CI/CD Pipeline

on:
  push:
    branches: [ main, staging, develop ]
  pull_request:
    branches: [ main, staging ]

env:
  PYTHON_VERSION: "3.12"
  POETRY_VERSION: "1.7.1"

jobs:
  # Code Quality and Security Checks
  code-quality:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install flake8 black isort bandit safety mypy
          pip install -r requirements.txt

      - name: Code formatting check (Black)
        run: black --check --diff .

      - name: Import sorting check (isort)
        run: isort --check-only --diff .

      - name: Linting (flake8)
        run: flake8 app tests --max-line-length=88 --extend-ignore=E203,W503

      - name: Type checking (mypy)
        run: mypy app --ignore-missing-imports

      - name: Security check (Bandit)
        run: bandit -r app -f json -o bandit-report.json || true

      - name: Dependency vulnerability check (Safety)
        run: safety check --json --output safety-report.json || true

      - name: Upload security reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-reports
          path: |
            bandit-report.json
            safety-report.json

  # Comprehensive Testing Suite
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    needs: code-quality
    
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: test_isms
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    strategy:
      matrix:
        test-type: [unit, integration, api]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest-cov pytest-xdist pytest-mock

      - name: Set up test environment
        run: |
          cp .env.example .env
          echo "ENV_MODE=testing" >> .env
          echo "SQLITE_DATABASE_URI=sqlite+aiosqlite:///:memory:" >> .env
          echo "POSTGRES_SERVER=localhost" >> .env
          echo "POSTGRES_USER=test_user" >> .env
          echo "POSTGRES_PASSWORD=test_password" >> .env
          echo "POSTGRES_DB=test_isms" >> .env
          echo "SECRET_KEY=test_secret_key_for_ci_cd_pipeline" >> .env

      - name: Run ${{ matrix.test-type }} tests
        run: |
          if [ "${{ matrix.test-type }}" = "unit" ]; then
            python run_tests.py --category unit --coverage --verbose
          elif [ "${{ matrix.test-type }}" = "integration" ]; then
            python run_tests.py --category integration --coverage --verbose
          elif [ "${{ matrix.test-type }}" = "api" ]; then
            python run_tests.py --category api --coverage --verbose
          fi

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        if: matrix.test-type == 'unit'
        with:
          file: ./coverage.xml
          flags: unittests
          name: codecov-umbrella

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: test-results-${{ matrix.test-type }}
          path: |
            htmlcov/
            coverage.xml
            pytest-report.xml

  # Build and Test Docker Image
  docker-build:
    name: Docker Build & Test
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/staging'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          target: production
          push: false
          tags: isms-backend:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Test Docker image
        run: |
          docker run --rm -d --name isms-test \
            -e ENV_MODE=testing \
            -e SQLITE_DATABASE_URI=sqlite+aiosqlite:///:memory: \
            -e SECRET_KEY=test_secret_key \
            isms-backend:${{ github.sha }}
          
          # Wait for container to start
          sleep 10
          
          # Test health endpoint
          docker exec isms-test curl -f http://localhost:8000/ || exit 1
          
          # Stop container
          docker stop isms-test

  # Security Scanning
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    needs: docker-build
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/staging'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: isms-backend:${{ github.sha }}
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [test, docker-build, security-scan]
    if: github.ref == 'refs/heads/staging' && github.event_name == 'push'
    environment: staging

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to Dokploy Staging
        run: |
          echo "Deploying to staging environment..."
          # Add your Dokploy deployment commands here
          # This would typically involve:
          # 1. Building and pushing the Docker image to a registry
          # 2. Updating the Dokploy configuration
          # 3. Triggering the deployment

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [test, docker-build, security-scan]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to Dokploy Production
        run: |
          echo "Deploying to production environment..."
          # Add your Dokploy deployment commands here
          # This would typically involve:
          # 1. Building and pushing the Docker image to a registry
          # 2. Updating the Dokploy configuration
          # 3. Triggering the deployment

  # Notification
  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()

    steps:
      - name: Notify on success
        if: needs.deploy-staging.result == 'success' || needs.deploy-production.result == 'success'
        run: |
          echo "Deployment successful!"
          # Add notification logic (Slack, Discord, email, etc.)

      - name: Notify on failure
        if: needs.deploy-staging.result == 'failure' || needs.deploy-production.result == 'failure'
        run: |
          echo "Deployment failed!"
          # Add notification logic for failures
