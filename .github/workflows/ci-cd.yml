name: ISMS CI/CD Pipeline

on:
  push:
    branches: [ main, staging ]
  pull_request:
    branches: [ main, staging ]

env:
  PYTHON_VERSION: '3.12'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Code Quality and Security Checks
  code-quality:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Cache pip dependencies
        uses: actions/cache@v4
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install flake8 black isort bandit safety
          pip install -r requirements.txt

      - name: Code formatting check (Black)
        run: black --check --diff .

      - name: Import sorting check (isort)
        run: isort --check-only --diff .

      - name: Linting (flake8)
        run: flake8 app tests

      - name: Security check (bandit)
        run: bandit -r app/ -f json -o bandit-report.json || true

      - name: Dependency vulnerability check (safety)
        run: safety check --json --output safety-report.json || true

      - name: Upload security reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: security-reports
          path: |
            bandit-report.json
            safety-report.json

  # Comprehensive Testing Suite
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    needs: code-quality
    
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: test_isms
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    strategy:
      matrix:
        test-type: [unit, integration, api]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Cache pip dependencies
        uses: actions/cache@v4
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest-cov pytest-xdist pytest-mock

      - name: Set up test environment
        run: |
          cp .env.example .env
          echo "ENV_MODE=testing" >> .env
          echo "SQLITE_DATABASE_URI=sqlite+aiosqlite:///:memory:" >> .env
          echo "POSTGRES_SERVER=localhost" >> .env
          echo "POSTGRES_USER=test_user" >> .env
          echo "POSTGRES_PASSWORD=test_password" >> .env
          echo "POSTGRES_DB=test_isms" >> .env
          echo "SECRET_KEY=test_secret_key_for_ci_cd_pipeline" >> .env

      - name: Run unit tests
        if: matrix.test-type == 'unit'
        run: |
          python run_tests.py --category unit --coverage --verbose

      - name: Run integration tests
        if: matrix.test-type == 'integration'
        run: |
          python run_tests.py --category integration --coverage --verbose

      - name: Run API tests
        if: matrix.test-type == 'api'
        run: |
          python run_tests.py --category api --coverage --verbose

      - name: Upload coverage reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: coverage-reports-${{ matrix.test-type }}
          path: |
            htmlcov/
            coverage.xml

  # Docker Build and Test
  docker-build:
    name: Docker Build & Test
    runs-on: ubuntu-latest
    needs: code-quality

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build Docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          target: production
          push: false
          tags: isms-backend:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Test Docker image
        run: |
          docker run --rm -d --name isms-test \
            -e ENV_MODE=testing \
            -e SQLITE_DATABASE_URI=sqlite+aiosqlite:///:memory: \
            -e SECRET_KEY=test_secret_key \
            isms-backend:${{ github.sha }}
          
          # Wait for container to start
          sleep 10
          
          # Test health endpoint
          docker exec isms-test curl -f http://localhost:8000/ || exit 1
          
          # Stop container
          docker stop isms-test

  # Security Scanning
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    needs: docker-build

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'isms-backend:${{ github.sha }}'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [test, docker-build, security-scan]
    if: github.ref == 'refs/heads/staging' && github.event_name == 'push'
    environment: staging

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to Dokploy Staging
        run: |
          echo "Deploying to staging environment..."
          curl -X POST "${{ secrets.DOKPLOY_STAGING_WEBHOOK_URL }}" \
            -H "Content-Type: application/json" \
            -d '{"ref": "${{ github.ref }}", "sha": "${{ github.sha }}"}'

      - name: Wait for deployment
        run: sleep 30

      - name: Health check staging
        run: |
          curl -f https://staging-api.isms.helevon.org/ || exit 1

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [test, docker-build, security-scan]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to Dokploy Production
        run: |
          echo "Deploying to production environment..."
          curl -X POST "https://helevon-vps.helevon.org/api/deploy/compose/DmBlOGTL2eE6i2LxjDP5u" \
            -H "Content-Type: application/json" \
            -d '{"ref": "${{ github.ref }}", "sha": "${{ github.sha }}"}'

      - name: Wait for deployment
        run: sleep 60

      - name: Health check production
        run: |
          curl -f https://api.isms.helevon.org/ || exit 1

      - name: Notify deployment success
        if: success()
        run: |
          echo "✅ Production deployment successful!"
          echo "🚀 ISMS API is now live at https://api.isms.helevon.org"

      - name: Notify deployment failure
        if: failure()
        run: |
          echo "❌ Production deployment failed!"
          echo "🔍 Check the logs and rollback if necessary"
