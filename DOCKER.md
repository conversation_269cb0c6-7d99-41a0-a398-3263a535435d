# ISMS Docker Guide

This guide provides comprehensive information about using Docker with the ISMS FastAPI application.

## 🐳 Docker Configuration

### Dockerfile Overview

The ISMS application uses a multi-stage Dockerfile optimized for production:

```dockerfile
# Build stage - Install dependencies
FROM python:3.12-slim as builder
# ... dependency installation

# Production stage - Runtime environment
FROM python:3.12-slim as production
# ... application setup
```

### Key Features

- **Multi-stage build**: Reduces final image size
- **Non-root user**: Enhanced security
- **Health checks**: Container monitoring
- **Optimized layers**: Better caching and faster builds

## 🚀 Quick Start

### Development

```bash
# Build development image
docker build -t isms-backend:dev .

# Run development container
docker run -d \
  --name isms-dev \
  -p 8000:8000 \
  -e ENV_MODE=development \
  -e SQLITE_DATABASE_URI=sqlite+aiosqlite:///./isms.db \
  -v $(pwd):/app \
  isms-backend:dev

# View logs
docker logs -f isms-dev
```

### Production

```bash
# Build production image
docker build --target production -t isms-backend:prod .

# Run production container
docker run -d \
  --name isms-prod \
  -p 8000:8000 \
  -e ENV_MODE=production \
  -e POSTGRES_SERVER=your-db-server \
  -e POSTGRES_USER=your-user \
  -e POSTGRES_PASSWORD=your-password \
  -e POSTGRES_DB=isms_production \
  -e SECRET_KEY=your-secret-key \
  isms-backend:prod
```

## 🔧 Docker Compose

### Development Environment

Create `docker-compose.dev.yml`:

```yaml
version: '3.8'

services:
  app:
    build:
      context: .
      target: production
    ports:
      - "8000:8000"
    environment:
      - ENV_MODE=development
      - SQLITE_DATABASE_URI=sqlite+aiosqlite:///./isms.db
      - SECRET_KEY=dev-secret-key
    volumes:
      - .:/app
      - ./logs:/app/logs
    command: python manage.py runserver --host 0.0.0.0 --port 8000
    restart: unless-stopped

  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_USER=isms_dev
      - POSTGRES_PASSWORD=dev_password
      - POSTGRES_DB=isms_dev
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  postgres_data:
```

### Production Environment (Dokploy)

The main `docker-compose.yml` is optimized for Dokploy:

```yaml
version: '3.8'

services:
  isms-api:
    build:
      context: .
      target: production
    expose:
      - '8000'
    environment:
      - ENV_MODE=production
      # ... other environment variables
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.isms-api.rule=Host(`api.isms.helevon.org`)"
      # ... other Traefik labels
    networks:
      - dokploy-network

networks:
  dokploy-network:
    external: true
```

## 🔍 Container Management

### Building Images

```bash
# Build with specific tag
docker build -t isms-backend:v1.0.0 .

# Build with build arguments
docker build \
  --build-arg PYTHON_VERSION=3.12 \
  -t isms-backend:latest .

# Build for specific platform
docker build --platform linux/amd64 -t isms-backend:amd64 .
```

### Running Containers

```bash
# Run with environment file
docker run --env-file .env.production isms-backend:latest

# Run with volume mounts
docker run \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/data:/app/data \
  isms-backend:latest

# Run with custom command
docker run isms-backend:latest python manage.py init_db

# Run interactively
docker run -it isms-backend:latest /bin/bash
```

### Container Inspection

```bash
# View container logs
docker logs isms-backend

# Follow logs in real-time
docker logs -f isms-backend

# Execute commands in running container
docker exec -it isms-backend python manage.py check_db

# Inspect container configuration
docker inspect isms-backend

# View container resource usage
docker stats isms-backend
```

## 🔧 Environment Configuration

### Environment Variables

| Variable | Development | Production | Description |
|----------|-------------|------------|-------------|
| `ENV_MODE` | `development` | `production` | Application environment |
| `DATABASE_URI` | SQLite | PostgreSQL | Database connection |
| `SECRET_KEY` | Simple | Complex | JWT secret key |
| `LOG_LEVEL` | `DEBUG` | `INFO` | Logging verbosity |
| `WORKERS` | `1` | `4` | Gunicorn workers |

### Development Configuration

```bash
# .env.development
ENV_MODE=development
SQLITE_DATABASE_URI=sqlite+aiosqlite:///./isms.db
SECRET_KEY=dev-secret-key-not-for-production
LOG_LEVEL=DEBUG
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8000"]
```

### Production Configuration

```bash
# .env.production
ENV_MODE=production
POSTGRES_SERVER=your-postgres-server
POSTGRES_USER=your-postgres-user
POSTGRES_PASSWORD=your-secure-password
POSTGRES_DB=isms_production
SECRET_KEY=your-super-secure-secret-key-min-32-chars
LOG_LEVEL=INFO
WORKERS=4
SECURE_HEADERS=true
RATE_LIMIT_ENABLED=true
```

## 🏥 Health Checks

### Container Health Check

The Dockerfile includes a health check:

```dockerfile
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8000/ || exit 1
```

### Manual Health Checks

```bash
# Check container health status
docker inspect --format='{{.State.Health.Status}}' isms-backend

# View health check logs
docker inspect --format='{{range .State.Health.Log}}{{.Output}}{{end}}' isms-backend

# Test health endpoint manually
docker exec isms-backend curl -f http://localhost:8000/health
```

## 📊 Monitoring and Logging

### Log Management

```bash
# Configure log rotation
docker run \
  --log-driver json-file \
  --log-opt max-size=10m \
  --log-opt max-file=3 \
  isms-backend:latest

# Export logs
docker logs isms-backend > isms-app.log 2>&1

# View logs with timestamps
docker logs -t isms-backend
```

### Resource Monitoring

```bash
# Monitor resource usage
docker stats isms-backend

# Set resource limits
docker run \
  --memory=1g \
  --cpus=1.0 \
  --memory-swap=2g \
  isms-backend:latest
```

## 🔐 Security Best Practices

### Image Security

```bash
# Scan image for vulnerabilities
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  aquasec/trivy image isms-backend:latest

# Run as non-root user (already configured in Dockerfile)
docker run --user isms isms-backend:latest

# Use read-only filesystem
docker run --read-only --tmpfs /tmp isms-backend:latest
```

### Runtime Security

```bash
# Drop capabilities
docker run \
  --cap-drop=ALL \
  --cap-add=NET_BIND_SERVICE \
  isms-backend:latest

# Use security profiles
docker run \
  --security-opt seccomp=default.json \
  --security-opt apparmor=docker-default \
  isms-backend:latest
```

## 🚀 Performance Optimization

### Build Optimization

```bash
# Use BuildKit for faster builds
DOCKER_BUILDKIT=1 docker build -t isms-backend:latest .

# Multi-platform builds
docker buildx build \
  --platform linux/amd64,linux/arm64 \
  -t isms-backend:latest .

# Cache optimization
docker build \
  --cache-from isms-backend:latest \
  -t isms-backend:new .
```

### Runtime Optimization

```bash
# Optimize memory usage
docker run \
  --memory=512m \
  --memory-swap=1g \
  --oom-kill-disable=false \
  isms-backend:latest

# CPU optimization
docker run \
  --cpus=1.5 \
  --cpu-shares=1024 \
  isms-backend:latest
```

## 🔄 CI/CD Integration

### GitHub Actions

```yaml
- name: Build Docker image
  run: |
    docker build -t isms-backend:${{ github.sha }} .
    docker tag isms-backend:${{ github.sha }} isms-backend:latest

- name: Test Docker image
  run: |
    docker run --rm -d --name test-container \
      -e ENV_MODE=testing \
      isms-backend:${{ github.sha }}
    sleep 10
    docker exec test-container curl -f http://localhost:8000/health
    docker stop test-container
```

### Registry Operations

```bash
# Tag for registry
docker tag isms-backend:latest registry.example.com/isms-backend:latest

# Push to registry
docker push registry.example.com/isms-backend:latest

# Pull from registry
docker pull registry.example.com/isms-backend:latest
```

## 🛠️ Troubleshooting

### Common Issues

1. **Container won't start**
   ```bash
   # Check logs
   docker logs isms-backend
   
   # Check exit code
   docker ps -a
   ```

2. **Database connection issues**
   ```bash
   # Test database connectivity
   docker exec isms-backend python manage.py check_db
   
   # Check environment variables
   docker exec isms-backend env | grep POSTGRES
   ```

3. **Permission issues**
   ```bash
   # Check file permissions
   docker exec isms-backend ls -la /app
   
   # Fix ownership
   docker exec isms-backend chown -R isms:isms /app
   ```

4. **Memory issues**
   ```bash
   # Check memory usage
   docker stats isms-backend
   
   # Increase memory limit
   docker update --memory=2g isms-backend
   ```

### Debugging Commands

```bash
# Enter container shell
docker exec -it isms-backend /bin/bash

# Run specific commands
docker exec isms-backend python manage.py health_check

# Copy files from container
docker cp isms-backend:/app/logs/isms.log ./local-logs/

# Copy files to container
docker cp ./config.json isms-backend:/app/config.json
```

## 📋 Maintenance

### Regular Tasks

```bash
# Update base image
docker pull python:3.12-slim
docker build --no-cache -t isms-backend:latest .

# Clean up unused images
docker image prune -f

# Clean up unused containers
docker container prune -f

# Clean up unused volumes
docker volume prune -f

# Complete cleanup
docker system prune -a -f
```

### Backup and Restore

```bash
# Export container
docker export isms-backend > isms-backup.tar

# Import container
docker import isms-backup.tar isms-backend:restored

# Save image
docker save isms-backend:latest > isms-image.tar

# Load image
docker load < isms-image.tar
```

---

**Related Guides:**
- [Main Deployment Guide](DEPLOYMENT.md)
- [AWS Deployment Guide](AWS_DEPLOYMENT.md)
- [Azure Deployment Guide](AZURE_DEPLOYMENT.md)
