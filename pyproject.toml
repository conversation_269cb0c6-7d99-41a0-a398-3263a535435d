[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
known_first_party = ["app", "tests"]
known_third_party = ["fastapi", "sqlalchemy", "sqlmodel", "pydantic", "pytest", "httpx", "uvicorn", "typer"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503", "E402", "F811", "F841"]
exclude = [
    ".git",
    "__pycache__",
    "venv",
    ".venv",
    "build",
    "dist",
    "*.egg-info",
]
