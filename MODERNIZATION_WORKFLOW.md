# Code Modernization Workflow

This document outlines the process for modernizing the codebase to address deprecation warnings and follow best practices.

## 1. Addressing Pydantic v2 Deprecation Warnings

Pydantic v2 introduced several changes that require updates to existing code. The most common deprecation warnings are related to class-based config and the `orm_mode` setting.

### Finding Files with Class-Based Config

```bash
grep -r "class Config" --include="*.py" app/
```

### Updating Class-Based Config to ConfigDict

1. Add the ConfigDict import:
   ```python
   from pydantic import ConfigDict
   ```

2. Replace class-based config with ConfigDict:
   ```python
   # Before
   class MyModel(BaseModel):
       # model fields...
       
       class Config:
           orm_mode = True
   
   # After
   class MyModel(BaseModel):
       # model fields...
       
       model_config = ConfigDict(from_attributes=True)
   ```

3. Replace `orm_mode = True` with `from_attributes=True` in the ConfigDict.

### Example Files Updated

- `app/core/settings.py`
- `app/schemas/user.py`
- `app/schemas/inventory.py`
- `app/schemas/sales.py`

## 2. Updating datetime.utcnow() Usage

The `datetime.utcnow()` method is deprecated and scheduled for removal. It should be replaced with timezone-aware objects.

### Finding Files Using datetime.utcnow()

```bash
grep -r "utcnow" --include="*.py" app/
```

### Updating to Timezone-Aware Objects

1. Add the timezone import:
   ```python
   from datetime import datetime, timezone
   ```

2. Replace `datetime.utcnow()` with `datetime.now(timezone.utc)`:
   ```python
   # Before
   created_at = datetime.utcnow()
   
   # After
   created_at = datetime.now(timezone.utc)
   ```

3. For default_factory usage, use a lambda function:
   ```python
   # Before
   created_at: datetime = Field(default_factory=datetime.utcnow)
   
   # After
   created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
   ```

### Example Files Updated

- `app/models/base.py`
- `app/models/inventory.py`
- `app/api/v1/sales.py`

## 3. Additional Improvements

### Updating Deprecated dict() Method

Pydantic v2 deprecated the `dict()` method in favor of `model_dump()`.

```python
# Before
data = model.dict(exclude={"field"})

# After
data = model.model_dump(exclude={"field"})
```

### Example Files Updated

- `app/api/v1/sales.py`

## Testing Changes

After making these changes, run the test suite to ensure everything still works:

```bash
python run.py test -v
```

## Results

- All tests passing
- Reduced warnings from 115 to 1
- Code is now more compatible with Pydantic v2
- Code now uses timezone-aware datetime objects, which is more future-proof

The remaining warning is from a third-party library (pythonjsonlogger) and not from our code.
