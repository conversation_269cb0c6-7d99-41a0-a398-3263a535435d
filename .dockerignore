# Git
.git
.gitignore
.github

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.pytest_cache
htmlcov

# Virtual environments
venv/
env/
ENV/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
*.db
*.sqlite*
logs/*.log
.env.local
.env.development
.env.staging
.env.production

# Documentation
*.md
docs/

# Test files
tests/
docker-compose.test.yml
run_tests.py

# Development files
run.py
run.bat
debug_settings.py

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Build artifacts
build/
dist/
*.egg-info/
